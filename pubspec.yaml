name: alsarea_store
description: "A new Flutter project."
publish_to: "none"

version: 1.0.2+5

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  auto_route: ^9.3.0
  dio: ^5.7.0
  intl: ^0.19.0
  retrofit: ^4.4.2
  json_annotation: ^4.9.0
  pretty_dio_logger: ^1.4.0
  flutter_bloc: ^9.0.0
  freezed_annotation: ^2.4.4
  shared_preferences: ^2.5.2
  flutter_secure_storage: ^9.2.4
  get_it: ^8.0.3
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.17
  pin_code_fields: ^8.0.1
  cached_network_image: ^3.4.1
  flutter_dotenv: ^5.2.1
  image_picker: ^1.1.2
  flutter_rating_bar: ^4.0.1
  dotted_border: ^2.1.0
  carousel_slider: ^5.0.0
  pull_to_refresh_flutter3: ^2.0.2
  url_launcher: ^6.3.1
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  flutter_local_notifications: ^19.2.1
  flutter_widget_from_html_core: ^0.16.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  auto_route_generator: ^9.3.1
  build_runner: ^2.4.15
  retrofit_generator: ^9.1.9
  json_serializable: ^6.9.4
  freezed: ^2.5.8
  flutter_launcher_icons: ^0.14.3
  rename: ^2.1.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: Somar
      fonts:
        - asset: assets/fonts/Somar-Bold.otf
        - asset: assets/fonts/Somar-Light.otf
        - asset: assets/fonts/Somar-ExtraBold.otf
        - asset: assets/fonts/Somar-Regular.otf
        - asset: assets/fonts/Somar-Medium.otf
        - asset: assets/fonts/Somar-SemiBold.otf

flutter_gen:
  integrations:
    flutter_svg: true
    rive: true
    lottie: true
