import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:dio/dio.dart';

class AppHelper {
  static MultipartFile? imageToJson(String? image) =>
      image != null ? MultipartFile.fromFileSync(image) : null;

  static Map<String, int> formDataListToJson(List<int> services) =>
      services.asMap().map((key, value) => MapEntry(key.toString(), value));

  static String getLocale() =>
      SharedPreferencesHelper.get<String?>('locale') ?? 'ar';
}
