extension FormValidator on String {
  bool get isEmail =>
      RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(this);
  bool get isPassword =>
      // RegExp(r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\><*~]).{8,}$')
      // .hasMatch(this);
      RegExp(r'^.{8,}$').hasMatch(this);
  // bool get isPhoneNumber => RegExp(r'^01[0,1,2,5]{1}[0-9]{8}$').hasMatch(this);
  bool get isPhoneNumber => RegExp(r'^.{10,}$').hasMatch(this);
  bool get isName => RegExp(r'^[a-zA-Z]{3,}$').hasMatch(this);
}
