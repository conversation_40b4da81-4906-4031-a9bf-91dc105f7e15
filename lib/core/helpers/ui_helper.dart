import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class UiHelper {
  static String dateFormatting(DateTime date,
      {String format = 'dd MMM yyyy', String locale = 'ar'}) {
    return DateFormat(format, locale).format(date);
  }

  static Future<T?> showCustomDialog<T>({
    required BuildContext context,
    required Widget dialog,
    bool barrierDismissible = true,
    Color? barrierColor,
  }) {
    return showDialog<T>(
      context: context,
      builder: (_) => dialog,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
    );
  }

  static Future<T?> showCustomBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      builder: (_) => child,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
    );
  }

  static void showCustomSnackBar({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    Duration? duration,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        content: Text(
          message,
          style: TextStyle(color: textColor),
        ),
        backgroundColor: backgroundColor ?? Colors.red,
        duration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  static Future<void> onLoading(BuildContext context) {
    return showDialog(
      context: context,
      builder: (_) => const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: CupertinoActivityIndicator(),
          ),
        ),
      ),
    );
  }

  static void onFailure(BuildContext context, String message) {
    Navigator.pop(context);
    showCustomSnackBar(
      context: context,
      message: message,
      backgroundColor: Colors.red,
    );
  }

  static void onSuccess(BuildContext context) {
    Navigator.pop(context);
  }

  static Future<T?> showCustomDialogV1<T>({
    required BuildContext context,
    required void Function()? onYesPressed,
    void Function()? onNoPressed,
    required String title,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      builder: (_) => AlertDialog(
        actionsPadding:
            const EdgeInsets.only(bottom: 35, right: 35, left: 35, top: 10),
        title: Text(
          title,
          textAlign: TextAlign.center,
          style: context.textTheme.titleLarge!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  onPressed: onYesPressed,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    context.l10n.yes,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: CustomElevatedButton(
                  onPressed: onNoPressed ??
                      () {
                        context.router.maybePop();
                      },
                  backgroundColor: AppColors.lightGrey,
                  child: Text(
                    context.l10n.cancel,
                    style: const TextStyle(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
      barrierDismissible: barrierDismissible,
    );
  }
}
