import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'page_view_state.dart';
part 'page_view_cubit.freezed.dart';

class PageViewCubit extends Cubit<PageViewState> {
  PageViewCubit() : super(const PageViewState.initial());

  late final PageController pageViewController;

  void initPageController() => pageViewController = PageController();
  int index = 0;

  void nextPage() {
    pageViewController.nextPage(
      duration: const Duration(milliseconds: 150),
      curve: Curves.linear,
    );
    index++;
    emit(PageViewState.changed(pageViewController.page!.toInt() + 1));
  }

  void previousPage() {
    pageViewController.previousPage(
      duration: const Duration(milliseconds: 150),
      curve: Curves.linear,
    );
    index--;
    emit(PageViewState.changed(pageViewController.page!.toInt() - 1));
  }

  void jumpToPage(int page) {
    pageViewController.jumpToPage(page);
    emit(PageViewState.changed(page));
  }

  @override
  Future<void> close() {
    pageViewController.dispose();
    return super.close();
  }
}
