import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextStyles {
  static TextStyle get label11 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0.5,
      );

  static TextStyle get label12 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 15.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0.5,
      );

  static TextStyle get label14 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 19.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0.1,
      );

  static TextStyle get body12 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 15.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0.4,
      );

  static TextStyle get body14 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 17.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0.25,
      );

  static TextStyle get body16 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 19.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0.15,
      );

  static TextStyle get title14 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 17.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0.1,
      );

  static TextStyle get title16 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 19.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0.15,
      );
  static TextStyle get title20 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 23.sp,
        fontWeight: FontWeight.w700,
        wordSpacing: 0,
      );

  static TextStyle get title22 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 25.sp,
        fontWeight: FontWeight.w500,
        wordSpacing: 0,
      );

  static TextStyle get headline24 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 29.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );

  static TextStyle get headline28 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 31.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );

  static TextStyle get headline32 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 35.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );

  static TextStyle get display36 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 39.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );

  static TextStyle get display45 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 48.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );

  static TextStyle get display57 => TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 60.sp,
        fontWeight: FontWeight.w400,
        wordSpacing: 0,
      );
}
