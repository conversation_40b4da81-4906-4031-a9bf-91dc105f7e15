import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';

ThemeData appTheme(Locale locale) {
  return ThemeData(
    fontFamily: FontFamily.somar,
    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,
      onPrimary: Colors.white,
      outline: AppColors.lightGrey,
      outlineVariant: AppColors.lightGrey,
      error: AppColors.red,
      surface: Colors.white,
      onSurface: AppColors.black,
    ),
    scaffoldBackgroundColor: Colors.white,
    dividerTheme: const DividerThemeData(
      color: AppColors.lightGrey,
    ),
    appBarTheme: _appBarTheme(),
    elevatedButtonTheme: _elevatedButtonTheme(),
    textButtonTheme: _textButtonTheme(),
    inputDecorationTheme: _inputDecorationTheme(),
    expansionTileTheme: _expansionTileTheme(),
  );
}

ExpansionTileThemeData _expansionTileTheme() {
  return ExpansionTileThemeData(
    iconColor: AppColors.primary,
    collapsedIconColor: AppColors.primary,
    backgroundColor: AppColors.lighterGrey,
    collapsedBackgroundColor: Colors.white,
    textColor: AppColors.black,
    collapsedTextColor: AppColors.black,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
      side: const BorderSide(
        color: AppColors.lightGrey,
        width: 1,
      ),
    ),
    collapsedShape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
      side: const BorderSide(
        color: AppColors.lightGrey,
        width: 1,
      ),
    ),
  );
}

AppBarTheme _appBarTheme() {
  return AppBarTheme(
    backgroundColor: Colors.transparent,
    surfaceTintColor: Colors.transparent,
    foregroundColor: Colors.transparent,
    shadowColor: Colors.transparent,
    centerTitle: false,
    elevation: 0,
    scrolledUnderElevation: 0,
    titleTextStyle: TextStyle(
      fontFamily: FontFamily.somar,
      fontSize: 22.sp,
      fontWeight: FontWeight.w700,
      color: AppColors.primary,
    ),
    iconTheme: const IconThemeData(
      color: AppColors.primary,
    ),
    systemOverlayStyle: const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );
}

ElevatedButtonThemeData _elevatedButtonTheme() {
  return ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
    ),
  );
}

TextButtonThemeData _textButtonTheme() {
  return TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.black,
      visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
      padding: const EdgeInsets.all(0),
      splashFactory: NoSplash.splashFactory,
      overlayColor: Colors.transparent,
      textStyle: TextStyle(
        fontFamily: FontFamily.somar,
        fontSize: 18.sp,
        fontWeight: FontWeight.w400,
      ),
    ),
  );
}

InputDecorationTheme _inputDecorationTheme() {
  return InputDecorationTheme(
    border: const OutlineInputBorder(
      borderRadius: BorderRadius.all(
        Radius.circular(8),
      ),
      borderSide: BorderSide(
        width: 0.5,
        color: AppColors.lighterGrey,
      ),
    ),
    enabledBorder: const OutlineInputBorder(
      borderRadius: BorderRadius.all(
        Radius.circular(8),
      ),
      borderSide: BorderSide(
        width: 1,
        color: Color(0xffEAEAEA),
      ),
    ),
    prefixIconConstraints: const BoxConstraints(
      minWidth: 60,
    ),
    isDense: true,
    contentPadding: const EdgeInsets.symmetric(
      vertical: 12,
      horizontal: 16,
    ),
    hintStyle: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: AppColors.grey,
    ),
    labelStyle: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: AppColors.grey,
    ),
    floatingLabelStyle: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
    ),
  );
}
