import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../error/error_handler.dart';

part 'localization_state.dart';
part 'localization_cubit.freezed.dart';

class LocalizationCubit extends Cubit<LocalizationState> {
  LocalizationCubit() : super(const LocalizationState.initial());

  void getLanguageCode() {
    final response = errorHandler<String>(
      () => SharedPreferencesHelper.containsKey('locale')
          ? SharedPreferencesHelper.get<String>('locale')
          : 'ar',
    );

    response.when(
      success: (languageCode) {
        emit(LocalizationChanged(Locale(languageCode)));
      },
      failure: (error) {
        emit(
          const LocalizationError('Something went wrong! Please try again.'),
        );
      },
    );
  }

  Future<void> _saveLanguageCode(String languageCode) async {
    final response = await errorHandlerAsync<bool>(
      () async =>
          await SharedPreferencesHelper.set<String>('locale', languageCode),
    );

    response.when(
      success: (_) {
        emit(LocalizationChanged(Locale(languageCode)));
      },
      failure: (error) {
        emit(
          const LocalizationError('Something went wrong! Please try again.'),
        );
      },
    );
  }

  void toArabic() {
    _saveLanguageCode('ar');
  }

  void toEnglish() {
    _saveLanguageCode('en');
  }
}
