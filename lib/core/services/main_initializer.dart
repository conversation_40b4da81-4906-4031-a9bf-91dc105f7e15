import 'package:alsarea_store/core/services/notifications/notifications.dart';
import 'package:alsarea_store/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'bloc_observer.dart';
import 'injection_container.dart';

class MainInitializer {
  static Future<void> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    Bloc.observer = MyBlocObserver();
    await Future.wait([
      ScreenUtil.ensureScreenSize(),
      // dotenv.load(fileName: ".env"),
      initInjector(),
      Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform)
          .then((_) async {
        await NotificationService.instance.initialize();
      }),
    ]);
  }
}
