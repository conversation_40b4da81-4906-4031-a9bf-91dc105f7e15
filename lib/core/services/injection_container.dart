import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/auth/data/api_service/auth_api_service.dart';
import 'package:alsarea_store/src/auth/data/repo/auth_repo.dart';
import 'package:alsarea_store/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/cubit/bottom_nav_bar_cubit.dart';
import 'package:alsarea_store/src/complaints/data/api_service/settings_api_service.dart';
import 'package:alsarea_store/src/complaints/data/repo/settings_repo.dart';
import 'package:alsarea_store/src/complaints/presentation/cubit/settings_cubit.dart';
import 'package:alsarea_store/src/orders/data/api_service/orders_api_service.dart';
import 'package:alsarea_store/src/orders/data/repo/orders_repo.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/products/data/api_service/category_api_service.dart';
import 'package:alsarea_store/src/products/data/repo/category_repo.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/sales_report/data/api_service/reports_api_service.dart';
import 'package:alsarea_store/src/sales_report/data/repo/reports_repo.dart';
import 'package:alsarea_store/src/sales_report/presentation/cubit/reports_cubit.dart';
import 'package:alsarea_store/src/splash/presentation/cubit/splash_cubit.dart';
import 'package:alsarea_store/src/terms/data/terms_api_service/terms_api_service.dart';
import 'package:alsarea_store/src/terms/data/terms_repo/terms_repo.dart';
import 'package:alsarea_store/src/terms/presentation/screens/cubit/terms_cubit.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/router/app_router.dart';
// import '../api/api_service.dart';
import '../api/dio_factory.dart';
import 'localization/localization_cubit.dart';

final injector = GetIt.instance;

Future<void> initInjector() async {
  // Splash
  injector.registerLazySingleton(() => SplashCubit());

  // Auth
  injector.registerLazySingleton(
      () => AuthApiService(injector(), baseUrl: EndPoints.baseUrl));
  injector.registerLazySingleton(() => AuthRepo(injector()));
  injector.registerLazySingleton(() => AuthCubit(injector()));

  // BottomNavBar
  injector.registerLazySingleton(() => BottomNavBarCubit());

  // categories
  injector.registerLazySingleton(
      () => CategoryApiService(injector(), baseUrl: EndPoints.baseUrl));
  injector.registerLazySingleton(() => CategoryRepo(injector()));
  injector.registerLazySingleton(() => CategoryCubit(injector()));

  // settings
  injector.registerLazySingleton(
      () => SettingsApiService(injector(), baseUrl: EndPoints.settingsBaseUrl));
  injector.registerLazySingleton(() => SettingsRepo(injector()));
  injector.registerLazySingleton(() => SettingsCubit(injector()));

  // orders
  injector.registerLazySingleton(
      () => OrdersApiService(injector(), baseUrl: EndPoints.baseUrl));
  injector.registerLazySingleton(() => OrdersRepo(injector()));
  injector.registerLazySingleton(() => OrdersCubit(injector()));

  // reports
  injector.registerLazySingleton(
      () => ReportsApiService(injector(), baseUrl: EndPoints.baseUrl));
  injector.registerLazySingleton(() => ReportsRepo(injector()));
  injector.registerLazySingleton(() => ReportsCubit(injector()));

  // terms
  injector.registerLazySingleton(
      () => TermsApiService(injector(), baseUrl: EndPoints.settingsBaseUrl));
  injector.registerLazySingleton(() => TermsRepo(injector()));
  injector.registerLazySingleton(() => TermsCubit(injector()));

  // Router
  injector.registerLazySingleton(() => AppRouter());

  // SharedPreferences
  final sp = await SharedPreferences.getInstance();
  injector.registerLazySingleton<SharedPreferences>(() => sp);

  // Localization
  injector.registerLazySingleton(() => LocalizationCubit());

  // Dio & ApiService
  Dio dio = DioFactory.instance;
  injector.registerLazySingleton<Dio>(() => dio);
  // injector.registerLazySingleton<ApiService>(() => ApiService(injector()));
}
