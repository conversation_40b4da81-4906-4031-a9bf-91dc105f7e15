// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PagesModel _$PagesModelFromJson(Map<String, dynamic> json) {
  return _PagesModel.fromJson(json);
}

/// @nodoc
mixin _$PagesModel {
  @JsonKey(name: "totalPages")
  int? get totalPages => throw _privateConstructorUsedError;
  @JsonKey(name: "currentPage")
  int? get currentPage => throw _privateConstructorUsedError;
  @JsonKey(name: "nextSkip")
  int? get nextSkip => throw _privateConstructorUsedError;
  @JsonKey(name: "take")
  int? get take => throw _privateConstructorUsedError;

  /// Serializes this PagesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PagesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PagesModelCopyWith<PagesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PagesModelCopyWith<$Res> {
  factory $PagesModelCopyWith(
          PagesModel value, $Res Function(PagesModel) then) =
      _$PagesModelCopyWithImpl<$Res, PagesModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "totalPages") int? totalPages,
      @JsonKey(name: "currentPage") int? currentPage,
      @JsonKey(name: "nextSkip") int? nextSkip,
      @JsonKey(name: "take") int? take});
}

/// @nodoc
class _$PagesModelCopyWithImpl<$Res, $Val extends PagesModel>
    implements $PagesModelCopyWith<$Res> {
  _$PagesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PagesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPages = freezed,
    Object? currentPage = freezed,
    Object? nextSkip = freezed,
    Object? take = freezed,
  }) {
    return _then(_value.copyWith(
      totalPages: freezed == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      currentPage: freezed == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
      nextSkip: freezed == nextSkip
          ? _value.nextSkip
          : nextSkip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PagesModelImplCopyWith<$Res>
    implements $PagesModelCopyWith<$Res> {
  factory _$$PagesModelImplCopyWith(
          _$PagesModelImpl value, $Res Function(_$PagesModelImpl) then) =
      __$$PagesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "totalPages") int? totalPages,
      @JsonKey(name: "currentPage") int? currentPage,
      @JsonKey(name: "nextSkip") int? nextSkip,
      @JsonKey(name: "take") int? take});
}

/// @nodoc
class __$$PagesModelImplCopyWithImpl<$Res>
    extends _$PagesModelCopyWithImpl<$Res, _$PagesModelImpl>
    implements _$$PagesModelImplCopyWith<$Res> {
  __$$PagesModelImplCopyWithImpl(
      _$PagesModelImpl _value, $Res Function(_$PagesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PagesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPages = freezed,
    Object? currentPage = freezed,
    Object? nextSkip = freezed,
    Object? take = freezed,
  }) {
    return _then(_$PagesModelImpl(
      totalPages: freezed == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      currentPage: freezed == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int?,
      nextSkip: freezed == nextSkip
          ? _value.nextSkip
          : nextSkip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PagesModelImpl implements _PagesModel {
  const _$PagesModelImpl(
      {@JsonKey(name: "totalPages") this.totalPages,
      @JsonKey(name: "currentPage") this.currentPage,
      @JsonKey(name: "nextSkip") this.nextSkip,
      @JsonKey(name: "take") this.take});

  factory _$PagesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PagesModelImplFromJson(json);

  @override
  @JsonKey(name: "totalPages")
  final int? totalPages;
  @override
  @JsonKey(name: "currentPage")
  final int? currentPage;
  @override
  @JsonKey(name: "nextSkip")
  final int? nextSkip;
  @override
  @JsonKey(name: "take")
  final int? take;

  @override
  String toString() {
    return 'PagesModel(totalPages: $totalPages, currentPage: $currentPage, nextSkip: $nextSkip, take: $take)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PagesModelImpl &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.nextSkip, nextSkip) ||
                other.nextSkip == nextSkip) &&
            (identical(other.take, take) || other.take == take));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, totalPages, currentPage, nextSkip, take);

  /// Create a copy of PagesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PagesModelImplCopyWith<_$PagesModelImpl> get copyWith =>
      __$$PagesModelImplCopyWithImpl<_$PagesModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PagesModelImplToJson(
      this,
    );
  }
}

abstract class _PagesModel implements PagesModel {
  const factory _PagesModel(
      {@JsonKey(name: "totalPages") final int? totalPages,
      @JsonKey(name: "currentPage") final int? currentPage,
      @JsonKey(name: "nextSkip") final int? nextSkip,
      @JsonKey(name: "take") final int? take}) = _$PagesModelImpl;

  factory _PagesModel.fromJson(Map<String, dynamic> json) =
      _$PagesModelImpl.fromJson;

  @override
  @JsonKey(name: "totalPages")
  int? get totalPages;
  @override
  @JsonKey(name: "currentPage")
  int? get currentPage;
  @override
  @JsonKey(name: "nextSkip")
  int? get nextSkip;
  @override
  @JsonKey(name: "take")
  int? get take;

  /// Create a copy of PagesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PagesModelImplCopyWith<_$PagesModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
