import 'package:alsarea_store/core/helpers/app_helper.dart';

class EndPoints {
  static String locale = AppHelper.getLocale();
  static String baseUrl =
      'http://api.3lasara.com/$locale/api/storeadministrations';
  static String settingsBaseUrl = 'http://api.3lasara.com/$locale/api/settings';
  // Splash
  static const String splash = '/splash';

  // Auth
  static const login = '/login';
  static const String verify = '/confirm-user';
  static const String resendCode = '/resend-confirmation-code';
  static const String completeProfile = '/complete';
  static const String getProfile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String appActivities = '/appactivities';
  static const String cities = '/cities';
  static const String storeOrdersCount = '/store-orders-count';

  // Categories
  static const String categories = '/categories';
  static const String categoryDetails = '/category-details';
  static const String createCategory = '/create-category';
  static const String editCategory = '/edit-category';
  static const String deleteCategory = '/delete-category';
  static const String createSubcategory = '/create-subcategory';
  static const String editSubcategory = '/edit-subcategory';
  static const String deleteSubcategory = '/delete-subcategory';
  static const String subcategoriesList = '/subcategories-list';
  static const String trademarksList = '/trademarks-list';
  static const String additionList = '/addition-list';
  static const String createAddition = '/create-addition';
  static const String editAddition = '/edit-addition';
  static const String deleteAddition = '/delete-addition';
  static const String createProduct = '/create-product';
  static const String editProduct = '/edit-product';
  static const String createProductOption = '/create-product-option';
  static const String createProductAddition = '/create-product-addition';
  static const String editProductAddition = '/edit-product-addition';
  static const String products = '/products';
  static const String singleProduct = '/single-product';
  static const String getGroups = '/get-groups';
  static const String createGroup = '/create-group';
  static const String editGroup = '/edit-group';
  static const String deleteGroup = '/delete-group';
  static const String createOption = '/create-option';
  static const String editOption = '/edit-option';
  static const String deleteOption = '/delete-option';

  // orders
  static const String getCurrentOrders = '/get-orders';
  static const String getLastOrders = '/get-last-orders';
  static const String getOrderDetails = '/single-order';
  static const String getSpecialOrderDetails = '/single-special-order';
  static const String convertStatusOrders = '/convert-status-orders';

  // reports
  static const String getReports = '/reports-orders';

  // settings
  static const String contactSettings = '/contact-settings';
  static const String createComplaint = '/contact-us';
  static const String terms = '/terms-and-conditions';
}
