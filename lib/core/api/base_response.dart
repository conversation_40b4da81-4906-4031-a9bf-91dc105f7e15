import 'package:freezed_annotation/freezed_annotation.dart';

part 'base_response.freezed.dart';
part 'base_response.g.dart';

@JsonSerializable()
class BaseResponse {
  BaseResponse({
    this.status,
    this.statusName,
    this.message,
    this.debug,
    this.pages,
    this.errors,
  });

  final int? status;
  final String? statusName;
  final dynamic message;
  final PagesModel? pages;
  final dynamic debug;
  final dynamic errors;

  factory BaseResponse.fromJson(Map<String, dynamic> json) =>
      _$BaseResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BaseResponseToJson(this);
}

@freezed
class PagesModel with _$PagesModel {
  const factory PagesModel({
    @J<PERSON><PERSON><PERSON>(name: "totalPages") int? totalPages,
    @JsonKey(name: "currentPage") int? currentPage,
    @J<PERSON><PERSON>ey(name: "nextSkip") int? nextSkip,
    @Json<PERSON><PERSON>(name: "take") int? take,
  }) = _PagesModel;

  factory PagesModel.fromJson(Map<String, dynamic> json) =>
      _$Pages<PERSON>(json);
}
