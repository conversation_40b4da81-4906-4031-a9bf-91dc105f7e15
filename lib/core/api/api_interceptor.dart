import 'package:dio/dio.dart';

class AppInterceptors extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _handleResponseError(response);
    super.onResponse(response, handler);
  }

  static void _handleResponseError(Response response) {
    try {
      if (response.data['status'] >= 300) {
        throw DioException(
          requestOptions: response.requestOptions,
          type: DioExceptionType.unknown,
          message: response.data['errors'] == null
              ? response.data['message']
              : response.data['errors'].values.first[0],
        );
      }
    } on DioException catch (_) {
      rethrow;
    } catch (e) {
      throw DioException(
        requestOptions: response.requestOptions,
        type: DioExceptionType.unknown,
        message: 'Something went wrong, Please try again later...',
      );
    }
  }
}
