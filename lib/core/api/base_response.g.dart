// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResponse _$BaseResponseFromJson(Map<String, dynamic> json) => BaseResponse(
      status: (json['status'] as num?)?.toInt(),
      statusName: json['statusName'] as String?,
      message: json['message'],
      debug: json['debug'],
      pages: json['pages'] == null
          ? null
          : PagesModel.fromJson(json['pages'] as Map<String, dynamic>),
      errors: json['errors'],
    );

Map<String, dynamic> _$BaseResponseToJson(BaseResponse instance) =>
    <String, dynamic>{
      if (instance.status case final value?) 'status': value,
      if (instance.statusName case final value?) 'statusName': value,
      if (instance.message case final value?) 'message': value,
      if (instance.pages case final value?) 'pages': value,
      if (instance.debug case final value?) 'debug': value,
      if (instance.errors case final value?) 'errors': value,
    };

_$PagesModelImpl _$$PagesModelImplFromJson(Map<String, dynamic> json) =>
    _$PagesModelImpl(
      totalPages: (json['totalPages'] as num?)?.toInt(),
      currentPage: (json['currentPage'] as num?)?.toInt(),
      nextSkip: (json['nextSkip'] as num?)?.toInt(),
      take: (json['take'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$PagesModelImplToJson(_$PagesModelImpl instance) =>
    <String, dynamic>{
      if (instance.totalPages case final value?) 'totalPages': value,
      if (instance.currentPage case final value?) 'currentPage': value,
      if (instance.nextSkip case final value?) 'nextSkip': value,
      if (instance.take case final value?) 'take': value,
    };
