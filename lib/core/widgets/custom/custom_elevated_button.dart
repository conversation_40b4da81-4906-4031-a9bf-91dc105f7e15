import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

class CustomElevatedButton extends StatelessWidget {
  final bool isOutline;
  const CustomElevatedButton({
    super.key,
    this.child,
    this.onPressed,
    this.width = double.infinity,
    this.height = 45,
    this.backgroundColor = AppColors.primary,
    this.borderColor = Colors.transparent,
    this.foregroundColor = Colors.white,
    this.textStyle,
    this.isDisabled = false,
    this.elevation = 0,
  }) : isOutline = false;

  const CustomElevatedButton.outline({
    super.key,
    this.child,
    this.onPressed,
    this.width = double.infinity,
    this.height = 45,
    this.backgroundColor = Colors.white,
    this.borderColor = AppColors.lightGrey,
    this.foregroundColor = AppColors.primary,
    this.textStyle,
    this.isDisabled = false,
    this.elevation = 0,
  }) : isOutline = true;

  final Widget? child;
  final void Function()? onPressed;
  final double width;
  final double height;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final TextStyle? textStyle;
  final bool isDisabled;
  final double elevation;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
            minimumSize: WidgetStatePropertyAll(Size(width, height)),
            backgroundColor: !isDisabled
                ? WidgetStatePropertyAll(backgroundColor)
                : WidgetStatePropertyAll(
                    backgroundColor.withAlpha(125),
                  ),
            foregroundColor: !isDisabled
                ? WidgetStatePropertyAll(foregroundColor)
                : const WidgetStatePropertyAll(
                    AppColors.grey,
                  ),
            side: WidgetStatePropertyAll(
              BorderSide(
                color: borderColor,
              ),
            ),
            elevation: WidgetStatePropertyAll(elevation),
          ),
      onPressed: onPressed,
      child: child,
    );
  }
}
