import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class CustomNetworkImage extends StatelessWidget {
  const CustomNetworkImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.fit,
    this.borderRadius,
  });

  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final BorderRadiusGeometry? borderRadius;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: CachedNetworkImage(
        imageUrl: imageUrl.isEmpty ||
                imageUrl == 'http://3lsare3.flyfox-eg.com/images/Allimages/'
            ? 'https://cdn-icons-png.flaticon.com/128/12891/12891131.png'
            : imageUrl,
        placeholder: (_, __) => const Center(
          child: CircularProgressIndicator.adaptive(),
        ),
        errorWidget: (_, __, ___) => const Icon(Icons.error),
        width: width ?? double.infinity,
        height: height,
        fit: fit ?? BoxFit.cover,
      ),
    );
  }
}

ImageProvider<Object> customNetworkImageProvider(String imageUrl) {
  return CachedNetworkImageProvider(
    imageUrl,
  );
}
// https://picsum.photos/200/300
// https://via.placeholder.com/150
