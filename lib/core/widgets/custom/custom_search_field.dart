import 'dart:async';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:flutter/material.dart';

class CustomSearchField extends StatefulWidget {
  const CustomSearchField(
      {super.key, this.hintText, this.onChanged, this.controller});
  final TextEditingController? controller;
  final String? hintText;
  final void Function(String? value)? onChanged;

  @override
  State<CustomSearchField> createState() => _CustomSearchFieldState();
}

class _CustomSearchFieldState extends State<CustomSearchField> {
  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      cursorHeight: 15,
      decoration: InputDecoration(
        hintText: widget.hintText,
        prefixIcon: const Icon(
          Icons.search,
          size: 24,
          color: Color(0xff6A6E70),
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: AppColors.black,
            width: 1,
          ),
        ),
      ),
      style:
          context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
      onChanged: (value) {
        if (_debounce?.isActive ?? false) {
          _debounce?.cancel();
        }
        _debounce = Timer(
          const Duration(milliseconds: 500, seconds: 1),
          () => widget.onChanged?.call(value),
        );
      },
    );
  }
}
