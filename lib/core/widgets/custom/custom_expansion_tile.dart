import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';

class CustomExpansionTile extends StatelessWidget {
  const CustomExpansionTile({
    super.key,
    required this.title,
    required this.children,
    this.leading,
    this.enabled,
    this.initiallyExpanded = false,
  });

  final Widget? leading;
  final String title;
  final bool initiallyExpanded;
  final bool? enabled;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          enabled: enabled ?? true,
          visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
          initiallyExpanded: initiallyExpanded,
          maintainState: true,
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          leading: leading,
          title: Text(
            title,
            style: TextStyles.body16,
          ),
          children: children,
        ),
      ),
    );
  }
}
