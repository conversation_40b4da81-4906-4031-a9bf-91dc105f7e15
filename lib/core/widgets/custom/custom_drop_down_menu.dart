import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDropDownButton<T> extends StatelessWidget {
  const CustomDropDownButton({
    super.key,
    required this.hintText,
    this.value,
    this.prefixIcon,
    this.items = const [],
    this.filled = false,
    this.fillColor = AppColors.lighterGrey,
    this.selectedItemBuilder,
    this.onSaved,
    this.onChanged,
    this.onValidate,
  });
  final String hintText;
  final T? value;
  final Widget? prefixIcon;
  final List<DropdownMenuItem<T>> items;
  final bool filled;
  final Color? fillColor;
  final List<Widget> Function(BuildContext? context)? selectedItemBuilder;
  final void Function(T? value)? onSaved;
  final void Function(T? value)? onChanged;
  final String? Function(T? value)? onValidate;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      decoration: InputDecoration(
        isDense: true,
        prefixIcon: prefixIcon,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: filled,
        fillColor: fillColor,
      ),
      style: TextStyles.body14.copyWith(
        fontFamily: FontFamily.somar,
        fontWeight: FontWeight.w400,
        color: AppColors.black,
      ),
      selectedItemBuilder: selectedItemBuilder,
      hint: Text(hintText),
      menuMaxHeight: 250.h,
      items: items,
      value: value,
      onChanged: onChanged ?? (value) {},
      onSaved: onSaved,
      validator: onValidate,
      isDense: true,
    );
  }
}
