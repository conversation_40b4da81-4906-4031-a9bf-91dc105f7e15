import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class CustomRefreshWidget extends StatefulWidget {
  const CustomRefreshWidget({
    super.key,
    required this.child,
    this.onRefresh,
    this.onLoading,
    this.footer,
    this.enablePullDown = true,
    this.enablePullUp = true,
  });
  final Future<void> Function()? onRefresh;
  final Future<void> Function()? onLoading;
  final Widget child;
  final Widget? footer;
  final bool enablePullDown;
  final bool enablePullUp;

  @override
  State<CustomRefreshWidget> createState() => _CustomRefreshWidgetState();
}

class _CustomRefreshWidgetState extends State<CustomRefreshWidget> {
  late final RefreshController refreshController;

  @override
  void initState() {
    refreshController = RefreshController();
    super.initState();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: refreshController,
      enablePullDown: widget.enablePullDown,
      enablePullUp: widget.enablePullUp,
      footer: CustomFooter(
        builder: (BuildContext context, LoadStatus? mode) {
          Widget? body;
          if (mode == LoadStatus.loading) {
            body = const Padding(
              padding: EdgeInsets.only(bottom: 25, top: 15),
              child: CircularProgressIndicator.adaptive(),
            );
          } else if (mode == LoadStatus.failed) {
            body = Text(context.l10n.tryAgain);
          } else {
            body = Text(
              context.l10n.noMoreToLoad,
            );
          }
          return Center(child: body);
        },
      ),
      onRefresh: () async {
        await widget.onRefresh?.call();
        refreshController.refreshCompleted();
      },
      onLoading: () async {
        await widget.onLoading?.call();
        refreshController.loadComplete();
      },
      header: MaterialClassicHeader(
        color: Theme.of(context).colorScheme.primary,
      ),
      physics: const BouncingScrollPhysics(),
      child: widget.child,
    );
  }
}
