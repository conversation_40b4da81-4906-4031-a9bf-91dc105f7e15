import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';

class LabeledField extends StatelessWidget {
  const LabeledField(
      {super.key,
      required this.label,
      required this.field,
      this.isRequired = false});

  final String label;
  final Widget field;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          spacing: 4,
          children: [
            Text(
              label,
              style: TextStyles.body16,
            ),
            if (isRequired)
              Text(
                '*',
                style: TextStyles.title14.copyWith(color: Colors.red),
              ),
          ],
        ),
        const SizedBox(height: 8),
        field,
      ],
    );
  }
}
