import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';

class DefaultRatingBar extends StatelessWidget {
  const DefaultRatingBar({
    super.key,
    this.rate = 4,
    this.itemSize = 18,
    this.itemCount = 5,
    this.onRatingUpdate,
  }) : isIndicator = false;

  const DefaultRatingBar.indicator({
    super.key,
    required this.rate,
    this.itemSize = 18,
    this.itemCount = 5,
    this.onRatingUpdate,
  }) : isIndicator = true;

  final double itemSize;
  final int itemCount;
  final bool isIndicator;
  final double rate;
  final void Function(double rating)? onRatingUpdate;

  @override
  Widget build(BuildContext context) {
    return isIndicator
        ? RatingBarIndicator(
            rating: rate,
            direction: Axis.horizontal,
            itemCount: itemCount,
            itemSize: itemSize,
            unratedColor: AppColors.lightGrey,
            itemBuilder: (context, _) => const Icon(
              Icons.star_rounded,
              color: Colors.amber,
            ),
          )
        : RatingBar(
            initialRating: rate,
            direction: Axis.horizontal,
            itemCount: itemCount,
            unratedColor: AppColors.lightGrey,
            itemSize: itemSize,
            ratingWidget: RatingWidget(
              full: const Icon(Icons.star_rate_rounded, color: Colors.amber),
              half: const Icon(Icons.star_half_rounded, color: Colors.amber),
              empty: const Icon(Icons.star_border_purple500_rounded,
                  color: Colors.amber),
            ),
            glow: false,
            tapOnlyMode: true,
            onRatingUpdate: (rating) => onRatingUpdate?.call(rating),
          );
  }
}
