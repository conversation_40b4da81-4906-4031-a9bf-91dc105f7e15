import 'dart:io';

import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class UploadImageWidget extends StatefulWidget {
  const UploadImageWidget({
    super.key,
    this.label,
    this.onUploadImage,
    this.smallIcon = false,
    this.isMultiSelect = false,
  });

  final String? label;
  final Function? onUploadImage;
  final bool smallIcon;
  final bool isMultiSelect;

  @override
  State<UploadImageWidget> createState() => _UploadImageWidgetState();
}

class _UploadImageWidgetState extends State<UploadImageWidget> {
  XFile? _file;
  List<XFile?> _files = [];
  @override
  Widget build(BuildContext context) {
    return AddCardWidget(
      padding: 4,
      onTap: _file == null
          ? () async {
              if (widget.isMultiSelect) {
                final files = await ImagePicker().pickMultiImage(
                  imageQuality: 50,
                  maxHeight: 500,
                  maxWidth: 500,
                );
                if (files.isEmpty) return;
                for (var file in files) {
                  setState(() {
                    _files = [..._files, file];
                  });
                  widget.onUploadImage?.call(file);
                }
              } else {
                final file = await ImagePicker().pickImage(
                  source: ImageSource.gallery,
                  imageQuality: 50,
                  maxHeight: 500,
                  maxWidth: 500,
                );
                if (file == null) return;
                setState(() {
                  _file = file;
                });
                widget.onUploadImage!(file);
              }
            }
          : null,
      child: widget.isMultiSelect
          ? _files.isEmpty
              ? Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    spacing: 8,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Assets.icons.uploadImage.svg(),
                      ),
                      if (widget.label != null) Text(widget.label!),
                    ],
                  ),
                )
              : Column(
                  children: _files.asMap().entries.map((entry) {
                    final index = entry.key;
                    final file = entry.value;

                    return ImageViewerWidget(
                      smallIcon: widget.smallIcon,
                      file: file,
                      onPressed: () {
                        setState(() {
                          _files[index] = null;
                        });
                        widget.onUploadImage?.call(null);
                      },
                    );
                  }).toList(),
                )
          : _file == null
              ? Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    spacing: 8,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Assets.icons.uploadImage.svg(),
                      ),
                      if (widget.label != null)
                        Text(
                          widget.label!,
                          style: TextStyles.body14,
                        ),
                    ],
                  ),
                )
              : ImageViewerWidget(
                  smallIcon: widget.smallIcon,
                  file: _file,
                  onPressed: () {
                    setState(() {
                      _file = null;
                    });
                    widget.onUploadImage!(null);
                  },
                ),
    );
  }
}

class ImageViewerWidget extends StatelessWidget {
  const ImageViewerWidget({
    super.key,
    this.file,
    this.onPressed,
    this.smallIcon = false,
  });

  final XFile? file;
  final VoidCallback? onPressed;
  final bool smallIcon;

  @override
  Widget build(BuildContext context) {
    if (file == null) {
      return const SizedBox(); // Return an empty widget if file is null
    }

    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            File(file!.path),
            fit: BoxFit.contain,
            alignment: Alignment.center,
          ),
        ),
        Positioned(
          top: 2,
          right: 2,
          child: IconButton.filled(
            iconSize: smallIcon ? 14 : 20,
            padding: EdgeInsets.zero,
            visualDensity: smallIcon
                ? const VisualDensity(horizontal: -4, vertical: -4)
                : VisualDensity.compact,
            icon: const Icon(Icons.close),
            onPressed: onPressed,
          ),
        ),
      ],
    );
  }
}
