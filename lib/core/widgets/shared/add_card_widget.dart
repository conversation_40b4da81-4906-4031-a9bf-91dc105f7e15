import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/dashed_container.dart';
import 'package:flutter/material.dart';

class AddCardWidget extends StatelessWidget {
  const AddCardWidget({
    super.key,
    this.onTap,
    this.child,
    this.title,
    this.padding,
  });
  final VoidCallback? onTap;
  final Widget? child;
  final String? title;
  final double? padding;

  @override
  Widget build(BuildContext context) {
    return DashedContainer(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(padding ?? 16),
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
          ),
          child: child ??
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.add_circle_outlined,
                      color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(title ?? '', style: TextStyles.title14),
                ],
              ),
        ),
      ),
    );
  }
}
