import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

class DashedContainer extends StatelessWidget {
  const DashedContainer(
      {super.key, this.borderRadius, this.color, required this.child});

  final BorderRadiusGeometry? borderRadius;
  final Color? color;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: DottedBorder(
        strokeWidth: 4,
        dashPattern: const [8, 8],
        color: color ?? Colors.grey.shade300,
        child: child,
      ),
    );
  }
}
