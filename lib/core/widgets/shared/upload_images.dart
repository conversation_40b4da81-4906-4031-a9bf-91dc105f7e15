import 'dart:io';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class UploadImagesWidget extends StatefulWidget {
  const UploadImagesWidget({
    super.key,
    this.label,
    this.image,
    this.onUploadImage,
    this.onClear,
    this.smallIcon = false,
    this.isMultiSelect = false,
  });

  final String? label;
  final String? image;
  final Function(XFile image)? onUploadImage;
  final VoidCallback? onClear;
  final bool smallIcon;
  final bool isMultiSelect;

  @override
  State<UploadImagesWidget> createState() => _UploadImagesWidgetState();
}

class _UploadImagesWidgetState extends State<UploadImagesWidget> {
  XFile? _file;
  @override
  Widget build(BuildContext context) {
    return AddCardWidget(
      padding: 4,
      onTap: _file == null
          ? () async {
              if (widget.isMultiSelect) {
                final files = await ImagePicker().pickMultiImage(
                  imageQuality: 50,
                  maxHeight: 500,
                  maxWidth: 500,
                );
                if (files.isEmpty) return;
                for (var file in files) {
                  widget.onUploadImage?.call(file);
                }
              } else {
                final source = await UiHelper.showCustomDialog(
                    context: context, dialog: const ShowCameraSourceDialog());
                if (source == null) return;
                final file = await ImagePicker().pickImage(
                  source: source,
                  imageQuality: 50,
                  maxHeight: 500,
                  maxWidth: 500,
                );
                if (file == null) return;
                widget.onUploadImage?.call(file);
                setState(() {
                  _file = file;
                });
              }
            }
          : null,
      child: _file == null && widget.image == null
          ? Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                spacing: 8,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Assets.icons.uploadImage.svg(),
                  ),
                  if (widget.label != null) Text(widget.label!),
                ],
              ),
            )
          : ImageViewerWidget(
              smallIcon: widget.smallIcon,
              file: _file,
              image: widget.image,
              onPressed: () {
                setState(() {
                  _file = null;
                });
                widget.onClear?.call();
              },
            ),
    );
  }
}

class ImageViewerWidget extends StatelessWidget {
  const ImageViewerWidget({
    super.key,
    this.file,
    this.image,
    this.onPressed,
    this.smallIcon = false,
  });

  final XFile? file;
  final String? image;
  final VoidCallback? onPressed;
  final bool smallIcon;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (image != null && file == null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CustomNetworkImage(image!),
          ),
        if (file != null) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(file!.path),
              fit: BoxFit.cover,
              alignment: Alignment.center,
            ),
          ),
          Positioned(
            top: 2,
            right: 2,
            child: IconButton.filled(
              iconSize: smallIcon ? 14 : 20,
              padding: EdgeInsets.zero,
              visualDensity: smallIcon
                  ? const VisualDensity(horizontal: -4, vertical: -4)
                  : VisualDensity.compact,
              icon: const Icon(Icons.close),
              onPressed: onPressed,
            ),
          ),
        ],
      ],
    );
  }
}

class ShowCameraSourceDialog extends StatelessWidget {
  const ShowCameraSourceDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog.adaptive(
      title: Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Text(
          context.l10n.selectImageOrLogo,
          style: TextStyles.title16,
        ),
      ),
      content: Row(
        children: [
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: IconButton(
                  onPressed: () => Navigator.pop(context, ImageSource.camera),
                  icon: const Icon(
                    Icons.camera_alt_outlined,
                    color: AppColors.primary,
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: IconButton(
                  onPressed: () => Navigator.pop(context, ImageSource.gallery),
                  icon: const Icon(
                    Icons.photo_library_outlined,
                    color: AppColors.primary,
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
