import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

class DefaultAppBar extends AppBar {
  DefaultAppBar(
    BuildContext context, {
    super.key,
    super.title,
    super.bottom,
    super.backgroundColor,
    super.actions,
  }) : super(
          elevation: 8,
          leading: context.router.canPop()
              ? IconButton(
                  visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                  padding: EdgeInsets.zero,
                  icon: const Icon(
                    Icons.arrow_back_ios_new_rounded,
                    size: 18,
                  ),
                  onPressed: () {
                    context.router.maybePop();
                  },
                )
              : null,
          titleSpacing:
              context.router.canPop() ? 0 : NavigationToolbar.kMiddleSpacing,
        );
}
