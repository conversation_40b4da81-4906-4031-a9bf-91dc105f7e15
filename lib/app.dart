import 'package:alsarea_store/src/bottom_nav_bar/presentation/cubit/bottom_nav_bar_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'config/router/app_router.dart';
import 'core/services/injection_container.dart';
import 'core/services/localization/localization_cubit.dart';
import 'core/theme/app_theme.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) =>
                injector<LocalizationCubit>()..getLanguageCode(),
          ),
          BlocProvider(
            create: (context) => injector<BottomNavBarCubit>(),
          ),
        ],
        child: BlocBuilder<LocalizationCubit, LocalizationState>(
          builder: (context, state) {
            final locale = state.maybeWhen(
              changed: (locale) => locale,
              orElse: () => const Locale('ar'),
            );
            ScreenUtil.init(context);
            return MaterialApp.router(
              title: 'alsarea_delivery',
              debugShowCheckedModeBanner: false,
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              locale: locale,
              theme: appTheme(locale),
              routerConfig: injector<AppRouter>().config(),
            );
          },
        ),
      ),
    );
  }
}
