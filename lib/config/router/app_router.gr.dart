// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AddCategoryScreen]
class AddCategoryRoute extends PageRouteInfo<AddCategoryRouteArgs> {
  AddCategoryRoute({
    Key? key,
    CategoryModel? category,
    List<PageRouteInfo>? children,
  }) : super(
         AddCategoryRoute.name,
         args: AddCategoryRouteArgs(key: key, category: category),
         initialChildren: children,
       );

  static const String name = 'AddCategoryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddCategoryRouteArgs>(
        orElse: () => const AddCategoryRouteArgs(),
      );
      return WrappedRoute(
        child: AddCategoryScreen(key: args.key, category: args.category),
      );
    },
  );
}

class AddCategoryRouteArgs {
  const AddCategoryRouteArgs({this.key, this.category});

  final Key? key;

  final CategoryModel? category;

  @override
  String toString() {
    return 'AddCategoryRouteArgs{key: $key, category: $category}';
  }
}

/// generated route for
/// [AddProductScreen]
class AddProductRoute extends PageRouteInfo<AddProductRouteArgs> {
  AddProductRoute({
    Key? key,
    required int subcategoryId,
    List<PageRouteInfo>? children,
  }) : super(
         AddProductRoute.name,
         args: AddProductRouteArgs(key: key, subcategoryId: subcategoryId),
         initialChildren: children,
       );

  static const String name = 'AddProductRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddProductRouteArgs>();
      return WrappedRoute(
        child: AddProductScreen(
          key: args.key,
          subcategoryId: args.subcategoryId,
        ),
      );
    },
  );
}

class AddProductRouteArgs {
  const AddProductRouteArgs({this.key, required this.subcategoryId});

  final Key? key;

  final int subcategoryId;

  @override
  String toString() {
    return 'AddProductRouteArgs{key: $key, subcategoryId: $subcategoryId}';
  }
}

/// generated route for
/// [AdditionsScreen]
class AdditionsRoute extends PageRouteInfo<void> {
  const AdditionsRoute({List<PageRouteInfo>? children})
    : super(AdditionsRoute.name, initialChildren: children);

  static const String name = 'AdditionsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const AdditionsScreen());
    },
  );
}

/// generated route for
/// [BottomNavBarScreen]
class BottomNavBarRoute extends PageRouteInfo<void> {
  const BottomNavBarRoute({List<PageRouteInfo>? children})
    : super(BottomNavBarRoute.name, initialChildren: children);

  static const String name = 'BottomNavBarRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const BottomNavBarScreen();
    },
  );
}

/// generated route for
/// [ChoiceDetailsScreen]
class ChoiceDetailsRoute extends PageRouteInfo<ChoiceDetailsRouteArgs> {
  ChoiceDetailsRoute({
    Key? key,
    required GroupModel group,
    List<PageRouteInfo>? children,
  }) : super(
         ChoiceDetailsRoute.name,
         args: ChoiceDetailsRouteArgs(key: key, group: group),
         initialChildren: children,
       );

  static const String name = 'ChoiceDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ChoiceDetailsRouteArgs>();
      return WrappedRoute(
        child: ChoiceDetailsScreen(key: args.key, group: args.group),
      );
    },
  );
}

class ChoiceDetailsRouteArgs {
  const ChoiceDetailsRouteArgs({this.key, required this.group});

  final Key? key;

  final GroupModel group;

  @override
  String toString() {
    return 'ChoiceDetailsRouteArgs{key: $key, group: $group}';
  }
}

/// generated route for
/// [ChoicesScreen]
class ChoicesRoute extends PageRouteInfo<void> {
  const ChoicesRoute({List<PageRouteInfo>? children})
    : super(ChoicesRoute.name, initialChildren: children);

  static const String name = 'ChoicesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const ChoicesScreen());
    },
  );
}

/// generated route for
/// [ComplaintsScreen]
class ComplaintsRoute extends PageRouteInfo<void> {
  const ComplaintsRoute({List<PageRouteInfo>? children})
    : super(ComplaintsRoute.name, initialChildren: children);

  static const String name = 'ComplaintsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const ComplaintsScreen());
    },
  );
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
    : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const LoginScreen());
    },
  );
}

/// generated route for
/// [OrderDetailsScreen]
class OrderDetailsRoute extends PageRouteInfo<OrderDetailsRouteArgs> {
  OrderDetailsRoute({
    Key? key,
    required bool isSpecialOrder,
    required int orderId,
    List<PageRouteInfo>? children,
  }) : super(
         OrderDetailsRoute.name,
         args: OrderDetailsRouteArgs(
           key: key,
           isSpecialOrder: isSpecialOrder,
           orderId: orderId,
         ),
         initialChildren: children,
       );

  static const String name = 'OrderDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OrderDetailsRouteArgs>();
      return WrappedRoute(
        child: OrderDetailsScreen(
          key: args.key,
          isSpecialOrder: args.isSpecialOrder,
          orderId: args.orderId,
        ),
      );
    },
  );
}

class OrderDetailsRouteArgs {
  const OrderDetailsRouteArgs({
    this.key,
    required this.isSpecialOrder,
    required this.orderId,
  });

  final Key? key;

  final bool isSpecialOrder;

  final int orderId;

  @override
  String toString() {
    return 'OrderDetailsRouteArgs{key: $key, isSpecialOrder: $isSpecialOrder, orderId: $orderId}';
  }
}

/// generated route for
/// [OtpScreen]
class OtpRoute extends PageRouteInfo<void> {
  const OtpRoute({List<PageRouteInfo>? children})
    : super(OtpRoute.name, initialChildren: children);

  static const String name = 'OtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const OtpScreen());
    },
  );
}

/// generated route for
/// [ProductChoicesScreen]
class ProductChoicesRoute extends PageRouteInfo<void> {
  const ProductChoicesRoute({List<PageRouteInfo>? children})
    : super(ProductChoicesRoute.name, initialChildren: children);

  static const String name = 'ProductChoicesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ProductChoicesScreen();
    },
  );
}

/// generated route for
/// [ProductInfoScreen]
class ProductInfoRoute extends PageRouteInfo<ProductInfoRouteArgs> {
  ProductInfoRoute({
    Key? key,
    required ProductModel product,
    List<PageRouteInfo>? children,
  }) : super(
         ProductInfoRoute.name,
         args: ProductInfoRouteArgs(key: key, product: product),
         initialChildren: children,
       );

  static const String name = 'ProductInfoRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProductInfoRouteArgs>();
      return WrappedRoute(
        child: ProductInfoScreen(key: args.key, product: args.product),
      );
    },
  );
}

class ProductInfoRouteArgs {
  const ProductInfoRouteArgs({this.key, required this.product});

  final Key? key;

  final ProductModel product;

  @override
  String toString() {
    return 'ProductInfoRouteArgs{key: $key, product: $product}';
  }
}

/// generated route for
/// [ProductsScreen]
class ProductsRoute extends PageRouteInfo<ProductsRouteArgs> {
  ProductsRoute({
    Key? key,
    CategoryModel? category,
    int? subcategoryId,
    List<PageRouteInfo>? children,
  }) : super(
         ProductsRoute.name,
         args: ProductsRouteArgs(
           key: key,
           category: category,
           subcategoryId: subcategoryId,
         ),
         initialChildren: children,
       );

  static const String name = 'ProductsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProductsRouteArgs>(
        orElse: () => const ProductsRouteArgs(),
      );
      return WrappedRoute(
        child: ProductsScreen(
          key: args.key,
          category: args.category,
          subcategoryId: args.subcategoryId,
        ),
      );
    },
  );
}

class ProductsRouteArgs {
  const ProductsRouteArgs({this.key, this.category, this.subcategoryId});

  final Key? key;

  final CategoryModel? category;

  final int? subcategoryId;

  @override
  String toString() {
    return 'ProductsRouteArgs{key: $key, category: $category, subcategoryId: $subcategoryId}';
  }
}

/// generated route for
/// [SalesReportScreen]
class SalesReportRoute extends PageRouteInfo<void> {
  const SalesReportRoute({List<PageRouteInfo>? children})
    : super(SalesReportRoute.name, initialChildren: children);

  static const String name = 'SalesReportRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const SalesReportScreen());
    },
  );
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
    : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const SplashScreen());
    },
  );
}

/// generated route for
/// [StoreDataScreen]
class StoreDataRoute extends PageRouteInfo<void> {
  const StoreDataRoute({List<PageRouteInfo>? children})
    : super(StoreDataRoute.name, initialChildren: children);

  static const String name = 'StoreDataRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const StoreDataScreen());
    },
  );
}

/// generated route for
/// [TermsScreen]
class TermsRoute extends PageRouteInfo<void> {
  const TermsRoute({List<PageRouteInfo>? children})
    : super(TermsRoute.name, initialChildren: children);

  static const String name = 'TermsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const TermsScreen());
    },
  );
}
