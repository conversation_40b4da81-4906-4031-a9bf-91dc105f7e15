import 'package:alsarea_store/src/auth/presentation/screens/login_screen.dart';
import 'package:alsarea_store/src/auth/presentation/screens/otp_screen.dart';
import 'package:alsarea_store/src/auth/presentation/screens/store_data_screen.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/screens/bottom_nav_bar_screen.dart';
import 'package:alsarea_store/src/complaints/presentation/screens/complaints_screen.dart';
import 'package:alsarea_store/src/orders/presentation/screens/order_details_screen.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/presentation/screens/add_category_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/add_product_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/additions_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/choice_details_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/choices_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/product_choices_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/product_info_screen.dart';
import 'package:alsarea_store/src/products/presentation/screens/products_screen.dart';
import 'package:alsarea_store/src/sales_report/presentation/screens/sales_report_screen.dart';
import 'package:alsarea_store/src/splash/presentation/screens/splash_screen.dart';
import 'package:alsarea_store/src/terms/presentation/screens/terms_screen.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';

part 'app_router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
        ),
        AutoRoute(page: LoginRoute.page),
        AutoRoute(page: OtpRoute.page),
        AutoRoute(page: StoreDataRoute.page),
        AutoRoute(page: BottomNavBarRoute.page),
        AutoRoute(page: AddCategoryRoute.page),
        AutoRoute(page: ProductsRoute.page),
        AutoRoute(page: AddProductRoute.page),
        AutoRoute(page: ProductChoicesRoute.page),
        AutoRoute(page: ProductInfoRoute.page),
        AutoRoute(page: OrderDetailsRoute.page),
        AutoRoute(page: SalesReportRoute.page),
        AutoRoute(page: ComplaintsRoute.page),
        AutoRoute(page: TermsRoute.page),
        AutoRoute(page: ChoicesRoute.page),
        AutoRoute(page: AdditionsRoute.page),
        AutoRoute(page: ChoiceDetailsRoute.page),
      ];
}
