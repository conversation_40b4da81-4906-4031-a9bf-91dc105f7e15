import 'package:alsarea_store/src/orders/data/models/get_last_orders_model/get_last_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_model.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_model.dart';
import 'package:alsarea_store/src/orders/data/repo/orders_repo.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_operations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'orders_cubit.freezed.dart';
part 'orders_state.dart';

class OrdersCubit extends Cubit<OrdersState> {
  final OrdersRepo _ordersRepo;
  OrdersCubit(this._ordersRepo) : super(const OrdersState.initial());

  final operations = OrdersOperations();

  Future<void> getOrders({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(const OrdersState.getOrdersLoading());
      operations.currentOrders.clear();
      operations.currentPage = 1;
    }
    if (operations.currentPage > operations.totalPages) return;
    operations.skip = (operations.currentPage - 1) * 10;
    operations.dataWrapper.take = 10;
    operations.dataWrapper.skip = operations.skip;
    final body = GetOrdersParams.fromJson(operations.dataWrapper.toJson());
    final result = await _ordersRepo.getOrders(body);
    result.when(
        success: (result) => {
              operations.currentOrders.addAll(result.data ?? []),
              operations.totalPages = result.pages?.totalPages ?? 1,
              operations.currentPage++,
              emit(OrdersState.getOrdersSuccess(operations.currentOrders)),
            },
        failure: (message) => emit(OrdersState.getOrdersFailure(message)));
  }

  Future<void> getLastOrders({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(const OrdersState.getLastOrdersLoading());
      operations.lastOrders.clear();
      operations.currentPage = 1;
    }
    if (operations.currentPage > operations.totalPages) return;
    operations.skip = (operations.currentPage - 1) * 10;
    operations.dataWrapper.take = 10;
    operations.dataWrapper.skip = operations.skip;
    final body = GetLastOrdersParams.fromJson(operations.dataWrapper.toJson());
    final result = await _ordersRepo.getLastOrders(body);
    result.when(
        success: (result) => {
              operations.lastOrders.addAll(result.data ?? []),
              operations.totalPages = result.pages?.totalPages ?? 1,
              operations.currentPage++,
              emit(OrdersState.getLastOrdersSuccess(operations.lastOrders)),
            },
        failure: (message) => emit(OrdersState.getLastOrdersFailure(message)));
  }

  Future<void> getOrderDetails(int orderId) async {
    emit(const OrdersState.getOrderDetailsLoading());
    final result = await _ordersRepo.getOrderDetails(orderId);
    result.when(
        success: (result) => emit(
              OrdersState.getOrderDetailsSuccess(
                result.data ?? const OrderDetailsModel(),
              ),
            ),
        failure: (message) =>
            emit(OrdersState.getOrderDetailsFailure(message)));
  }

  Future<void> getSpecialOrderDetails(int orderId) async {
    emit(const OrdersState.getOrderDetailsLoading());
    final result = await _ordersRepo.getSpecialOrderDetails(orderId);
    result.when(
        success: (result) => emit(
              OrdersState.getSpecialOrderDetailsSuccess(
                result.data ?? const SpecialOrderDetailsModel(),
              ),
            ),
        failure: (message) =>
            emit(OrdersState.getOrderDetailsFailure(message)));
  }

  Future<void> convertStatusOrder(
      int orderId, int statusId, String? reason) async {
    emit(const OrdersState.convertStatusOrderLoading());
    final result =
        await _ordersRepo.convertStatusOrder(orderId, statusId, reason);
    result.when(
        success: (result) =>
            emit(const OrdersState.convertStatusOrderSuccess()),
        failure: (message) =>
            emit(OrdersState.convertStatusOrderFailure(message)));
  }
}
