part of 'orders_cubit.dart';

@freezed
class OrdersState with _$OrdersState {
  const factory OrdersState.initial() = _Initial;
  const factory OrdersState.getOrdersLoading() = _GetOrdersLoading;
  const factory OrdersState.getOrdersSuccess(List<GetOrdersModel> orders) =
      _GetOrdersSuccess;
  const factory OrdersState.getOrdersFailure(String error) = _GetOrdersFailure;

  const factory OrdersState.getLastOrdersLoading() = _GetLastOrdersLoading;
  const factory OrdersState.getLastOrdersSuccess(List<GetOrdersModel> orders) =
      _GetLastOrdersSuccess;
  const factory OrdersState.getLastOrdersFailure(String error) =
      _GetLastOrdersFailure;

  const factory OrdersState.getOrderDetailsLoading() = _GetOrderDetailsLoading;
  const factory OrdersState.getOrderDetailsSuccess(OrderDetailsModel order) =
      _GetOrderDetailsSuccess;
  const factory OrdersState.getSpecialOrderDetailsSuccess(
      SpecialOrderDetailsModel specialOrder) = _GetSpecialOrderDetailsSuccess;
  const factory OrdersState.getOrderDetailsFailure(String error) =
      _GetOrderDetailsFailure;

  const factory OrdersState.convertStatusOrderLoading() =
      _ConvertStatusOrderLoading;
  const factory OrdersState.convertStatusOrderSuccess() =
      _ConvertStatusOrderSuccess;
  const factory OrdersState.convertStatusOrderFailure(String error) =
      _ConvertStatusOrderFailure;
}
