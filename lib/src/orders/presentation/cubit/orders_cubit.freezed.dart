// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'orders_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrdersState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrdersStateCopyWith<$Res> {
  factory $OrdersStateCopyWith(
          OrdersState value, $Res Function(OrdersState) then) =
      _$OrdersStateCopyWithImpl<$Res, OrdersState>;
}

/// @nodoc
class _$OrdersStateCopyWithImpl<$Res, $Val extends OrdersState>
    implements $OrdersStateCopyWith<$Res> {
  _$OrdersStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'OrdersState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements OrdersState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$GetOrdersLoadingImplCopyWith<$Res> {
  factory _$$GetOrdersLoadingImplCopyWith(_$GetOrdersLoadingImpl value,
          $Res Function(_$GetOrdersLoadingImpl) then) =
      __$$GetOrdersLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetOrdersLoadingImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrdersLoadingImpl>
    implements _$$GetOrdersLoadingImplCopyWith<$Res> {
  __$$GetOrdersLoadingImplCopyWithImpl(_$GetOrdersLoadingImpl _value,
      $Res Function(_$GetOrdersLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetOrdersLoadingImpl implements _GetOrdersLoading {
  const _$GetOrdersLoadingImpl();

  @override
  String toString() {
    return 'OrdersState.getOrdersLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetOrdersLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrdersLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrdersLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersLoading != null) {
      return getOrdersLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrdersLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrdersLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersLoading != null) {
      return getOrdersLoading(this);
    }
    return orElse();
  }
}

abstract class _GetOrdersLoading implements OrdersState {
  const factory _GetOrdersLoading() = _$GetOrdersLoadingImpl;
}

/// @nodoc
abstract class _$$GetOrdersSuccessImplCopyWith<$Res> {
  factory _$$GetOrdersSuccessImplCopyWith(_$GetOrdersSuccessImpl value,
          $Res Function(_$GetOrdersSuccessImpl) then) =
      __$$GetOrdersSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<GetOrdersModel> orders});
}

/// @nodoc
class __$$GetOrdersSuccessImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrdersSuccessImpl>
    implements _$$GetOrdersSuccessImplCopyWith<$Res> {
  __$$GetOrdersSuccessImplCopyWithImpl(_$GetOrdersSuccessImpl _value,
      $Res Function(_$GetOrdersSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
  }) {
    return _then(_$GetOrdersSuccessImpl(
      null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<GetOrdersModel>,
    ));
  }
}

/// @nodoc

class _$GetOrdersSuccessImpl implements _GetOrdersSuccess {
  const _$GetOrdersSuccessImpl(final List<GetOrdersModel> orders)
      : _orders = orders;

  final List<GetOrdersModel> _orders;
  @override
  List<GetOrdersModel> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  String toString() {
    return 'OrdersState.getOrdersSuccess(orders: $orders)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrdersSuccessImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_orders));

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrdersSuccessImplCopyWith<_$GetOrdersSuccessImpl> get copyWith =>
      __$$GetOrdersSuccessImplCopyWithImpl<_$GetOrdersSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrdersSuccess(orders);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrdersSuccess?.call(orders);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersSuccess != null) {
      return getOrdersSuccess(orders);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrdersSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrdersSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersSuccess != null) {
      return getOrdersSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetOrdersSuccess implements OrdersState {
  const factory _GetOrdersSuccess(final List<GetOrdersModel> orders) =
      _$GetOrdersSuccessImpl;

  List<GetOrdersModel> get orders;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrdersSuccessImplCopyWith<_$GetOrdersSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetOrdersFailureImplCopyWith<$Res> {
  factory _$$GetOrdersFailureImplCopyWith(_$GetOrdersFailureImpl value,
          $Res Function(_$GetOrdersFailureImpl) then) =
      __$$GetOrdersFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$GetOrdersFailureImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrdersFailureImpl>
    implements _$$GetOrdersFailureImplCopyWith<$Res> {
  __$$GetOrdersFailureImplCopyWithImpl(_$GetOrdersFailureImpl _value,
      $Res Function(_$GetOrdersFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetOrdersFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetOrdersFailureImpl implements _GetOrdersFailure {
  const _$GetOrdersFailureImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'OrdersState.getOrdersFailure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrdersFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrdersFailureImplCopyWith<_$GetOrdersFailureImpl> get copyWith =>
      __$$GetOrdersFailureImplCopyWithImpl<_$GetOrdersFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrdersFailure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrdersFailure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersFailure != null) {
      return getOrdersFailure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrdersFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrdersFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrdersFailure != null) {
      return getOrdersFailure(this);
    }
    return orElse();
  }
}

abstract class _GetOrdersFailure implements OrdersState {
  const factory _GetOrdersFailure(final String error) = _$GetOrdersFailureImpl;

  String get error;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrdersFailureImplCopyWith<_$GetOrdersFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetLastOrdersLoadingImplCopyWith<$Res> {
  factory _$$GetLastOrdersLoadingImplCopyWith(_$GetLastOrdersLoadingImpl value,
          $Res Function(_$GetLastOrdersLoadingImpl) then) =
      __$$GetLastOrdersLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetLastOrdersLoadingImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetLastOrdersLoadingImpl>
    implements _$$GetLastOrdersLoadingImplCopyWith<$Res> {
  __$$GetLastOrdersLoadingImplCopyWithImpl(_$GetLastOrdersLoadingImpl _value,
      $Res Function(_$GetLastOrdersLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetLastOrdersLoadingImpl implements _GetLastOrdersLoading {
  const _$GetLastOrdersLoadingImpl();

  @override
  String toString() {
    return 'OrdersState.getLastOrdersLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLastOrdersLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getLastOrdersLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getLastOrdersLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersLoading != null) {
      return getLastOrdersLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getLastOrdersLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getLastOrdersLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersLoading != null) {
      return getLastOrdersLoading(this);
    }
    return orElse();
  }
}

abstract class _GetLastOrdersLoading implements OrdersState {
  const factory _GetLastOrdersLoading() = _$GetLastOrdersLoadingImpl;
}

/// @nodoc
abstract class _$$GetLastOrdersSuccessImplCopyWith<$Res> {
  factory _$$GetLastOrdersSuccessImplCopyWith(_$GetLastOrdersSuccessImpl value,
          $Res Function(_$GetLastOrdersSuccessImpl) then) =
      __$$GetLastOrdersSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<GetOrdersModel> orders});
}

/// @nodoc
class __$$GetLastOrdersSuccessImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetLastOrdersSuccessImpl>
    implements _$$GetLastOrdersSuccessImplCopyWith<$Res> {
  __$$GetLastOrdersSuccessImplCopyWithImpl(_$GetLastOrdersSuccessImpl _value,
      $Res Function(_$GetLastOrdersSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
  }) {
    return _then(_$GetLastOrdersSuccessImpl(
      null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<GetOrdersModel>,
    ));
  }
}

/// @nodoc

class _$GetLastOrdersSuccessImpl implements _GetLastOrdersSuccess {
  const _$GetLastOrdersSuccessImpl(final List<GetOrdersModel> orders)
      : _orders = orders;

  final List<GetOrdersModel> _orders;
  @override
  List<GetOrdersModel> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  String toString() {
    return 'OrdersState.getLastOrdersSuccess(orders: $orders)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLastOrdersSuccessImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_orders));

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetLastOrdersSuccessImplCopyWith<_$GetLastOrdersSuccessImpl>
      get copyWith =>
          __$$GetLastOrdersSuccessImplCopyWithImpl<_$GetLastOrdersSuccessImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getLastOrdersSuccess(orders);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getLastOrdersSuccess?.call(orders);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersSuccess != null) {
      return getLastOrdersSuccess(orders);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getLastOrdersSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getLastOrdersSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersSuccess != null) {
      return getLastOrdersSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetLastOrdersSuccess implements OrdersState {
  const factory _GetLastOrdersSuccess(final List<GetOrdersModel> orders) =
      _$GetLastOrdersSuccessImpl;

  List<GetOrdersModel> get orders;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetLastOrdersSuccessImplCopyWith<_$GetLastOrdersSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetLastOrdersFailureImplCopyWith<$Res> {
  factory _$$GetLastOrdersFailureImplCopyWith(_$GetLastOrdersFailureImpl value,
          $Res Function(_$GetLastOrdersFailureImpl) then) =
      __$$GetLastOrdersFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$GetLastOrdersFailureImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetLastOrdersFailureImpl>
    implements _$$GetLastOrdersFailureImplCopyWith<$Res> {
  __$$GetLastOrdersFailureImplCopyWithImpl(_$GetLastOrdersFailureImpl _value,
      $Res Function(_$GetLastOrdersFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetLastOrdersFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetLastOrdersFailureImpl implements _GetLastOrdersFailure {
  const _$GetLastOrdersFailureImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'OrdersState.getLastOrdersFailure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetLastOrdersFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetLastOrdersFailureImplCopyWith<_$GetLastOrdersFailureImpl>
      get copyWith =>
          __$$GetLastOrdersFailureImplCopyWithImpl<_$GetLastOrdersFailureImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getLastOrdersFailure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getLastOrdersFailure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersFailure != null) {
      return getLastOrdersFailure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getLastOrdersFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getLastOrdersFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getLastOrdersFailure != null) {
      return getLastOrdersFailure(this);
    }
    return orElse();
  }
}

abstract class _GetLastOrdersFailure implements OrdersState {
  const factory _GetLastOrdersFailure(final String error) =
      _$GetLastOrdersFailureImpl;

  String get error;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetLastOrdersFailureImplCopyWith<_$GetLastOrdersFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetOrderDetailsLoadingImplCopyWith<$Res> {
  factory _$$GetOrderDetailsLoadingImplCopyWith(
          _$GetOrderDetailsLoadingImpl value,
          $Res Function(_$GetOrderDetailsLoadingImpl) then) =
      __$$GetOrderDetailsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetOrderDetailsLoadingImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrderDetailsLoadingImpl>
    implements _$$GetOrderDetailsLoadingImplCopyWith<$Res> {
  __$$GetOrderDetailsLoadingImplCopyWithImpl(
      _$GetOrderDetailsLoadingImpl _value,
      $Res Function(_$GetOrderDetailsLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetOrderDetailsLoadingImpl implements _GetOrderDetailsLoading {
  const _$GetOrderDetailsLoadingImpl();

  @override
  String toString() {
    return 'OrdersState.getOrderDetailsLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrderDetailsLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrderDetailsLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrderDetailsLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsLoading != null) {
      return getOrderDetailsLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsLoading != null) {
      return getOrderDetailsLoading(this);
    }
    return orElse();
  }
}

abstract class _GetOrderDetailsLoading implements OrdersState {
  const factory _GetOrderDetailsLoading() = _$GetOrderDetailsLoadingImpl;
}

/// @nodoc
abstract class _$$GetOrderDetailsSuccessImplCopyWith<$Res> {
  factory _$$GetOrderDetailsSuccessImplCopyWith(
          _$GetOrderDetailsSuccessImpl value,
          $Res Function(_$GetOrderDetailsSuccessImpl) then) =
      __$$GetOrderDetailsSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OrderDetailsModel order});

  $OrderDetailsModelCopyWith<$Res> get order;
}

/// @nodoc
class __$$GetOrderDetailsSuccessImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrderDetailsSuccessImpl>
    implements _$$GetOrderDetailsSuccessImplCopyWith<$Res> {
  __$$GetOrderDetailsSuccessImplCopyWithImpl(
      _$GetOrderDetailsSuccessImpl _value,
      $Res Function(_$GetOrderDetailsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = null,
  }) {
    return _then(_$GetOrderDetailsSuccessImpl(
      null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderDetailsModel,
    ));
  }

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderDetailsModelCopyWith<$Res> get order {
    return $OrderDetailsModelCopyWith<$Res>(_value.order, (value) {
      return _then(_value.copyWith(order: value));
    });
  }
}

/// @nodoc

class _$GetOrderDetailsSuccessImpl implements _GetOrderDetailsSuccess {
  const _$GetOrderDetailsSuccessImpl(this.order);

  @override
  final OrderDetailsModel order;

  @override
  String toString() {
    return 'OrdersState.getOrderDetailsSuccess(order: $order)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrderDetailsSuccessImpl &&
            (identical(other.order, order) || other.order == order));
  }

  @override
  int get hashCode => Object.hash(runtimeType, order);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrderDetailsSuccessImplCopyWith<_$GetOrderDetailsSuccessImpl>
      get copyWith => __$$GetOrderDetailsSuccessImplCopyWithImpl<
          _$GetOrderDetailsSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrderDetailsSuccess(order);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrderDetailsSuccess?.call(order);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsSuccess != null) {
      return getOrderDetailsSuccess(order);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsSuccess != null) {
      return getOrderDetailsSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetOrderDetailsSuccess implements OrdersState {
  const factory _GetOrderDetailsSuccess(final OrderDetailsModel order) =
      _$GetOrderDetailsSuccessImpl;

  OrderDetailsModel get order;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrderDetailsSuccessImplCopyWith<_$GetOrderDetailsSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetSpecialOrderDetailsSuccessImplCopyWith<$Res> {
  factory _$$GetSpecialOrderDetailsSuccessImplCopyWith(
          _$GetSpecialOrderDetailsSuccessImpl value,
          $Res Function(_$GetSpecialOrderDetailsSuccessImpl) then) =
      __$$GetSpecialOrderDetailsSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SpecialOrderDetailsModel specialOrder});

  $SpecialOrderDetailsModelCopyWith<$Res> get specialOrder;
}

/// @nodoc
class __$$GetSpecialOrderDetailsSuccessImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetSpecialOrderDetailsSuccessImpl>
    implements _$$GetSpecialOrderDetailsSuccessImplCopyWith<$Res> {
  __$$GetSpecialOrderDetailsSuccessImplCopyWithImpl(
      _$GetSpecialOrderDetailsSuccessImpl _value,
      $Res Function(_$GetSpecialOrderDetailsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? specialOrder = null,
  }) {
    return _then(_$GetSpecialOrderDetailsSuccessImpl(
      null == specialOrder
          ? _value.specialOrder
          : specialOrder // ignore: cast_nullable_to_non_nullable
              as SpecialOrderDetailsModel,
    ));
  }

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SpecialOrderDetailsModelCopyWith<$Res> get specialOrder {
    return $SpecialOrderDetailsModelCopyWith<$Res>(_value.specialOrder,
        (value) {
      return _then(_value.copyWith(specialOrder: value));
    });
  }
}

/// @nodoc

class _$GetSpecialOrderDetailsSuccessImpl
    implements _GetSpecialOrderDetailsSuccess {
  const _$GetSpecialOrderDetailsSuccessImpl(this.specialOrder);

  @override
  final SpecialOrderDetailsModel specialOrder;

  @override
  String toString() {
    return 'OrdersState.getSpecialOrderDetailsSuccess(specialOrder: $specialOrder)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetSpecialOrderDetailsSuccessImpl &&
            (identical(other.specialOrder, specialOrder) ||
                other.specialOrder == specialOrder));
  }

  @override
  int get hashCode => Object.hash(runtimeType, specialOrder);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetSpecialOrderDetailsSuccessImplCopyWith<
          _$GetSpecialOrderDetailsSuccessImpl>
      get copyWith => __$$GetSpecialOrderDetailsSuccessImplCopyWithImpl<
          _$GetSpecialOrderDetailsSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getSpecialOrderDetailsSuccess(specialOrder);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getSpecialOrderDetailsSuccess?.call(specialOrder);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getSpecialOrderDetailsSuccess != null) {
      return getSpecialOrderDetailsSuccess(specialOrder);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getSpecialOrderDetailsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getSpecialOrderDetailsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getSpecialOrderDetailsSuccess != null) {
      return getSpecialOrderDetailsSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetSpecialOrderDetailsSuccess implements OrdersState {
  const factory _GetSpecialOrderDetailsSuccess(
          final SpecialOrderDetailsModel specialOrder) =
      _$GetSpecialOrderDetailsSuccessImpl;

  SpecialOrderDetailsModel get specialOrder;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetSpecialOrderDetailsSuccessImplCopyWith<
          _$GetSpecialOrderDetailsSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetOrderDetailsFailureImplCopyWith<$Res> {
  factory _$$GetOrderDetailsFailureImplCopyWith(
          _$GetOrderDetailsFailureImpl value,
          $Res Function(_$GetOrderDetailsFailureImpl) then) =
      __$$GetOrderDetailsFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$GetOrderDetailsFailureImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$GetOrderDetailsFailureImpl>
    implements _$$GetOrderDetailsFailureImplCopyWith<$Res> {
  __$$GetOrderDetailsFailureImplCopyWithImpl(
      _$GetOrderDetailsFailureImpl _value,
      $Res Function(_$GetOrderDetailsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$GetOrderDetailsFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetOrderDetailsFailureImpl implements _GetOrderDetailsFailure {
  const _$GetOrderDetailsFailureImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'OrdersState.getOrderDetailsFailure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrderDetailsFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrderDetailsFailureImplCopyWith<_$GetOrderDetailsFailureImpl>
      get copyWith => __$$GetOrderDetailsFailureImplCopyWithImpl<
          _$GetOrderDetailsFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return getOrderDetailsFailure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return getOrderDetailsFailure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsFailure != null) {
      return getOrderDetailsFailure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return getOrderDetailsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (getOrderDetailsFailure != null) {
      return getOrderDetailsFailure(this);
    }
    return orElse();
  }
}

abstract class _GetOrderDetailsFailure implements OrdersState {
  const factory _GetOrderDetailsFailure(final String error) =
      _$GetOrderDetailsFailureImpl;

  String get error;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrderDetailsFailureImplCopyWith<_$GetOrderDetailsFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConvertStatusOrderLoadingImplCopyWith<$Res> {
  factory _$$ConvertStatusOrderLoadingImplCopyWith(
          _$ConvertStatusOrderLoadingImpl value,
          $Res Function(_$ConvertStatusOrderLoadingImpl) then) =
      __$$ConvertStatusOrderLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConvertStatusOrderLoadingImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$ConvertStatusOrderLoadingImpl>
    implements _$$ConvertStatusOrderLoadingImplCopyWith<$Res> {
  __$$ConvertStatusOrderLoadingImplCopyWithImpl(
      _$ConvertStatusOrderLoadingImpl _value,
      $Res Function(_$ConvertStatusOrderLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConvertStatusOrderLoadingImpl implements _ConvertStatusOrderLoading {
  const _$ConvertStatusOrderLoadingImpl();

  @override
  String toString() {
    return 'OrdersState.convertStatusOrderLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConvertStatusOrderLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return convertStatusOrderLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return convertStatusOrderLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderLoading != null) {
      return convertStatusOrderLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderLoading != null) {
      return convertStatusOrderLoading(this);
    }
    return orElse();
  }
}

abstract class _ConvertStatusOrderLoading implements OrdersState {
  const factory _ConvertStatusOrderLoading() = _$ConvertStatusOrderLoadingImpl;
}

/// @nodoc
abstract class _$$ConvertStatusOrderSuccessImplCopyWith<$Res> {
  factory _$$ConvertStatusOrderSuccessImplCopyWith(
          _$ConvertStatusOrderSuccessImpl value,
          $Res Function(_$ConvertStatusOrderSuccessImpl) then) =
      __$$ConvertStatusOrderSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConvertStatusOrderSuccessImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$ConvertStatusOrderSuccessImpl>
    implements _$$ConvertStatusOrderSuccessImplCopyWith<$Res> {
  __$$ConvertStatusOrderSuccessImplCopyWithImpl(
      _$ConvertStatusOrderSuccessImpl _value,
      $Res Function(_$ConvertStatusOrderSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConvertStatusOrderSuccessImpl implements _ConvertStatusOrderSuccess {
  const _$ConvertStatusOrderSuccessImpl();

  @override
  String toString() {
    return 'OrdersState.convertStatusOrderSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConvertStatusOrderSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return convertStatusOrderSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return convertStatusOrderSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderSuccess != null) {
      return convertStatusOrderSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderSuccess != null) {
      return convertStatusOrderSuccess(this);
    }
    return orElse();
  }
}

abstract class _ConvertStatusOrderSuccess implements OrdersState {
  const factory _ConvertStatusOrderSuccess() = _$ConvertStatusOrderSuccessImpl;
}

/// @nodoc
abstract class _$$ConvertStatusOrderFailureImplCopyWith<$Res> {
  factory _$$ConvertStatusOrderFailureImplCopyWith(
          _$ConvertStatusOrderFailureImpl value,
          $Res Function(_$ConvertStatusOrderFailureImpl) then) =
      __$$ConvertStatusOrderFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$ConvertStatusOrderFailureImplCopyWithImpl<$Res>
    extends _$OrdersStateCopyWithImpl<$Res, _$ConvertStatusOrderFailureImpl>
    implements _$$ConvertStatusOrderFailureImplCopyWith<$Res> {
  __$$ConvertStatusOrderFailureImplCopyWithImpl(
      _$ConvertStatusOrderFailureImpl _value,
      $Res Function(_$ConvertStatusOrderFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$ConvertStatusOrderFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ConvertStatusOrderFailureImpl implements _ConvertStatusOrderFailure {
  const _$ConvertStatusOrderFailureImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'OrdersState.convertStatusOrderFailure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConvertStatusOrderFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConvertStatusOrderFailureImplCopyWith<_$ConvertStatusOrderFailureImpl>
      get copyWith => __$$ConvertStatusOrderFailureImplCopyWithImpl<
          _$ConvertStatusOrderFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getOrdersSuccess,
    required TResult Function(String error) getOrdersFailure,
    required TResult Function() getLastOrdersLoading,
    required TResult Function(List<GetOrdersModel> orders) getLastOrdersSuccess,
    required TResult Function(String error) getLastOrdersFailure,
    required TResult Function() getOrderDetailsLoading,
    required TResult Function(OrderDetailsModel order) getOrderDetailsSuccess,
    required TResult Function(SpecialOrderDetailsModel specialOrder)
        getSpecialOrderDetailsSuccess,
    required TResult Function(String error) getOrderDetailsFailure,
    required TResult Function() convertStatusOrderLoading,
    required TResult Function() convertStatusOrderSuccess,
    required TResult Function(String error) convertStatusOrderFailure,
  }) {
    return convertStatusOrderFailure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult? Function(String error)? getOrdersFailure,
    TResult? Function()? getLastOrdersLoading,
    TResult? Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult? Function(String error)? getLastOrdersFailure,
    TResult? Function()? getOrderDetailsLoading,
    TResult? Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult? Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(String error)? getOrderDetailsFailure,
    TResult? Function()? convertStatusOrderLoading,
    TResult? Function()? convertStatusOrderSuccess,
    TResult? Function(String error)? convertStatusOrderFailure,
  }) {
    return convertStatusOrderFailure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getOrdersSuccess,
    TResult Function(String error)? getOrdersFailure,
    TResult Function()? getLastOrdersLoading,
    TResult Function(List<GetOrdersModel> orders)? getLastOrdersSuccess,
    TResult Function(String error)? getLastOrdersFailure,
    TResult Function()? getOrderDetailsLoading,
    TResult Function(OrderDetailsModel order)? getOrderDetailsSuccess,
    TResult Function(SpecialOrderDetailsModel specialOrder)?
        getSpecialOrderDetailsSuccess,
    TResult Function(String error)? getOrderDetailsFailure,
    TResult Function()? convertStatusOrderLoading,
    TResult Function()? convertStatusOrderSuccess,
    TResult Function(String error)? convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderFailure != null) {
      return convertStatusOrderFailure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetOrdersLoading value) getOrdersLoading,
    required TResult Function(_GetOrdersSuccess value) getOrdersSuccess,
    required TResult Function(_GetOrdersFailure value) getOrdersFailure,
    required TResult Function(_GetLastOrdersLoading value) getLastOrdersLoading,
    required TResult Function(_GetLastOrdersSuccess value) getLastOrdersSuccess,
    required TResult Function(_GetLastOrdersFailure value) getLastOrdersFailure,
    required TResult Function(_GetOrderDetailsLoading value)
        getOrderDetailsLoading,
    required TResult Function(_GetOrderDetailsSuccess value)
        getOrderDetailsSuccess,
    required TResult Function(_GetSpecialOrderDetailsSuccess value)
        getSpecialOrderDetailsSuccess,
    required TResult Function(_GetOrderDetailsFailure value)
        getOrderDetailsFailure,
    required TResult Function(_ConvertStatusOrderLoading value)
        convertStatusOrderLoading,
    required TResult Function(_ConvertStatusOrderSuccess value)
        convertStatusOrderSuccess,
    required TResult Function(_ConvertStatusOrderFailure value)
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult? Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult? Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult? Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult? Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult? Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult? Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult? Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult? Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult? Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult? Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult? Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult? Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
  }) {
    return convertStatusOrderFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetOrdersLoading value)? getOrdersLoading,
    TResult Function(_GetOrdersSuccess value)? getOrdersSuccess,
    TResult Function(_GetOrdersFailure value)? getOrdersFailure,
    TResult Function(_GetLastOrdersLoading value)? getLastOrdersLoading,
    TResult Function(_GetLastOrdersSuccess value)? getLastOrdersSuccess,
    TResult Function(_GetLastOrdersFailure value)? getLastOrdersFailure,
    TResult Function(_GetOrderDetailsLoading value)? getOrderDetailsLoading,
    TResult Function(_GetOrderDetailsSuccess value)? getOrderDetailsSuccess,
    TResult Function(_GetSpecialOrderDetailsSuccess value)?
        getSpecialOrderDetailsSuccess,
    TResult Function(_GetOrderDetailsFailure value)? getOrderDetailsFailure,
    TResult Function(_ConvertStatusOrderLoading value)?
        convertStatusOrderLoading,
    TResult Function(_ConvertStatusOrderSuccess value)?
        convertStatusOrderSuccess,
    TResult Function(_ConvertStatusOrderFailure value)?
        convertStatusOrderFailure,
    required TResult orElse(),
  }) {
    if (convertStatusOrderFailure != null) {
      return convertStatusOrderFailure(this);
    }
    return orElse();
  }
}

abstract class _ConvertStatusOrderFailure implements OrdersState {
  const factory _ConvertStatusOrderFailure(final String error) =
      _$ConvertStatusOrderFailureImpl;

  String get error;

  /// Create a copy of OrdersState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConvertStatusOrderFailureImplCopyWith<_$ConvertStatusOrderFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
