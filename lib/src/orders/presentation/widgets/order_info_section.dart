import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:flutter/material.dart';

class OrderInfoSection extends StatelessWidget {
  const OrderInfoSection({
    super.key,
    required this.orderId,
    required this.orderDate,
    required this.isSpecialOrder,
  });

  final int orderId;
  final DateTime orderDate;
  final bool isSpecialOrder;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(start: 16),
      child: Row(
        children: [
          Row(
            spacing: 8,
            children: [
              Assets.icons.calendar.svg(),
              Text(
                UiHelper.dateFormatting(
                  orderDate,
                  format: "dd/MM/yyyy",
                ),
                style: TextStyles.body16,
              ),
            ],
          ),
          const SizedBox(
            height: 20,
            child: VerticalDivider(
              thickness: 1,
            ),
          ),
          Text(
            '# $orderId',
            style: TextStyles.body16,
          ),
          if (isSpecialOrder) ...[
            const Spacer(),
            Container(
              decoration: const BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadiusDirectional.only(
                  topStart: Radius.circular(8),
                  bottomStart: Radius.circular(8),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                context.l10n.specialOrder,
                style: TextStyles.body14.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }
}
