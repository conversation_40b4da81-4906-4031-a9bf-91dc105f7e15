import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/client_data_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/delivery_address_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_info_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_status_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/special_order_details/special_order_details_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/special_order_details/special_order_payment_section.dart';
import 'package:flutter/material.dart';

class SpecialOrderDetailsBody extends StatelessWidget {
  const SpecialOrderDetailsBody({super.key, required this.specialOrder});
  final SpecialOrderDetailsModel specialOrder;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        spacing: 16,
        children: [
          OrderInfoSection(
            orderId: specialOrder.id ?? 0,
            orderDate: specialOrder.dateOrdered ?? DateTime.now(),
            isSpecialOrder: true,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              spacing: 16,
              children: [
                ClientDataSection(
                  clientName: specialOrder.customerName ?? '',
                  clientPhone: specialOrder.customerPhone ?? '',
                ),
                DeliveryAddressSection(
                  address:
                      specialOrder.customerAddress ?? const CustomerAddress(),
                ),
                SpecialOrderDetailsSection(
                  description: specialOrder.note ?? '',
                  imageUrl: specialOrder.image ?? '',
                ),
                const Divider(height: 8),
                SpecialOrderPaymentSection(specialOrder: specialOrder),
                const Divider(height: 8),
                OrderStatusSection(
                  trakingOrder: specialOrder.trakingOrder ?? [],
                  orderId: specialOrder.id ?? 0,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
