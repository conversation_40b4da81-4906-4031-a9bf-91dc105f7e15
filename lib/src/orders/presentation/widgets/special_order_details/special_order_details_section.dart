import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class SpecialOrderDetailsSection extends StatelessWidget {
  const SpecialOrderDetailsSection(
      {super.key, required this.description, required this.imageUrl});
  final String description;
  final String imageUrl;
  @override
  Widget build(BuildContext context) {
    return OrderTitledSection(
      isExpansionTile: true,
      icon: Assets.icons.cart.svg(),
      title: context.l10n.orderDetails,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 16,
          children: [
            Text(
              description,
            ),
            CustomNetworkImage(
              imageUrl,
              height: 300,
            ),
          ],
        ),
      ),
    );
  }
}
