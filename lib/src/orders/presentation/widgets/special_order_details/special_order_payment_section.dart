import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class SpecialOrderPaymentSection extends StatelessWidget {
  const SpecialOrderPaymentSection({super.key, required this.specialOrder});
  final SpecialOrderDetailsModel specialOrder;
  @override
  Widget build(BuildContext context) {
    return OrderTitledSection(
      icon: Assets.icons.payment.svg(),
      title: context.l10n.payment,
      label: 'نقدي',
      child: Column(
        children: [
          PaymentItem(
            title: context.l10n.totalPrice,
            value: specialOrder.subTotal ?? 0.0,
          ),
          PaymentItem(
            title: context.l10n.deliveryFees,
            value: specialOrder.deliveryValue ?? 0.0,
          ),
          const Divider(height: 8),
          PaymentItem(
            title: context.l10n.total,
            value: specialOrder.total ?? 0.0,
            isTotal: true,
          ),
        ],
      ),
    );
  }
}

class PaymentItem extends StatelessWidget {
  const PaymentItem({
    super.key,
    required this.title,
    required this.value,
    this.isTotal = false,
  });

  final String title;
  final double value;
  final bool isTotal;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyles.body14.copyWith(
            color: isTotal ? AppColors.primary : AppColors.black,
            fontWeight: isTotal ? FontWeight.w800 : FontWeight.normal,
          ),
        ),
        const Spacer(),
        Text(
          '$value ${context.l10n.egp}',
          style: TextStyles.body14.copyWith(
            color: isTotal ? AppColors.primary : AppColors.black,
            fontWeight: isTotal ? FontWeight.w800 : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
