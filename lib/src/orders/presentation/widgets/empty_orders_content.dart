import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:flutter/material.dart';

class EmptyOrdersContent extends StatelessWidget {
  const EmptyOrdersContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        spacing: 16,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Assets.icons.emptyOrders.svg(),
          Text(
            context.l10n.youDidNotReceiveAnyOrdersYet,
            style: TextStyles.title16,
          ),
        ],
      ),
    );
  }
}
