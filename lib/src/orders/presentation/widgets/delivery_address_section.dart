import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class DeliveryAddressSection extends StatelessWidget {
  const DeliveryAddressSection({super.key, required this.address});
  final CustomerAddress address;
  @override
  Widget build(BuildContext context) {
    String getAddress() {
      String addressText = '${context.l10n.address}: ';
      if (address.cityName != null && address.cityName!.isNotEmpty) {
        addressText += '${address.cityName}';
      }
      if (address.districtName != null && address.districtName!.isNotEmpty) {
        addressText += ', ${address.districtName}';
      }
      if (address.mark != null && address.mark!.isNotEmpty) {
        addressText += ',${context.l10n.mark}: ${address.mark}';
      }
      if (address.building != null && address.building!.isNotEmpty) {
        addressText += ', ${context.l10n.building}: ${address.building}';
      }
      if (address.floor != null && address.floor!.isNotEmpty) {
        addressText += ', ${context.l10n.floor}: ${address.floor}';
      }
      if (address.flat != null && address.flat!.isNotEmpty) {
        addressText += ', ${context.l10n.flat}: ${address.flat}';
      }
      return addressText;
    }

    return OrderTitledSection(
      icon: Assets.icons.location.svg(
        width: 16,
        height: 16,
        colorFilter: const ColorFilter.mode(
          AppColors.primary,
          BlendMode.srcIn,
        ),
      ),
      title: context.l10n.deliveryAndAddress,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          spacing: 4,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.deliverOrder,
              style: TextStyles.body14.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
            Text(getAddress()),
          ],
        ),
      ),
    );
  }
}
