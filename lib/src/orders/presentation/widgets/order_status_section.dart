import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class OrderStatusSection extends StatelessWidget {
  const OrderStatusSection(
      {super.key, required this.trakingOrder, required this.orderId});

  final List<TrakingOrder> trakingOrder;
  final int orderId;

  @override
  Widget build(BuildContext context) {
    final cubit = injector<OrdersCubit>();
    List<TrakingOrder> orderStatus = trakingOrder
        .where((element) => [2, 3, 4].contains(element.statusId))
        .toList();
    if (trakingOrder.lastOrNull?.statusId == 2 ||
        trakingOrder.lastOrNull?.statusId == 3) {
      return OrderTitledSection(
        icon: Assets.icons.orderTracking.svg(),
        title: context.l10n.orderStatus,
        useBackground: false,
        child: Column(
          children: [
            ...List.generate(orderStatus.length, (index) {
              return OrderStatusItem(
                status: orderStatus[index].orderStatus ?? '',
                date: DateTime.now(),
              );
            }),
            const SizedBox(height: 16),
            Row(
              spacing: 8,
              children: [
                Expanded(
                  child: Text(
                    context.l10n.changeOrderStatus,
                    style: TextStyles.body16,
                  ),
                ),
                Expanded(
                  child: CustomElevatedButton(
                    height: 40,
                    child: Text(
                      _getChangeOrderStatusName(
                        trakingOrder.lastOrNull?.statusId ?? 0,
                        context,
                      ),
                    ),
                    onPressed: () {
                      if (trakingOrder.lastOrNull?.statusId == 2) {
                        cubit.convertStatusOrder(orderId, 3, '');
                      } else if (trakingOrder.lastOrNull?.statusId == 3) {
                        cubit.convertStatusOrder(orderId, 4, '');
                      } else {
                        return;
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (trakingOrder.lastOrNull?.statusId == 2)
              TextButton(
                onPressed: () => UiHelper.showCustomDialog(
                  context: context,
                  dialog: CancelSpecialOrderDialog(
                    orderId: orderId,
                  ),
                ),
                child: Text(
                  context.l10n.cancelOrder,
                  style: TextStyles.title20.copyWith(
                    color: AppColors.red,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
          ],
        ),
      );
    } else if (trakingOrder.lastOrNull?.statusId == 4) {
      return Text(
        context.l10n.prepared,
        style: TextStyles.headline32.copyWith(
          color: AppColors.red,
        ),
      );
    } else if (trakingOrder.lastOrNull?.statusId == 5) {
      return Text(
        context.l10n.shipping,
        style: TextStyles.headline32.copyWith(
          color: AppColors.red,
        ),
      );
    } else if (trakingOrder.lastOrNull?.statusId == 6) {
      return Text(
        context.l10n.delivered,
        style: TextStyles.headline32.copyWith(
          color: AppColors.red,
        ),
      );
    } else if (trakingOrder.lastOrNull?.statusId == 7) {
      return Text(
        context.l10n.cancelledFromStore,
        style: TextStyles.headline32.copyWith(
          color: AppColors.red,
        ),
      );
    } else if (trakingOrder.lastOrNull?.statusId == 8) {
      return Text(
        context.l10n.cancelledFromClient,
        style: TextStyles.headline32.copyWith(
          color: AppColors.red,
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  String _getChangeOrderStatusName(int statusId, BuildContext context) {
    switch (statusId) {
      case 2:
        return context.l10n.preparing;
      case 3:
        return context.l10n.prepared;
      default:
        return '';
    }
  }
}

class OrderStatusItem extends StatelessWidget {
  const OrderStatusItem({super.key, required this.status, required this.date});

  final String status;
  final DateTime date;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: [
        const Icon(
          Icons.check_circle_rounded,
          size: 16,
          color: AppColors.green,
        ),
        Text(status),
        const Spacer(),
        Text(
          UiHelper.dateFormatting(
            date,
            format: 'dd/MM/yyyy - hh:mm a',
            locale: context.currentLocaleCode,
          ),
        ),
      ],
    );
  }
}

class CancelSpecialOrderDialog extends StatefulWidget {
  const CancelSpecialOrderDialog({
    super.key,
    required this.orderId,
  });
  final int orderId;
  @override
  State<CancelSpecialOrderDialog> createState() =>
      _CancelSpecialOrderDialogState();
}

class _CancelSpecialOrderDialogState extends State<CancelSpecialOrderDialog> {
  String _reason = '';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        spacing: 20,
        children: [
          Text(context.l10n.pleaseEnterTheReasonForCancelingTheOrder),
          CustomTextField(
            maxLines: 2,
            hintText: context.l10n.reason,
            onChanged: (value) {
              _reason = value!;
              log(_reason);
            },
          ),
        ],
      ),
      actions: [
        CustomElevatedButton(
          onPressed: () async {
            await injector<OrdersCubit>().convertStatusOrder(
              widget.orderId,
              7,
              _reason,
            );
            injector<OrdersCubit>().operations.dataWrapper.orderStatusId = 2;
            await injector<OrdersCubit>().getOrders();
            Navigator.pop(context);
          },
          child: Text(context.l10n.send),
        )
      ],
    );
  }
}
