import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class ClientDataSection extends StatelessWidget {
  const ClientDataSection(
      {super.key, required this.clientName, required this.clientPhone});
  final String clientName;
  final String clientPhone;
  @override
  Widget build(BuildContext context) {
    return OrderTitledSection(
      icon: Assets.icons.person.svg(
        width: 16,
        height: 16,
        colorFilter: const ColorFilter.mode(
          AppColors.primary,
          BlendMode.srcIn,
        ),
      ),
      title: context.l10n.clientData,
      child: Row(
        children: [
          Expanded(
            child: Text(
              clientName,
              style: TextStyles.body14,
            ),
          ),
          clientPhone != ''
              ? Expanded(
                  child: CustomElevatedButton(
                    onPressed: phoneCall,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      spacing: 8,
                      children: [
                        Assets.icons.call.svg(),
                        Text(context.l10n.directCall),
                      ],
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  Future<void> phoneCall() async {
    final Uri callUri = Uri(scheme: 'tel', path: clientPhone);

    if (await canLaunchUrl(callUri)) {
      await launchUrl(callUri);
    } else {
      throw 'Could not place call to $clientPhone';
    }
  }
}
