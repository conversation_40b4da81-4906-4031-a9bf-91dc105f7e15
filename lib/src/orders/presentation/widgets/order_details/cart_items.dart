import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:flutter/material.dart';

class CartItems extends StatelessWidget {
  const CartItems({
    super.key,
    required this.order,
  });
  final OrderStoreDetail order;
  @override
  Widget build(BuildContext context) {
    final mergedOptions = order.orderDetailOptionValues?.map((e) {
      return '${e.optionName}';
    }).toList();
    final mergedAdditions = order.orderStoreDetailAdditions?.map((e) {
      return '${e.productAdditionName}';
    }).toList();
    final mergedModify = order.orderStoreDetailModifyProduct?.map((e) {
      return '${e.productAdditionName}';
    }).toList();
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomNetworkImage(
          order.productImage ?? '',
          width: 80,
          height: 80,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '${order.productName} ${mergedOptions?.join('-')} x',
                      style: TextStyles.body16,
                    ),
                    const SizedBox(width: 3),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: const BoxDecoration(
                          color: AppColors.primary, shape: BoxShape.circle),
                      child: Text(
                        order.quantity?.toString() ?? '0',
                        style: TextStyles.body16.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if ((mergedAdditions?.isNotEmpty ?? false))
                Text(
                  '${context.l10n.additons} : ${mergedAdditions?.join('-')}',
                  style: TextStyles.body12,
                ),
              if ((mergedModify?.isNotEmpty ?? false))
                Text(
                  '${context.l10n.mopd} : ${mergedModify?.join('-')}',
                  style: TextStyles.body12,
                ),
              Text(
                '${context.l10n.total} : ${order.total} ${context.l10n.egp}',
                style: TextStyles.body12.copyWith(
                    color: AppColors.green, fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
        const SizedBox(width: 10),
      ],
    );
  }
}
