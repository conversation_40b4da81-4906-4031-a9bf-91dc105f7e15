import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/client_data_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/delivery_address_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_details/order_cart_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_details/order_payment_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_info_section.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_status_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OrderDetailsBody extends StatelessWidget {
  const OrderDetailsBody({super.key, required this.order});

  final OrderDetailsModel order;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        spacing: 16,
        children: [
          OrderInfoSection(
            orderId: order.id ?? 0,
            orderDate: order.dateOrdered ?? DateTime.now(),
            isSpecialOrder: false,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              spacing: 16,
              children: [
                ClientDataSection(
                  clientName: order.customerName ?? '',
                  clientPhone: order.customerPhone ?? '',
                ),
                DeliveryAddressSection(
                  address: order.customerAddress ?? const CustomerAddress(),
                ),
                OrderCartSection(
                  orderDetails:
                      order.orderStores?.firstOrNull?.orderStoreDetails,
                ),
                const Divider(height: 8),
                OrderPaymentSection(order: order),
                const Divider(height: 8),
                BlocConsumer<OrdersCubit, OrdersState>(
                  listenWhen: (previous, current) => current.maybeWhen(
                    orElse: () => false,
                    convertStatusOrderLoading: () => true,
                    convertStatusOrderFailure: (_) => true,
                    convertStatusOrderSuccess: () => true,
                  ),
                  listener: (context, state) {
                    state.maybeWhen(
                      orElse: () => null,
                      convertStatusOrderLoading: () {
                        UiHelper.onLoading(context);
                      },
                      convertStatusOrderFailure: (failure) {
                        UiHelper.onFailure(context, failure);
                      },
                      convertStatusOrderSuccess: () async {
                        UiHelper.onSuccess(context);
                        await injector<OrdersCubit>().getOrderDetails(
                          order.id ?? 0,
                        );
                        injector<OrdersCubit>()
                                .operations
                                .dataWrapper
                                .orderStatusId =
                            order.trakingOrder?.lastOrNull?.statusId ?? 0;
                        await injector<OrdersCubit>().getOrders();
                      },
                    );
                  },
                  buildWhen: (previous, current) => current.maybeWhen(
                    orElse: () => false,
                    convertStatusOrderSuccess: () => true,
                  ),
                  builder: (context, state) {
                    return OrderStatusSection(
                      trakingOrder: order.trakingOrder ?? [],
                      orderId: order.id ?? 0,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
