import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_details/cart_items.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class OrderCartSection extends StatelessWidget {
  const OrderCartSection({
    super.key,
    required this.orderDetails,
  });
  final List<OrderStoreDetail>? orderDetails;
  @override
  Widget build(BuildContext context) {
    return OrderTitledSection(
      icon: Assets.icons.cart.svg(),
      isExpansionTile: true,
      title: context.l10n.orderDetails,
      child: ListView.separated(
        padding: const EdgeInsets.all(8),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: orderDetails?.length ?? 0,
        separatorBuilder: (context, index) => const Divider(
          indent: 90,
          color: Color(0xffF2F2F2),
        ),
        itemBuilder: (context, index) => CartItems(
          order: orderDetails?[index] ?? const OrderStoreDetail(),
        ),
      ),
    );
  }
}
