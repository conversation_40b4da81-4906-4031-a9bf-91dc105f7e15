import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_titled_section.dart';
import 'package:flutter/material.dart';

class OrderPaymentSection extends StatelessWidget {
  const OrderPaymentSection({super.key, required this.order});
  final OrderDetailsModel order;
  @override
  Widget build(BuildContext context) {
    return OrderTitledSection(
      icon: Assets.icons.payment.svg(),
      title: context.l10n.payment,
      label: order.paymentMethod ?? '',
      child: Column(
        children: [
          PaymentItem(
            title: context.l10n.totalPrice,
            value: order.subTotal ?? 0.0,
          ),
          PaymentItem(
            title: context.l10n.deliveryFees,
            value: order.deliveryValue ?? 0.0,
          ),
          order.walletDiscount != 0 && order.walletDiscount != null
              ? PaymentItem(
                  title: context.l10n.walletDiscount,
                  value: -(order.walletDiscount ?? 0.0),
                )
              : const SizedBox(),
          order.couponValue != 0 && order.couponValue != null
              ? PaymentItem(
                  title: context.l10n.couponValue,
                  value: -(order.couponValue ?? 0.0),
                )
              : const SizedBox(),
          const Divider(height: 8),
          PaymentItem(
            title: context.l10n.total,
            value: order.total ?? 0.0,
            color: AppColors.red,
          ),
        ],
      ),
    );
  }
}

class PaymentItem extends StatelessWidget {
  const PaymentItem({
    super.key,
    required this.title,
    required this.value,
    this.color = AppColors.black,
  });

  final String title;
  final double value;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyles.body14.copyWith(
            color: color ?? AppColors.black,
            fontWeight: FontWeight.normal,
          ),
        ),
        const Spacer(),
        Text(
          '$value ${context.l10n.egp}',
          style: TextStyles.body14.copyWith(
            color: color ?? AppColors.black,
            fontWeight: FontWeight.w800,
          ),
        ),
      ],
    );
  }
}
