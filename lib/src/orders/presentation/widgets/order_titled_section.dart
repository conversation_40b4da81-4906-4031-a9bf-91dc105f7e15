import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';

class OrderTitledSection extends StatelessWidget {
  const OrderTitledSection({
    super.key,
    required this.icon,
    required this.title,
    this.child,
    this.label,
    this.isExpansionTile = false,
    this.useBackground = true,
  });

  final Widget icon;
  final String title;
  final String? label;
  final Widget? child;
  final bool isExpansionTile;
  final bool useBackground;

  @override
  Widget build(BuildContext context) {
    return isExpansionTile
        ? ExpansionTile(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
              side: BorderSide.none,
            ),
            collapsedShape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
              side: BorderSide.none,
            ),
            backgroundColor: Colors.transparent,
            collapsedBackgroundColor: Colors.transparent,
            title: Row(
              spacing: 8,
              children: [
                icon,
                Text(
                  title,
                  style: TextStyles.body14.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            children: [if (child != null) child!],
          )
        : Column(
            spacing: 8,
            children: [
              Row(
                spacing: 8,
                children: [
                  icon,
                  Text(
                    title,
                    style: TextStyles.body14.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const Spacer(),
                  if (label != null) Text(label!),
                ],
              ),
              if (child != null)
                Container(
                  decoration: BoxDecoration(
                    color: useBackground
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: child,
                ),
            ],
          );
  }
}
