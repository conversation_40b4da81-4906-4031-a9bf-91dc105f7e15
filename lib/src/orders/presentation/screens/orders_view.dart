import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/screens/current_orders_tab_view.dart';
import 'package:alsarea_store/src/orders/presentation/screens/previous_orders_tab_view.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OrdersView extends StatefulWidget {
  const OrdersView({super.key});

  @override
  State<OrdersView> createState() => _OrdersViewState();
}

class _OrdersViewState extends State<OrdersView> {
  final cubit = injector<OrdersCubit>();
  late String? token = '';
  @override
  void initState() {
    super.initState();
    cubit.operations.dataWrapper.orderStatusId = 2;
    cubit.getOrders();
  }

  void getToken() async {
    final String? token =
        await SharedPreferencesHelper.getSecuredString('token');
    setState(() {
      this.token = token;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              TabBar(
                onTap: (value) async {
                  switch (value) {
                    case 0:
                      cubit.operations.dataWrapper.orderStatusId = 1;
                      await cubit.getOrders();
                      break;
                    case 1:
                      await cubit.getLastOrders();
                      break;
                  }
                  cubit.getOrders();
                },
                tabs: [
                  Tab(text: context.l10n.currentOrders),
                  Tab(text: context.l10n.previousOrders),
                ],
              ),
              Expanded(
                child: TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    CurrentOrdersTabView(token: token),
                    BlocBuilder<OrdersCubit, OrdersState>(
                      buildWhen: (previous, current) => current.maybeWhen(
                        orElse: () => false,
                        getLastOrdersSuccess: (_) => true,
                        getLastOrdersFailure: (_) => true,
                        getLastOrdersLoading: () => true,
                      ),
                      builder: (context, state) {
                        return state.maybeWhen(
                          getLastOrdersSuccess: (orders) =>
                              PreviousOrdersTabView(orders: orders),
                          getLastOrdersFailure: (message) => Center(
                            child: token == ''
                                ? SizedBox(
                                    height: 1.sh,
                                    width: .95.sw,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          context.l10n.pleaseLoginToUnlock,
                                          style: TextStyles.title20,
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 16),
                                        SizedBox(
                                          width: .7.sw,
                                          child: CustomElevatedButton(
                                            child: Text(context.l10n.login),
                                            onPressed: () {
                                              context.router
                                                  .push(const LoginRoute());
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Text(
                                    message,
                                    style: const TextStyle(color: Colors.red),
                                  ),
                          ),
                          getLastOrdersLoading: () => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          orElse: () => const SizedBox(),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
