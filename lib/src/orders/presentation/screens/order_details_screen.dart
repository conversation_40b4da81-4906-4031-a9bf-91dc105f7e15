import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_details/order_details_body.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/special_order_details/special_order_details_body.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class OrderDetailsScreen extends StatelessWidget implements AutoRouteWrapper {
  const OrderDetailsScreen(
      {super.key, required this.isSpecialOrder, required this.orderId});
  final bool isSpecialOrder;
  final int orderId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.orderDetails),
      ),
      body: BlocBuilder<OrdersCubit, OrdersState>(
        buildWhen: (previous, current) => current.maybeWhen(
          orElse: () => false,
          getOrderDetailsLoading: () => true,
          getOrderDetailsFailure: (_) => true,
          getOrderDetailsSuccess: (_) => true,
          getSpecialOrderDetailsSuccess: (_) => true,
        ),
        builder: (context, state) {
          return state.maybeWhen(
            orElse: () => const SizedBox(),
            getOrderDetailsLoading: () =>
                const Center(child: CircularProgressIndicator()),
            getOrderDetailsSuccess: (order) => OrderDetailsBody(order: order),
            getSpecialOrderDetailsSuccess: (specialOrder) =>
                SpecialOrderDetailsBody(specialOrder: specialOrder),
            getOrderDetailsFailure: (failure) => Center(
              child: Text(
                failure,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: isSpecialOrder
            ? (injector<OrdersCubit>()..getSpecialOrderDetails(orderId))
            : (injector<OrdersCubit>()..getOrderDetails(orderId)),
        child: this,
      );
}
