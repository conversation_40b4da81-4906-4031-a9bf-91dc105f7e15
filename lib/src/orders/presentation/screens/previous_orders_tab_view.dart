import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_model.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/empty_orders_content.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_card.dart';
import 'package:flutter/material.dart';

class PreviousOrdersTabView extends StatelessWidget {
  const PreviousOrdersTabView({super.key, required this.orders});
  final List<GetOrdersModel> orders;
  @override
  Widget build(BuildContext context) {
    return orders.isNotEmpty
        ? ListView.separated(
            padding: const EdgeInsets.all(16),
            itemBuilder: (_, index) => OrderCard(
              order: orders[index],
            ),
            separatorBuilder: (_, __) => const SizedBox(height: 8),
            itemCount: orders.length,
          )
        : const EmptyOrdersContent();
  }
}
