import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_refresh_widget.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/empty_orders_content.dart';
import 'package:alsarea_store/src/orders/presentation/widgets/order_card.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CurrentOrdersTabView extends StatelessWidget {
  const CurrentOrdersTabView({super.key, this.token});

  final String? token;

  @override
  Widget build(BuildContext context) {
    final cubit = injector<OrdersCubit>();
    return DefaultTabController(
      length: 5,
      child: BlocBuilder<OrdersCubit, OrdersState>(
        buildWhen: (previous, current) => current.maybeWhen(
          getOrdersSuccess: (_) => true,
          getOrdersLoading: () => true,
          getOrdersFailure: (_) => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          return state.maybeWhen(
            getOrdersLoading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            getOrdersSuccess: (orders) {
              return CustomRefreshWidget(
                onRefresh: () async {
                  await cubit.getOrders();
                },
                onLoading: () async {
                  await cubit.getOrders(isLoadMore: true);
                },
                enablePullUp: true,
                child: CustomScrollView(
                  slivers: [
                    SliverFloatingHeader(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Card(
                          child: TabBar.secondary(
                            indicatorAnimation: TabIndicatorAnimation.elastic,
                            onTap: (value) async {
                              switch (value) {
                                case 0:
                                  cubit.operations.dataWrapper.orderStatusId =
                                      1;
                                  await cubit.getOrders();
                                case 1:
                                  cubit.operations.dataWrapper.orderStatusId =
                                      2;
                                  await cubit.getOrders();
                                case 2:
                                  cubit.operations.dataWrapper.orderStatusId =
                                      3;
                                  await cubit.getOrders();
                                  break;
                                case 3:
                                  cubit.operations.dataWrapper.orderStatusId =
                                      4;
                                  await cubit.getOrders();
                                  break;
                                case 4:
                                  cubit.operations.dataWrapper.orderStatusId =
                                      5;
                                  await cubit.getOrders();
                                  break;
                              }
                            },
                            dividerColor: Colors.transparent,
                            indicatorSize: TabBarIndicatorSize.tab,
                            indicatorPadding: const EdgeInsets.all(4),
                            indicator: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            labelColor: Colors.white,
                            labelPadding: EdgeInsets.zero,
                            overlayColor:
                                WidgetStateProperty.all(Colors.transparent),
                            tabs: [
                              Tab(text: context.l10n.newOrder),
                              Tab(text: context.l10n.reviewed),
                              Tab(text: context.l10n.preparing),
                              Tab(text: context.l10n.prepared),
                              Tab(text: context.l10n.shipping),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (orders.isEmpty)
                      const SliverFillRemaining(
                        hasScrollBody: false,
                        child: EmptyOrdersContent(),
                      )
                    else
                      SliverPadding(
                        padding: const EdgeInsets.all(16),
                        sliver: SliverList.separated(
                          itemBuilder: (_, index) => OrderCard(
                            order: orders[index],
                          ),
                          separatorBuilder: (_, __) =>
                              const SizedBox(height: 8),
                          itemCount: orders.length,
                        ),
                      ),
                  ],
                ),
              );
            },
            getOrdersFailure: (message) {
              return Center(
                child: token == ''
                    ? SizedBox(
                        height: 1.sh,
                        width: .95.sw,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.l10n.pleaseLoginToUnlock,
                              style: TextStyles.title20,
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: .7.sw,
                              child: CustomElevatedButton(
                                child: Text(context.l10n.login),
                                onPressed: () {
                                  context.router.push(const LoginRoute());
                                },
                              ),
                            ),
                          ],
                        ),
                      )
                    : Text(
                        message,
                        style: const TextStyle(color: Colors.red),
                      ),
              );
            },
            orElse: () => const SizedBox.shrink(),
          );
        },
      ),
    );
  }
}
