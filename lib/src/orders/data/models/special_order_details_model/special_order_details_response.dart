import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'special_order_details_response.g.dart';

@JsonSerializable()
class SpecialOrderDetailsResponse extends BaseResponse {
  final SpecialOrderDetailsModel? data;

  SpecialOrderDetailsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory SpecialOrderDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$SpecialOrderDetailsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$SpecialOrderDetailsResponseToJson(this);
}
