// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_order_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SpecialOrderDetailsModelImpl _$$SpecialOrderDetailsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SpecialOrderDetailsModelImpl(
      id: (json['id'] as num?)?.toInt(),
      dateOrdered: json['dateOrdered'] == null
          ? null
          : DateTime.parse(json['dateOrdered'] as String),
      customerName: json['customerName'] as String?,
      customerPhone: json['customerPhone'] as String?,
      deliveryValue: (json['deliveryValue'] as num?)?.toDouble(),
      total: (json['total'] as num?)?.toDouble(),
      isSpecialOrder: json['isSpecialOrder'] as bool?,
      paymentMethod: json['paymentMethod'] as String?,
      image: json['image'] as String?,
      note: json['note'] as String?,
      subTotal: (json['subTotal'] as num?)?.toDouble(),
      moneyAddedToCustomerWallet:
          (json['moneyAddedToCustomerWallet'] as num?)?.toDouble(),
      trakingOrder: (json['trakingOrder'] as List<dynamic>?)
          ?.map((e) => TrakingOrder.fromJson(e as Map<String, dynamic>))
          .toList(),
      customerAddress: json['customerAddress'] == null
          ? null
          : CustomerAddress.fromJson(
              json['customerAddress'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpecialOrderDetailsModelImplToJson(
        _$SpecialOrderDetailsModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.dateOrdered?.toIso8601String() case final value?)
        'dateOrdered': value,
      if (instance.customerName case final value?) 'customerName': value,
      if (instance.customerPhone case final value?) 'customerPhone': value,
      if (instance.deliveryValue case final value?) 'deliveryValue': value,
      if (instance.total case final value?) 'total': value,
      if (instance.isSpecialOrder case final value?) 'isSpecialOrder': value,
      if (instance.paymentMethod case final value?) 'paymentMethod': value,
      if (instance.image case final value?) 'image': value,
      if (instance.note case final value?) 'note': value,
      if (instance.subTotal case final value?) 'subTotal': value,
      if (instance.moneyAddedToCustomerWallet case final value?)
        'moneyAddedToCustomerWallet': value,
      if (instance.trakingOrder case final value?) 'trakingOrder': value,
      if (instance.customerAddress case final value?) 'customerAddress': value,
    };
