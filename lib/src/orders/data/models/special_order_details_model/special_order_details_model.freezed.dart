// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'special_order_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SpecialOrderDetailsModel _$SpecialOrderDetailsModelFromJson(
    Map<String, dynamic> json) {
  return _SpecialOrderDetailsModel.fromJson(json);
}

/// @nodoc
mixin _$SpecialOrderDetailsModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "dateOrdered")
  DateTime? get dateOrdered => throw _privateConstructorUsedError;
  @JsonKey(name: "customerName")
  String? get customerName => throw _privateConstructorUsedError;
  @JsonKey(name: "customerPhone")
  String? get customerPhone => throw _privateConstructorUsedError;
  @JsonKey(name: "deliveryValue")
  double? get deliveryValue => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder => throw _privateConstructorUsedError;
  @JsonKey(name: "paymentMethod")
  String? get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: "image")
  String? get image => throw _privateConstructorUsedError;
  @JsonKey(name: "note")
  String? get note => throw _privateConstructorUsedError;
  @JsonKey(name: "subTotal")
  double? get subTotal => throw _privateConstructorUsedError;
  @JsonKey(name: "moneyAddedToCustomerWallet")
  double? get moneyAddedToCustomerWallet => throw _privateConstructorUsedError;
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder => throw _privateConstructorUsedError;
  @JsonKey(name: "customerAddress")
  CustomerAddress? get customerAddress => throw _privateConstructorUsedError;

  /// Serializes this SpecialOrderDetailsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SpecialOrderDetailsModelCopyWith<SpecialOrderDetailsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialOrderDetailsModelCopyWith<$Res> {
  factory $SpecialOrderDetailsModelCopyWith(SpecialOrderDetailsModel value,
          $Res Function(SpecialOrderDetailsModel) then) =
      _$SpecialOrderDetailsModelCopyWithImpl<$Res, SpecialOrderDetailsModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "dateOrdered") DateTime? dateOrdered,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "customerPhone") String? customerPhone,
      @JsonKey(name: "deliveryValue") double? deliveryValue,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "paymentMethod") String? paymentMethod,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "note") String? note,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      double? moneyAddedToCustomerWallet,
      @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder,
      @JsonKey(name: "customerAddress") CustomerAddress? customerAddress});

  $CustomerAddressCopyWith<$Res>? get customerAddress;
}

/// @nodoc
class _$SpecialOrderDetailsModelCopyWithImpl<$Res,
        $Val extends SpecialOrderDetailsModel>
    implements $SpecialOrderDetailsModelCopyWith<$Res> {
  _$SpecialOrderDetailsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? dateOrdered = freezed,
    Object? customerName = freezed,
    Object? customerPhone = freezed,
    Object? deliveryValue = freezed,
    Object? total = freezed,
    Object? isSpecialOrder = freezed,
    Object? paymentMethod = freezed,
    Object? image = freezed,
    Object? note = freezed,
    Object? subTotal = freezed,
    Object? moneyAddedToCustomerWallet = freezed,
    Object? trakingOrder = freezed,
    Object? customerAddress = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      dateOrdered: freezed == dateOrdered
          ? _value.dateOrdered
          : dateOrdered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      moneyAddedToCustomerWallet: freezed == moneyAddedToCustomerWallet
          ? _value.moneyAddedToCustomerWallet
          : moneyAddedToCustomerWallet // ignore: cast_nullable_to_non_nullable
              as double?,
      trakingOrder: freezed == trakingOrder
          ? _value.trakingOrder
          : trakingOrder // ignore: cast_nullable_to_non_nullable
              as List<TrakingOrder>?,
      customerAddress: freezed == customerAddress
          ? _value.customerAddress
          : customerAddress // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
    ) as $Val);
  }

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerAddressCopyWith<$Res>? get customerAddress {
    if (_value.customerAddress == null) {
      return null;
    }

    return $CustomerAddressCopyWith<$Res>(_value.customerAddress!, (value) {
      return _then(_value.copyWith(customerAddress: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpecialOrderDetailsModelImplCopyWith<$Res>
    implements $SpecialOrderDetailsModelCopyWith<$Res> {
  factory _$$SpecialOrderDetailsModelImplCopyWith(
          _$SpecialOrderDetailsModelImpl value,
          $Res Function(_$SpecialOrderDetailsModelImpl) then) =
      __$$SpecialOrderDetailsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "dateOrdered") DateTime? dateOrdered,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "customerPhone") String? customerPhone,
      @JsonKey(name: "deliveryValue") double? deliveryValue,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "paymentMethod") String? paymentMethod,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "note") String? note,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      double? moneyAddedToCustomerWallet,
      @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder,
      @JsonKey(name: "customerAddress") CustomerAddress? customerAddress});

  @override
  $CustomerAddressCopyWith<$Res>? get customerAddress;
}

/// @nodoc
class __$$SpecialOrderDetailsModelImplCopyWithImpl<$Res>
    extends _$SpecialOrderDetailsModelCopyWithImpl<$Res,
        _$SpecialOrderDetailsModelImpl>
    implements _$$SpecialOrderDetailsModelImplCopyWith<$Res> {
  __$$SpecialOrderDetailsModelImplCopyWithImpl(
      _$SpecialOrderDetailsModelImpl _value,
      $Res Function(_$SpecialOrderDetailsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? dateOrdered = freezed,
    Object? customerName = freezed,
    Object? customerPhone = freezed,
    Object? deliveryValue = freezed,
    Object? total = freezed,
    Object? isSpecialOrder = freezed,
    Object? paymentMethod = freezed,
    Object? image = freezed,
    Object? note = freezed,
    Object? subTotal = freezed,
    Object? moneyAddedToCustomerWallet = freezed,
    Object? trakingOrder = freezed,
    Object? customerAddress = freezed,
  }) {
    return _then(_$SpecialOrderDetailsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      dateOrdered: freezed == dateOrdered
          ? _value.dateOrdered
          : dateOrdered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      moneyAddedToCustomerWallet: freezed == moneyAddedToCustomerWallet
          ? _value.moneyAddedToCustomerWallet
          : moneyAddedToCustomerWallet // ignore: cast_nullable_to_non_nullable
              as double?,
      trakingOrder: freezed == trakingOrder
          ? _value._trakingOrder
          : trakingOrder // ignore: cast_nullable_to_non_nullable
              as List<TrakingOrder>?,
      customerAddress: freezed == customerAddress
          ? _value.customerAddress
          : customerAddress // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialOrderDetailsModelImpl implements _SpecialOrderDetailsModel {
  const _$SpecialOrderDetailsModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "dateOrdered") this.dateOrdered,
      @JsonKey(name: "customerName") this.customerName,
      @JsonKey(name: "customerPhone") this.customerPhone,
      @JsonKey(name: "deliveryValue") this.deliveryValue,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "isSpecialOrder") this.isSpecialOrder,
      @JsonKey(name: "paymentMethod") this.paymentMethod,
      @JsonKey(name: "image") this.image,
      @JsonKey(name: "note") this.note,
      @JsonKey(name: "subTotal") this.subTotal,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      this.moneyAddedToCustomerWallet,
      @JsonKey(name: "trakingOrder") final List<TrakingOrder>? trakingOrder,
      @JsonKey(name: "customerAddress") this.customerAddress})
      : _trakingOrder = trakingOrder;

  factory _$SpecialOrderDetailsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialOrderDetailsModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "dateOrdered")
  final DateTime? dateOrdered;
  @override
  @JsonKey(name: "customerName")
  final String? customerName;
  @override
  @JsonKey(name: "customerPhone")
  final String? customerPhone;
  @override
  @JsonKey(name: "deliveryValue")
  final double? deliveryValue;
  @override
  @JsonKey(name: "total")
  final double? total;
  @override
  @JsonKey(name: "isSpecialOrder")
  final bool? isSpecialOrder;
  @override
  @JsonKey(name: "paymentMethod")
  final String? paymentMethod;
  @override
  @JsonKey(name: "image")
  final String? image;
  @override
  @JsonKey(name: "note")
  final String? note;
  @override
  @JsonKey(name: "subTotal")
  final double? subTotal;
  @override
  @JsonKey(name: "moneyAddedToCustomerWallet")
  final double? moneyAddedToCustomerWallet;
  final List<TrakingOrder>? _trakingOrder;
  @override
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder {
    final value = _trakingOrder;
    if (value == null) return null;
    if (_trakingOrder is EqualUnmodifiableListView) return _trakingOrder;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "customerAddress")
  final CustomerAddress? customerAddress;

  @override
  String toString() {
    return 'SpecialOrderDetailsModel(id: $id, dateOrdered: $dateOrdered, customerName: $customerName, customerPhone: $customerPhone, deliveryValue: $deliveryValue, total: $total, isSpecialOrder: $isSpecialOrder, paymentMethod: $paymentMethod, image: $image, note: $note, subTotal: $subTotal, moneyAddedToCustomerWallet: $moneyAddedToCustomerWallet, trakingOrder: $trakingOrder, customerAddress: $customerAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialOrderDetailsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.dateOrdered, dateOrdered) ||
                other.dateOrdered == dateOrdered) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerPhone, customerPhone) ||
                other.customerPhone == customerPhone) &&
            (identical(other.deliveryValue, deliveryValue) ||
                other.deliveryValue == deliveryValue) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.isSpecialOrder, isSpecialOrder) ||
                other.isSpecialOrder == isSpecialOrder) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.subTotal, subTotal) ||
                other.subTotal == subTotal) &&
            (identical(other.moneyAddedToCustomerWallet,
                    moneyAddedToCustomerWallet) ||
                other.moneyAddedToCustomerWallet ==
                    moneyAddedToCustomerWallet) &&
            const DeepCollectionEquality()
                .equals(other._trakingOrder, _trakingOrder) &&
            (identical(other.customerAddress, customerAddress) ||
                other.customerAddress == customerAddress));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      dateOrdered,
      customerName,
      customerPhone,
      deliveryValue,
      total,
      isSpecialOrder,
      paymentMethod,
      image,
      note,
      subTotal,
      moneyAddedToCustomerWallet,
      const DeepCollectionEquality().hash(_trakingOrder),
      customerAddress);

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialOrderDetailsModelImplCopyWith<_$SpecialOrderDetailsModelImpl>
      get copyWith => __$$SpecialOrderDetailsModelImplCopyWithImpl<
          _$SpecialOrderDetailsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialOrderDetailsModelImplToJson(
      this,
    );
  }
}

abstract class _SpecialOrderDetailsModel implements SpecialOrderDetailsModel {
  const factory _SpecialOrderDetailsModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "dateOrdered") final DateTime? dateOrdered,
      @JsonKey(name: "customerName") final String? customerName,
      @JsonKey(name: "customerPhone") final String? customerPhone,
      @JsonKey(name: "deliveryValue") final double? deliveryValue,
      @JsonKey(name: "total") final double? total,
      @JsonKey(name: "isSpecialOrder") final bool? isSpecialOrder,
      @JsonKey(name: "paymentMethod") final String? paymentMethod,
      @JsonKey(name: "image") final String? image,
      @JsonKey(name: "note") final String? note,
      @JsonKey(name: "subTotal") final double? subTotal,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      final double? moneyAddedToCustomerWallet,
      @JsonKey(name: "trakingOrder") final List<TrakingOrder>? trakingOrder,
      @JsonKey(name: "customerAddress")
      final CustomerAddress? customerAddress}) = _$SpecialOrderDetailsModelImpl;

  factory _SpecialOrderDetailsModel.fromJson(Map<String, dynamic> json) =
      _$SpecialOrderDetailsModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "dateOrdered")
  DateTime? get dateOrdered;
  @override
  @JsonKey(name: "customerName")
  String? get customerName;
  @override
  @JsonKey(name: "customerPhone")
  String? get customerPhone;
  @override
  @JsonKey(name: "deliveryValue")
  double? get deliveryValue;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder;
  @override
  @JsonKey(name: "paymentMethod")
  String? get paymentMethod;
  @override
  @JsonKey(name: "image")
  String? get image;
  @override
  @JsonKey(name: "note")
  String? get note;
  @override
  @JsonKey(name: "subTotal")
  double? get subTotal;
  @override
  @JsonKey(name: "moneyAddedToCustomerWallet")
  double? get moneyAddedToCustomerWallet;
  @override
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder;
  @override
  @JsonKey(name: "customerAddress")
  CustomerAddress? get customerAddress;

  /// Create a copy of SpecialOrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpecialOrderDetailsModelImplCopyWith<_$SpecialOrderDetailsModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
