// To parse this JSON data, do
//
//     final specialOrderDetailsModel = specialOrderDetailsModelFromJson(jsonString);

import 'dart:convert';

import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'special_order_details_model.freezed.dart';
part 'special_order_details_model.g.dart';

SpecialOrderDetailsModel specialOrderDetailsModelFromJson(String str) =>
    SpecialOrderDetailsModel.fromJson(json.decode(str));

String specialOrderDetailsModelToJson(SpecialOrderDetailsModel data) =>
    json.encode(data.toJson());

@freezed
class SpecialOrderDetailsModel with _$SpecialOrderDetailsModel {
  const factory SpecialOrderDetailsModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "dateOrdered") DateTime? dateOrdered,
    @Json<PERSON>ey(name: "customerName") String? customerName,
    @Json<PERSON>ey(name: "customerPhone") String? customerPhone,
    @JsonKey(name: "deliveryValue") double? deliveryValue,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
    @JsonKey(name: "paymentMethod") String? paymentMethod,
    @JsonKey(name: "image") String? image,
    @JsonKey(name: "note") String? note,
    @JsonKey(name: "subTotal") double? subTotal,
    @JsonKey(name: "moneyAddedToCustomerWallet")
    double? moneyAddedToCustomerWallet,
    @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder,
    @JsonKey(name: "customerAddress") CustomerAddress? customerAddress,
  }) = _SpecialOrderDetailsModel;

  factory SpecialOrderDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$SpecialOrderDetailsModelFromJson(json);
}
