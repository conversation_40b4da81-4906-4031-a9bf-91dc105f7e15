// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_orders_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetOrdersParams _$GetOrdersParamsFromJson(Map<String, dynamic> json) {
  return _GetOrdersParams.fromJson(json);
}

/// @nodoc
mixin _$GetOrdersParams {
  @JsonKey(name: "orderStatusId")
  int? get orderStatusId => throw _privateConstructorUsedError;
  @JsonKey(name: "skip")
  int? get skip => throw _privateConstructorUsedError;
  @JsonKey(name: "take")
  int? get take => throw _privateConstructorUsedError;

  /// Serializes this GetOrdersParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetOrdersParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetOrdersParamsCopyWith<GetOrdersParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetOrdersParamsCopyWith<$Res> {
  factory $GetOrdersParamsCopyWith(
          GetOrdersParams value, $Res Function(GetOrdersParams) then) =
      _$GetOrdersParamsCopyWithImpl<$Res, GetOrdersParams>;
  @useResult
  $Res call(
      {@JsonKey(name: "orderStatusId") int? orderStatusId,
      @JsonKey(name: "skip") int? skip,
      @JsonKey(name: "take") int? take});
}

/// @nodoc
class _$GetOrdersParamsCopyWithImpl<$Res, $Val extends GetOrdersParams>
    implements $GetOrdersParamsCopyWith<$Res> {
  _$GetOrdersParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetOrdersParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderStatusId = freezed,
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_value.copyWith(
      orderStatusId: freezed == orderStatusId
          ? _value.orderStatusId
          : orderStatusId // ignore: cast_nullable_to_non_nullable
              as int?,
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetOrdersParamsImplCopyWith<$Res>
    implements $GetOrdersParamsCopyWith<$Res> {
  factory _$$GetOrdersParamsImplCopyWith(_$GetOrdersParamsImpl value,
          $Res Function(_$GetOrdersParamsImpl) then) =
      __$$GetOrdersParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "orderStatusId") int? orderStatusId,
      @JsonKey(name: "skip") int? skip,
      @JsonKey(name: "take") int? take});
}

/// @nodoc
class __$$GetOrdersParamsImplCopyWithImpl<$Res>
    extends _$GetOrdersParamsCopyWithImpl<$Res, _$GetOrdersParamsImpl>
    implements _$$GetOrdersParamsImplCopyWith<$Res> {
  __$$GetOrdersParamsImplCopyWithImpl(
      _$GetOrdersParamsImpl _value, $Res Function(_$GetOrdersParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetOrdersParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderStatusId = freezed,
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_$GetOrdersParamsImpl(
      orderStatusId: freezed == orderStatusId
          ? _value.orderStatusId
          : orderStatusId // ignore: cast_nullable_to_non_nullable
              as int?,
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetOrdersParamsImpl implements _GetOrdersParams {
  const _$GetOrdersParamsImpl(
      {@JsonKey(name: "orderStatusId") this.orderStatusId,
      @JsonKey(name: "skip") this.skip,
      @JsonKey(name: "take") this.take});

  factory _$GetOrdersParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetOrdersParamsImplFromJson(json);

  @override
  @JsonKey(name: "orderStatusId")
  final int? orderStatusId;
  @override
  @JsonKey(name: "skip")
  final int? skip;
  @override
  @JsonKey(name: "take")
  final int? take;

  @override
  String toString() {
    return 'GetOrdersParams(orderStatusId: $orderStatusId, skip: $skip, take: $take)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrdersParamsImpl &&
            (identical(other.orderStatusId, orderStatusId) ||
                other.orderStatusId == orderStatusId) &&
            (identical(other.skip, skip) || other.skip == skip) &&
            (identical(other.take, take) || other.take == take));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, orderStatusId, skip, take);

  /// Create a copy of GetOrdersParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrdersParamsImplCopyWith<_$GetOrdersParamsImpl> get copyWith =>
      __$$GetOrdersParamsImplCopyWithImpl<_$GetOrdersParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetOrdersParamsImplToJson(
      this,
    );
  }
}

abstract class _GetOrdersParams implements GetOrdersParams {
  const factory _GetOrdersParams(
      {@JsonKey(name: "orderStatusId") final int? orderStatusId,
      @JsonKey(name: "skip") final int? skip,
      @JsonKey(name: "take") final int? take}) = _$GetOrdersParamsImpl;

  factory _GetOrdersParams.fromJson(Map<String, dynamic> json) =
      _$GetOrdersParamsImpl.fromJson;

  @override
  @JsonKey(name: "orderStatusId")
  int? get orderStatusId;
  @override
  @JsonKey(name: "skip")
  int? get skip;
  @override
  @JsonKey(name: "take")
  int? get take;

  /// Create a copy of GetOrdersParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrdersParamsImplCopyWith<_$GetOrdersParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
