// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_orders_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GetOrdersModelImpl _$$GetOrdersModelImplFromJson(Map<String, dynamic> json) =>
    _$GetOrdersModelImpl(
      id: (json['id'] as num?)?.toInt(),
      isSpecialOrder: json['isSpecialOrder'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      city: json['city'] as String?,
      customerName: json['customerName'] as String?,
      total: (json['total'] as num?)?.toInt(),
      subTotal: (json['subTotal'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GetOrdersModelImplToJson(
        _$GetOrdersModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.isSpecialOrder case final value?) 'isSpecialOrder': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.city case final value?) 'city': value,
      if (instance.customerName case final value?) 'customerName': value,
      if (instance.total case final value?) 'total': value,
      if (instance.subTotal case final value?) 'subTotal': value,
    };
