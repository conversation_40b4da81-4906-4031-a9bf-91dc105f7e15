// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_orders_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GetOrdersParamsImpl _$$GetOrdersParamsImplFromJson(
        Map<String, dynamic> json) =>
    _$GetOrdersParamsImpl(
      orderStatusId: (json['orderStatusId'] as num?)?.toInt(),
      skip: (json['skip'] as num?)?.toInt(),
      take: (json['take'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GetOrdersParamsImplToJson(
        _$GetOrdersParamsImpl instance) =>
    <String, dynamic>{
      if (instance.orderStatusId case final value?) 'orderStatusId': value,
      if (instance.skip case final value?) 'skip': value,
      if (instance.take case final value?) 'take': value,
    };
