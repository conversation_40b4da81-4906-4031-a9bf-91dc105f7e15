// To parse this JSON data, do
//
//     final getOrdersParams = getOrdersParamsFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_orders_params.freezed.dart';
part 'get_orders_params.g.dart';

GetOrdersParams getOrdersParamsFromJson(String str) =>
    GetOrdersParams.fromJson(json.decode(str));

String getOrdersParamsToJson(GetOrdersParams data) =>
    json.encode(data.toJson());

@freezed
class GetOrdersParams with _$GetOrdersParams {
  const factory GetOrdersParams({
    @JsonKey(name: "orderStatusId") int? orderStatusId,
    @JsonKey(name: "skip") int? skip,
    @Json<PERSON>ey(name: "take") int? take,
  }) = _GetOrdersParams;

  factory GetOrdersParams.fromJson(Map<String, dynamic> json) =>
      _$GetOrdersParamsFromJson(json);
}
