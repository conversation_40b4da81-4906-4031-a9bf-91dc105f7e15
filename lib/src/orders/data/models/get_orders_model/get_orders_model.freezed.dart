// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_orders_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetOrdersModel _$GetOrdersModelFromJson(Map<String, dynamic> json) {
  return _GetOrdersModel.fromJson(json);
}

/// @nodoc
mixin _$GetOrdersModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder => throw _privateConstructorUsedError;
  @JsonKey(name: "createdAt")
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "city")
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: "customerName")
  String? get customerName => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  int? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "subTotal")
  int? get subTotal => throw _privateConstructorUsedError;

  /// Serializes this GetOrdersModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetOrdersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetOrdersModelCopyWith<GetOrdersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetOrdersModelCopyWith<$Res> {
  factory $GetOrdersModelCopyWith(
          GetOrdersModel value, $Res Function(GetOrdersModel) then) =
      _$GetOrdersModelCopyWithImpl<$Res, GetOrdersModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "createdAt") DateTime? createdAt,
      @JsonKey(name: "city") String? city,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "total") int? total,
      @JsonKey(name: "subTotal") int? subTotal});
}

/// @nodoc
class _$GetOrdersModelCopyWithImpl<$Res, $Val extends GetOrdersModel>
    implements $GetOrdersModelCopyWith<$Res> {
  _$GetOrdersModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetOrdersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? isSpecialOrder = freezed,
    Object? createdAt = freezed,
    Object? city = freezed,
    Object? customerName = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetOrdersModelImplCopyWith<$Res>
    implements $GetOrdersModelCopyWith<$Res> {
  factory _$$GetOrdersModelImplCopyWith(_$GetOrdersModelImpl value,
          $Res Function(_$GetOrdersModelImpl) then) =
      __$$GetOrdersModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "createdAt") DateTime? createdAt,
      @JsonKey(name: "city") String? city,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "total") int? total,
      @JsonKey(name: "subTotal") int? subTotal});
}

/// @nodoc
class __$$GetOrdersModelImplCopyWithImpl<$Res>
    extends _$GetOrdersModelCopyWithImpl<$Res, _$GetOrdersModelImpl>
    implements _$$GetOrdersModelImplCopyWith<$Res> {
  __$$GetOrdersModelImplCopyWithImpl(
      _$GetOrdersModelImpl _value, $Res Function(_$GetOrdersModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetOrdersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? isSpecialOrder = freezed,
    Object? createdAt = freezed,
    Object? city = freezed,
    Object? customerName = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
  }) {
    return _then(_$GetOrdersModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetOrdersModelImpl implements _GetOrdersModel {
  const _$GetOrdersModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "isSpecialOrder") this.isSpecialOrder,
      @JsonKey(name: "createdAt") this.createdAt,
      @JsonKey(name: "city") this.city,
      @JsonKey(name: "customerName") this.customerName,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "subTotal") this.subTotal});

  factory _$GetOrdersModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetOrdersModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "isSpecialOrder")
  final bool? isSpecialOrder;
  @override
  @JsonKey(name: "createdAt")
  final DateTime? createdAt;
  @override
  @JsonKey(name: "city")
  final String? city;
  @override
  @JsonKey(name: "customerName")
  final String? customerName;
  @override
  @JsonKey(name: "total")
  final int? total;
  @override
  @JsonKey(name: "subTotal")
  final int? subTotal;

  @override
  String toString() {
    return 'GetOrdersModel(id: $id, isSpecialOrder: $isSpecialOrder, createdAt: $createdAt, city: $city, customerName: $customerName, total: $total, subTotal: $subTotal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetOrdersModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isSpecialOrder, isSpecialOrder) ||
                other.isSpecialOrder == isSpecialOrder) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.subTotal, subTotal) ||
                other.subTotal == subTotal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, isSpecialOrder, createdAt,
      city, customerName, total, subTotal);

  /// Create a copy of GetOrdersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetOrdersModelImplCopyWith<_$GetOrdersModelImpl> get copyWith =>
      __$$GetOrdersModelImplCopyWithImpl<_$GetOrdersModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetOrdersModelImplToJson(
      this,
    );
  }
}

abstract class _GetOrdersModel implements GetOrdersModel {
  const factory _GetOrdersModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "isSpecialOrder") final bool? isSpecialOrder,
      @JsonKey(name: "createdAt") final DateTime? createdAt,
      @JsonKey(name: "city") final String? city,
      @JsonKey(name: "customerName") final String? customerName,
      @JsonKey(name: "total") final int? total,
      @JsonKey(name: "subTotal") final int? subTotal}) = _$GetOrdersModelImpl;

  factory _GetOrdersModel.fromJson(Map<String, dynamic> json) =
      _$GetOrdersModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder;
  @override
  @JsonKey(name: "createdAt")
  DateTime? get createdAt;
  @override
  @JsonKey(name: "city")
  String? get city;
  @override
  @JsonKey(name: "customerName")
  String? get customerName;
  @override
  @JsonKey(name: "total")
  int? get total;
  @override
  @JsonKey(name: "subTotal")
  int? get subTotal;

  /// Create a copy of GetOrdersModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetOrdersModelImplCopyWith<_$GetOrdersModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
