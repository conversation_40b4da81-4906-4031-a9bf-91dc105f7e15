import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_orders_response.g.dart';

@JsonSerializable()
class GetOrdersResponse extends BaseResponse {
  final List<GetOrdersModel>? data;

  GetOrdersResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetOrdersResponse.fromJson(Map<String, dynamic> json) =>
      _$GetOrdersResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetOrdersResponseToJson(this);
}
