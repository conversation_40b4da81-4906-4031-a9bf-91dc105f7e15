// To parse this JSON data, do
//
//     final getOrdersModel = getOrdersModelFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_orders_model.freezed.dart';
part 'get_orders_model.g.dart';

GetOrdersModel getOrdersModelFromJson(String str) =>
    GetOrdersModel.fromJson(json.decode(str));

String getOrdersModelToJson(GetOrdersModel data) => json.encode(data.toJson());

@freezed
class GetOrdersModel with _$GetOrdersModel {
  const factory GetOrdersModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON><PERSON>(name: "isSpecialOrder") bool? isSpecialOrder,
    @<PERSON>son<PERSON>ey(name: "createdAt") DateTime? createdAt,
    @JsonKey(name: "city") String? city,
    @Json<PERSON>ey(name: "customerName") String? customerName,
    @<PERSON>son<PERSON>ey(name: "total") int? total,
    @JsonKey(name: "subTotal") int? subTotal,
  }) = _GetOrdersModel;

  factory GetOrdersModel.fromJson(Map<String, dynamic> json) =>
      _$GetOrdersModelFromJson(json);
}
