// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderDetailsModel _$OrderDetailsModelFromJson(Map<String, dynamic> json) {
  return _OrderDetailsModel.fromJson(json);
}

/// @nodoc
mixin _$OrderDetailsModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "dateOrdered")
  DateTime? get dateOrdered => throw _privateConstructorUsedError;
  @JsonKey(name: "scheduleDate")
  dynamic get scheduleDate => throw _privateConstructorUsedError;
  @JsonKey(name: "customerName")
  String? get customerName => throw _privateConstructorUsedError;
  @JsonKey(name: "customerPhone")
  String? get customerPhone => throw _privateConstructorUsedError;
  @JsonKey(name: "paymentMethod")
  String? get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder => throw _privateConstructorUsedError;
  @JsonKey(name: "subTotal")
  double? get subTotal => throw _privateConstructorUsedError;
  @JsonKey(name: "couponValue")
  double? get couponValue => throw _privateConstructorUsedError;
  @JsonKey(name: "walletDiscount")
  double? get walletDiscount => throw _privateConstructorUsedError;
  @JsonKey(name: "moneyAddedToCustomerWallet")
  dynamic get moneyAddedToCustomerWallet => throw _privateConstructorUsedError;
  @JsonKey(name: "deliveryValue")
  double? get deliveryValue => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "customerAddress")
  CustomerAddress? get customerAddress => throw _privateConstructorUsedError;
  @JsonKey(name: "orderStores")
  List<OrderStore>? get orderStores => throw _privateConstructorUsedError;
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder => throw _privateConstructorUsedError;

  /// Serializes this OrderDetailsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderDetailsModelCopyWith<OrderDetailsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderDetailsModelCopyWith<$Res> {
  factory $OrderDetailsModelCopyWith(
          OrderDetailsModel value, $Res Function(OrderDetailsModel) then) =
      _$OrderDetailsModelCopyWithImpl<$Res, OrderDetailsModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "dateOrdered") DateTime? dateOrdered,
      @JsonKey(name: "scheduleDate") dynamic scheduleDate,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "customerPhone") String? customerPhone,
      @JsonKey(name: "paymentMethod") String? paymentMethod,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "couponValue") double? couponValue,
      @JsonKey(name: "walletDiscount") double? walletDiscount,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      dynamic moneyAddedToCustomerWallet,
      @JsonKey(name: "deliveryValue") double? deliveryValue,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "customerAddress") CustomerAddress? customerAddress,
      @JsonKey(name: "orderStores") List<OrderStore>? orderStores,
      @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder});

  $CustomerAddressCopyWith<$Res>? get customerAddress;
}

/// @nodoc
class _$OrderDetailsModelCopyWithImpl<$Res, $Val extends OrderDetailsModel>
    implements $OrderDetailsModelCopyWith<$Res> {
  _$OrderDetailsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? dateOrdered = freezed,
    Object? scheduleDate = freezed,
    Object? customerName = freezed,
    Object? customerPhone = freezed,
    Object? paymentMethod = freezed,
    Object? isSpecialOrder = freezed,
    Object? subTotal = freezed,
    Object? couponValue = freezed,
    Object? walletDiscount = freezed,
    Object? moneyAddedToCustomerWallet = freezed,
    Object? deliveryValue = freezed,
    Object? total = freezed,
    Object? customerAddress = freezed,
    Object? orderStores = freezed,
    Object? trakingOrder = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      dateOrdered: freezed == dateOrdered
          ? _value.dateOrdered
          : dateOrdered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      scheduleDate: freezed == scheduleDate
          ? _value.scheduleDate
          : scheduleDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      couponValue: freezed == couponValue
          ? _value.couponValue
          : couponValue // ignore: cast_nullable_to_non_nullable
              as double?,
      walletDiscount: freezed == walletDiscount
          ? _value.walletDiscount
          : walletDiscount // ignore: cast_nullable_to_non_nullable
              as double?,
      moneyAddedToCustomerWallet: freezed == moneyAddedToCustomerWallet
          ? _value.moneyAddedToCustomerWallet
          : moneyAddedToCustomerWallet // ignore: cast_nullable_to_non_nullable
              as dynamic,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      customerAddress: freezed == customerAddress
          ? _value.customerAddress
          : customerAddress // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
      orderStores: freezed == orderStores
          ? _value.orderStores
          : orderStores // ignore: cast_nullable_to_non_nullable
              as List<OrderStore>?,
      trakingOrder: freezed == trakingOrder
          ? _value.trakingOrder
          : trakingOrder // ignore: cast_nullable_to_non_nullable
              as List<TrakingOrder>?,
    ) as $Val);
  }

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerAddressCopyWith<$Res>? get customerAddress {
    if (_value.customerAddress == null) {
      return null;
    }

    return $CustomerAddressCopyWith<$Res>(_value.customerAddress!, (value) {
      return _then(_value.copyWith(customerAddress: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderDetailsModelImplCopyWith<$Res>
    implements $OrderDetailsModelCopyWith<$Res> {
  factory _$$OrderDetailsModelImplCopyWith(_$OrderDetailsModelImpl value,
          $Res Function(_$OrderDetailsModelImpl) then) =
      __$$OrderDetailsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "dateOrdered") DateTime? dateOrdered,
      @JsonKey(name: "scheduleDate") dynamic scheduleDate,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "customerPhone") String? customerPhone,
      @JsonKey(name: "paymentMethod") String? paymentMethod,
      @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "couponValue") double? couponValue,
      @JsonKey(name: "walletDiscount") double? walletDiscount,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      dynamic moneyAddedToCustomerWallet,
      @JsonKey(name: "deliveryValue") double? deliveryValue,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "customerAddress") CustomerAddress? customerAddress,
      @JsonKey(name: "orderStores") List<OrderStore>? orderStores,
      @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder});

  @override
  $CustomerAddressCopyWith<$Res>? get customerAddress;
}

/// @nodoc
class __$$OrderDetailsModelImplCopyWithImpl<$Res>
    extends _$OrderDetailsModelCopyWithImpl<$Res, _$OrderDetailsModelImpl>
    implements _$$OrderDetailsModelImplCopyWith<$Res> {
  __$$OrderDetailsModelImplCopyWithImpl(_$OrderDetailsModelImpl _value,
      $Res Function(_$OrderDetailsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? dateOrdered = freezed,
    Object? scheduleDate = freezed,
    Object? customerName = freezed,
    Object? customerPhone = freezed,
    Object? paymentMethod = freezed,
    Object? isSpecialOrder = freezed,
    Object? subTotal = freezed,
    Object? couponValue = freezed,
    Object? walletDiscount = freezed,
    Object? moneyAddedToCustomerWallet = freezed,
    Object? deliveryValue = freezed,
    Object? total = freezed,
    Object? customerAddress = freezed,
    Object? orderStores = freezed,
    Object? trakingOrder = freezed,
  }) {
    return _then(_$OrderDetailsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      dateOrdered: freezed == dateOrdered
          ? _value.dateOrdered
          : dateOrdered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      scheduleDate: freezed == scheduleDate
          ? _value.scheduleDate
          : scheduleDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      isSpecialOrder: freezed == isSpecialOrder
          ? _value.isSpecialOrder
          : isSpecialOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      couponValue: freezed == couponValue
          ? _value.couponValue
          : couponValue // ignore: cast_nullable_to_non_nullable
              as double?,
      walletDiscount: freezed == walletDiscount
          ? _value.walletDiscount
          : walletDiscount // ignore: cast_nullable_to_non_nullable
              as double?,
      moneyAddedToCustomerWallet: freezed == moneyAddedToCustomerWallet
          ? _value.moneyAddedToCustomerWallet
          : moneyAddedToCustomerWallet // ignore: cast_nullable_to_non_nullable
              as dynamic,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      customerAddress: freezed == customerAddress
          ? _value.customerAddress
          : customerAddress // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
      orderStores: freezed == orderStores
          ? _value._orderStores
          : orderStores // ignore: cast_nullable_to_non_nullable
              as List<OrderStore>?,
      trakingOrder: freezed == trakingOrder
          ? _value._trakingOrder
          : trakingOrder // ignore: cast_nullable_to_non_nullable
              as List<TrakingOrder>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderDetailsModelImpl implements _OrderDetailsModel {
  const _$OrderDetailsModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "dateOrdered") this.dateOrdered,
      @JsonKey(name: "scheduleDate") this.scheduleDate,
      @JsonKey(name: "customerName") this.customerName,
      @JsonKey(name: "customerPhone") this.customerPhone,
      @JsonKey(name: "paymentMethod") this.paymentMethod,
      @JsonKey(name: "isSpecialOrder") this.isSpecialOrder,
      @JsonKey(name: "subTotal") this.subTotal,
      @JsonKey(name: "couponValue") this.couponValue,
      @JsonKey(name: "walletDiscount") this.walletDiscount,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      this.moneyAddedToCustomerWallet,
      @JsonKey(name: "deliveryValue") this.deliveryValue,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "customerAddress") this.customerAddress,
      @JsonKey(name: "orderStores") final List<OrderStore>? orderStores,
      @JsonKey(name: "trakingOrder") final List<TrakingOrder>? trakingOrder})
      : _orderStores = orderStores,
        _trakingOrder = trakingOrder;

  factory _$OrderDetailsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderDetailsModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "dateOrdered")
  final DateTime? dateOrdered;
  @override
  @JsonKey(name: "scheduleDate")
  final dynamic scheduleDate;
  @override
  @JsonKey(name: "customerName")
  final String? customerName;
  @override
  @JsonKey(name: "customerPhone")
  final String? customerPhone;
  @override
  @JsonKey(name: "paymentMethod")
  final String? paymentMethod;
  @override
  @JsonKey(name: "isSpecialOrder")
  final bool? isSpecialOrder;
  @override
  @JsonKey(name: "subTotal")
  final double? subTotal;
  @override
  @JsonKey(name: "couponValue")
  final double? couponValue;
  @override
  @JsonKey(name: "walletDiscount")
  final double? walletDiscount;
  @override
  @JsonKey(name: "moneyAddedToCustomerWallet")
  final dynamic moneyAddedToCustomerWallet;
  @override
  @JsonKey(name: "deliveryValue")
  final double? deliveryValue;
  @override
  @JsonKey(name: "total")
  final double? total;
  @override
  @JsonKey(name: "customerAddress")
  final CustomerAddress? customerAddress;
  final List<OrderStore>? _orderStores;
  @override
  @JsonKey(name: "orderStores")
  List<OrderStore>? get orderStores {
    final value = _orderStores;
    if (value == null) return null;
    if (_orderStores is EqualUnmodifiableListView) return _orderStores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TrakingOrder>? _trakingOrder;
  @override
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder {
    final value = _trakingOrder;
    if (value == null) return null;
    if (_trakingOrder is EqualUnmodifiableListView) return _trakingOrder;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'OrderDetailsModel(id: $id, dateOrdered: $dateOrdered, scheduleDate: $scheduleDate, customerName: $customerName, customerPhone: $customerPhone, paymentMethod: $paymentMethod, isSpecialOrder: $isSpecialOrder, subTotal: $subTotal, couponValue: $couponValue, walletDiscount: $walletDiscount, moneyAddedToCustomerWallet: $moneyAddedToCustomerWallet, deliveryValue: $deliveryValue, total: $total, customerAddress: $customerAddress, orderStores: $orderStores, trakingOrder: $trakingOrder)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderDetailsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.dateOrdered, dateOrdered) ||
                other.dateOrdered == dateOrdered) &&
            const DeepCollectionEquality()
                .equals(other.scheduleDate, scheduleDate) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerPhone, customerPhone) ||
                other.customerPhone == customerPhone) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.isSpecialOrder, isSpecialOrder) ||
                other.isSpecialOrder == isSpecialOrder) &&
            (identical(other.subTotal, subTotal) ||
                other.subTotal == subTotal) &&
            (identical(other.couponValue, couponValue) ||
                other.couponValue == couponValue) &&
            (identical(other.walletDiscount, walletDiscount) ||
                other.walletDiscount == walletDiscount) &&
            const DeepCollectionEquality().equals(
                other.moneyAddedToCustomerWallet, moneyAddedToCustomerWallet) &&
            (identical(other.deliveryValue, deliveryValue) ||
                other.deliveryValue == deliveryValue) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.customerAddress, customerAddress) ||
                other.customerAddress == customerAddress) &&
            const DeepCollectionEquality()
                .equals(other._orderStores, _orderStores) &&
            const DeepCollectionEquality()
                .equals(other._trakingOrder, _trakingOrder));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      dateOrdered,
      const DeepCollectionEquality().hash(scheduleDate),
      customerName,
      customerPhone,
      paymentMethod,
      isSpecialOrder,
      subTotal,
      couponValue,
      walletDiscount,
      const DeepCollectionEquality().hash(moneyAddedToCustomerWallet),
      deliveryValue,
      total,
      customerAddress,
      const DeepCollectionEquality().hash(_orderStores),
      const DeepCollectionEquality().hash(_trakingOrder));

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderDetailsModelImplCopyWith<_$OrderDetailsModelImpl> get copyWith =>
      __$$OrderDetailsModelImplCopyWithImpl<_$OrderDetailsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderDetailsModelImplToJson(
      this,
    );
  }
}

abstract class _OrderDetailsModel implements OrderDetailsModel {
  const factory _OrderDetailsModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "dateOrdered") final DateTime? dateOrdered,
      @JsonKey(name: "scheduleDate") final dynamic scheduleDate,
      @JsonKey(name: "customerName") final String? customerName,
      @JsonKey(name: "customerPhone") final String? customerPhone,
      @JsonKey(name: "paymentMethod") final String? paymentMethod,
      @JsonKey(name: "isSpecialOrder") final bool? isSpecialOrder,
      @JsonKey(name: "subTotal") final double? subTotal,
      @JsonKey(name: "couponValue") final double? couponValue,
      @JsonKey(name: "walletDiscount") final double? walletDiscount,
      @JsonKey(name: "moneyAddedToCustomerWallet")
      final dynamic moneyAddedToCustomerWallet,
      @JsonKey(name: "deliveryValue") final double? deliveryValue,
      @JsonKey(name: "total") final double? total,
      @JsonKey(name: "customerAddress") final CustomerAddress? customerAddress,
      @JsonKey(name: "orderStores") final List<OrderStore>? orderStores,
      @JsonKey(name: "trakingOrder")
      final List<TrakingOrder>? trakingOrder}) = _$OrderDetailsModelImpl;

  factory _OrderDetailsModel.fromJson(Map<String, dynamic> json) =
      _$OrderDetailsModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "dateOrdered")
  DateTime? get dateOrdered;
  @override
  @JsonKey(name: "scheduleDate")
  dynamic get scheduleDate;
  @override
  @JsonKey(name: "customerName")
  String? get customerName;
  @override
  @JsonKey(name: "customerPhone")
  String? get customerPhone;
  @override
  @JsonKey(name: "paymentMethod")
  String? get paymentMethod;
  @override
  @JsonKey(name: "isSpecialOrder")
  bool? get isSpecialOrder;
  @override
  @JsonKey(name: "subTotal")
  double? get subTotal;
  @override
  @JsonKey(name: "couponValue")
  double? get couponValue;
  @override
  @JsonKey(name: "walletDiscount")
  double? get walletDiscount;
  @override
  @JsonKey(name: "moneyAddedToCustomerWallet")
  dynamic get moneyAddedToCustomerWallet;
  @override
  @JsonKey(name: "deliveryValue")
  double? get deliveryValue;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "customerAddress")
  CustomerAddress? get customerAddress;
  @override
  @JsonKey(name: "orderStores")
  List<OrderStore>? get orderStores;
  @override
  @JsonKey(name: "trakingOrder")
  List<TrakingOrder>? get trakingOrder;

  /// Create a copy of OrderDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderDetailsModelImplCopyWith<_$OrderDetailsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerAddress _$CustomerAddressFromJson(Map<String, dynamic> json) {
  return _CustomerAddress.fromJson(json);
}

/// @nodoc
mixin _$CustomerAddress {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "cityName")
  String? get cityName => throw _privateConstructorUsedError;
  @JsonKey(name: "districtName")
  String? get districtName => throw _privateConstructorUsedError;
  @JsonKey(name: "flat")
  String? get flat => throw _privateConstructorUsedError;
  @JsonKey(name: "floor")
  String? get floor => throw _privateConstructorUsedError;
  @JsonKey(name: "building")
  String? get building => throw _privateConstructorUsedError;
  @JsonKey(name: "mark")
  String? get mark => throw _privateConstructorUsedError;
  @JsonKey(name: "street")
  String? get street => throw _privateConstructorUsedError;
  @JsonKey(name: "lat")
  String? get lat => throw _privateConstructorUsedError;
  @JsonKey(name: "lng")
  String? get lng => throw _privateConstructorUsedError;

  /// Serializes this CustomerAddress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerAddressCopyWith<CustomerAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerAddressCopyWith<$Res> {
  factory $CustomerAddressCopyWith(
          CustomerAddress value, $Res Function(CustomerAddress) then) =
      _$CustomerAddressCopyWithImpl<$Res, CustomerAddress>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "cityName") String? cityName,
      @JsonKey(name: "districtName") String? districtName,
      @JsonKey(name: "flat") String? flat,
      @JsonKey(name: "floor") String? floor,
      @JsonKey(name: "building") String? building,
      @JsonKey(name: "mark") String? mark,
      @JsonKey(name: "street") String? street,
      @JsonKey(name: "lat") String? lat,
      @JsonKey(name: "lng") String? lng});
}

/// @nodoc
class _$CustomerAddressCopyWithImpl<$Res, $Val extends CustomerAddress>
    implements $CustomerAddressCopyWith<$Res> {
  _$CustomerAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityName = freezed,
    Object? districtName = freezed,
    Object? flat = freezed,
    Object? floor = freezed,
    Object? building = freezed,
    Object? mark = freezed,
    Object? street = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cityName: freezed == cityName
          ? _value.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String?,
      districtName: freezed == districtName
          ? _value.districtName
          : districtName // ignore: cast_nullable_to_non_nullable
              as String?,
      flat: freezed == flat
          ? _value.flat
          : flat // ignore: cast_nullable_to_non_nullable
              as String?,
      floor: freezed == floor
          ? _value.floor
          : floor // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      mark: freezed == mark
          ? _value.mark
          : mark // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerAddressImplCopyWith<$Res>
    implements $CustomerAddressCopyWith<$Res> {
  factory _$$CustomerAddressImplCopyWith(_$CustomerAddressImpl value,
          $Res Function(_$CustomerAddressImpl) then) =
      __$$CustomerAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "cityName") String? cityName,
      @JsonKey(name: "districtName") String? districtName,
      @JsonKey(name: "flat") String? flat,
      @JsonKey(name: "floor") String? floor,
      @JsonKey(name: "building") String? building,
      @JsonKey(name: "mark") String? mark,
      @JsonKey(name: "street") String? street,
      @JsonKey(name: "lat") String? lat,
      @JsonKey(name: "lng") String? lng});
}

/// @nodoc
class __$$CustomerAddressImplCopyWithImpl<$Res>
    extends _$CustomerAddressCopyWithImpl<$Res, _$CustomerAddressImpl>
    implements _$$CustomerAddressImplCopyWith<$Res> {
  __$$CustomerAddressImplCopyWithImpl(
      _$CustomerAddressImpl _value, $Res Function(_$CustomerAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerAddress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityName = freezed,
    Object? districtName = freezed,
    Object? flat = freezed,
    Object? floor = freezed,
    Object? building = freezed,
    Object? mark = freezed,
    Object? street = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
  }) {
    return _then(_$CustomerAddressImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cityName: freezed == cityName
          ? _value.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String?,
      districtName: freezed == districtName
          ? _value.districtName
          : districtName // ignore: cast_nullable_to_non_nullable
              as String?,
      flat: freezed == flat
          ? _value.flat
          : flat // ignore: cast_nullable_to_non_nullable
              as String?,
      floor: freezed == floor
          ? _value.floor
          : floor // ignore: cast_nullable_to_non_nullable
              as String?,
      building: freezed == building
          ? _value.building
          : building // ignore: cast_nullable_to_non_nullable
              as String?,
      mark: freezed == mark
          ? _value.mark
          : mark // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerAddressImpl implements _CustomerAddress {
  const _$CustomerAddressImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "cityName") this.cityName,
      @JsonKey(name: "districtName") this.districtName,
      @JsonKey(name: "flat") this.flat,
      @JsonKey(name: "floor") this.floor,
      @JsonKey(name: "building") this.building,
      @JsonKey(name: "mark") this.mark,
      @JsonKey(name: "street") this.street,
      @JsonKey(name: "lat") this.lat,
      @JsonKey(name: "lng") this.lng});

  factory _$CustomerAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerAddressImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "cityName")
  final String? cityName;
  @override
  @JsonKey(name: "districtName")
  final String? districtName;
  @override
  @JsonKey(name: "flat")
  final String? flat;
  @override
  @JsonKey(name: "floor")
  final String? floor;
  @override
  @JsonKey(name: "building")
  final String? building;
  @override
  @JsonKey(name: "mark")
  final String? mark;
  @override
  @JsonKey(name: "street")
  final String? street;
  @override
  @JsonKey(name: "lat")
  final String? lat;
  @override
  @JsonKey(name: "lng")
  final String? lng;

  @override
  String toString() {
    return 'CustomerAddress(id: $id, cityName: $cityName, districtName: $districtName, flat: $flat, floor: $floor, building: $building, mark: $mark, street: $street, lat: $lat, lng: $lng)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerAddressImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityName, cityName) ||
                other.cityName == cityName) &&
            (identical(other.districtName, districtName) ||
                other.districtName == districtName) &&
            (identical(other.flat, flat) || other.flat == flat) &&
            (identical(other.floor, floor) || other.floor == floor) &&
            (identical(other.building, building) ||
                other.building == building) &&
            (identical(other.mark, mark) || other.mark == mark) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, cityName, districtName, flat,
      floor, building, mark, street, lat, lng);

  /// Create a copy of CustomerAddress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerAddressImplCopyWith<_$CustomerAddressImpl> get copyWith =>
      __$$CustomerAddressImplCopyWithImpl<_$CustomerAddressImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerAddressImplToJson(
      this,
    );
  }
}

abstract class _CustomerAddress implements CustomerAddress {
  const factory _CustomerAddress(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "cityName") final String? cityName,
      @JsonKey(name: "districtName") final String? districtName,
      @JsonKey(name: "flat") final String? flat,
      @JsonKey(name: "floor") final String? floor,
      @JsonKey(name: "building") final String? building,
      @JsonKey(name: "mark") final String? mark,
      @JsonKey(name: "street") final String? street,
      @JsonKey(name: "lat") final String? lat,
      @JsonKey(name: "lng") final String? lng}) = _$CustomerAddressImpl;

  factory _CustomerAddress.fromJson(Map<String, dynamic> json) =
      _$CustomerAddressImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "cityName")
  String? get cityName;
  @override
  @JsonKey(name: "districtName")
  String? get districtName;
  @override
  @JsonKey(name: "flat")
  String? get flat;
  @override
  @JsonKey(name: "floor")
  String? get floor;
  @override
  @JsonKey(name: "building")
  String? get building;
  @override
  @JsonKey(name: "mark")
  String? get mark;
  @override
  @JsonKey(name: "street")
  String? get street;
  @override
  @JsonKey(name: "lat")
  String? get lat;
  @override
  @JsonKey(name: "lng")
  String? get lng;

  /// Create a copy of CustomerAddress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerAddressImplCopyWith<_$CustomerAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderStore _$OrderStoreFromJson(Map<String, dynamic> json) {
  return _OrderStore.fromJson(json);
}

/// @nodoc
mixin _$OrderStore {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "storeName")
  String? get storeName => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "subTotal")
  double? get subTotal => throw _privateConstructorUsedError;
  @JsonKey(name: "discountTotal")
  double? get discountTotal => throw _privateConstructorUsedError;
  @JsonKey(name: "orderStoreDetails")
  List<OrderStoreDetail>? get orderStoreDetails =>
      throw _privateConstructorUsedError;

  /// Serializes this OrderStore to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderStore
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderStoreCopyWith<OrderStore> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderStoreCopyWith<$Res> {
  factory $OrderStoreCopyWith(
          OrderStore value, $Res Function(OrderStore) then) =
      _$OrderStoreCopyWithImpl<$Res, OrderStore>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "storeName") String? storeName,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "discountTotal") double? discountTotal,
      @JsonKey(name: "orderStoreDetails")
      List<OrderStoreDetail>? orderStoreDetails});
}

/// @nodoc
class _$OrderStoreCopyWithImpl<$Res, $Val extends OrderStore>
    implements $OrderStoreCopyWith<$Res> {
  _$OrderStoreCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderStore
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? storeName = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
    Object? discountTotal = freezed,
    Object? orderStoreDetails = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      storeName: freezed == storeName
          ? _value.storeName
          : storeName // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      discountTotal: freezed == discountTotal
          ? _value.discountTotal
          : discountTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      orderStoreDetails: freezed == orderStoreDetails
          ? _value.orderStoreDetails
          : orderStoreDetails // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetail>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderStoreImplCopyWith<$Res>
    implements $OrderStoreCopyWith<$Res> {
  factory _$$OrderStoreImplCopyWith(
          _$OrderStoreImpl value, $Res Function(_$OrderStoreImpl) then) =
      __$$OrderStoreImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "storeName") String? storeName,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "subTotal") double? subTotal,
      @JsonKey(name: "discountTotal") double? discountTotal,
      @JsonKey(name: "orderStoreDetails")
      List<OrderStoreDetail>? orderStoreDetails});
}

/// @nodoc
class __$$OrderStoreImplCopyWithImpl<$Res>
    extends _$OrderStoreCopyWithImpl<$Res, _$OrderStoreImpl>
    implements _$$OrderStoreImplCopyWith<$Res> {
  __$$OrderStoreImplCopyWithImpl(
      _$OrderStoreImpl _value, $Res Function(_$OrderStoreImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderStore
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? storeName = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
    Object? discountTotal = freezed,
    Object? orderStoreDetails = freezed,
  }) {
    return _then(_$OrderStoreImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      storeName: freezed == storeName
          ? _value.storeName
          : storeName // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      discountTotal: freezed == discountTotal
          ? _value.discountTotal
          : discountTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      orderStoreDetails: freezed == orderStoreDetails
          ? _value._orderStoreDetails
          : orderStoreDetails // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetail>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderStoreImpl implements _OrderStore {
  const _$OrderStoreImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "storeName") this.storeName,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "subTotal") this.subTotal,
      @JsonKey(name: "discountTotal") this.discountTotal,
      @JsonKey(name: "orderStoreDetails")
      final List<OrderStoreDetail>? orderStoreDetails})
      : _orderStoreDetails = orderStoreDetails;

  factory _$OrderStoreImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderStoreImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "storeName")
  final String? storeName;
  @override
  @JsonKey(name: "total")
  final double? total;
  @override
  @JsonKey(name: "subTotal")
  final double? subTotal;
  @override
  @JsonKey(name: "discountTotal")
  final double? discountTotal;
  final List<OrderStoreDetail>? _orderStoreDetails;
  @override
  @JsonKey(name: "orderStoreDetails")
  List<OrderStoreDetail>? get orderStoreDetails {
    final value = _orderStoreDetails;
    if (value == null) return null;
    if (_orderStoreDetails is EqualUnmodifiableListView)
      return _orderStoreDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'OrderStore(id: $id, storeName: $storeName, total: $total, subTotal: $subTotal, discountTotal: $discountTotal, orderStoreDetails: $orderStoreDetails)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderStoreImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.storeName, storeName) ||
                other.storeName == storeName) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.subTotal, subTotal) ||
                other.subTotal == subTotal) &&
            (identical(other.discountTotal, discountTotal) ||
                other.discountTotal == discountTotal) &&
            const DeepCollectionEquality()
                .equals(other._orderStoreDetails, _orderStoreDetails));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, storeName, total, subTotal,
      discountTotal, const DeepCollectionEquality().hash(_orderStoreDetails));

  /// Create a copy of OrderStore
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderStoreImplCopyWith<_$OrderStoreImpl> get copyWith =>
      __$$OrderStoreImplCopyWithImpl<_$OrderStoreImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderStoreImplToJson(
      this,
    );
  }
}

abstract class _OrderStore implements OrderStore {
  const factory _OrderStore(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "storeName") final String? storeName,
      @JsonKey(name: "total") final double? total,
      @JsonKey(name: "subTotal") final double? subTotal,
      @JsonKey(name: "discountTotal") final double? discountTotal,
      @JsonKey(name: "orderStoreDetails")
      final List<OrderStoreDetail>? orderStoreDetails}) = _$OrderStoreImpl;

  factory _OrderStore.fromJson(Map<String, dynamic> json) =
      _$OrderStoreImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "storeName")
  String? get storeName;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "subTotal")
  double? get subTotal;
  @override
  @JsonKey(name: "discountTotal")
  double? get discountTotal;
  @override
  @JsonKey(name: "orderStoreDetails")
  List<OrderStoreDetail>? get orderStoreDetails;

  /// Create a copy of OrderStore
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderStoreImplCopyWith<_$OrderStoreImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderStoreDetail _$OrderStoreDetailFromJson(Map<String, dynamic> json) {
  return _OrderStoreDetail.fromJson(json);
}

/// @nodoc
mixin _$OrderStoreDetail {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "productName")
  String? get productName => throw _privateConstructorUsedError;
  @JsonKey(name: "productImage")
  String? get productImage => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "priceAfterDiscount")
  double? get priceAfterDiscount => throw _privateConstructorUsedError;
  @JsonKey(name: "quantity")
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "orderDetailOptionValues")
  List<OrderDetailOptionValue>? get orderDetailOptionValues =>
      throw _privateConstructorUsedError;
  @JsonKey(name: "orderStoreDetailAdditions")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailAdditions =>
      throw _privateConstructorUsedError;
  @JsonKey(name: "orderStoreDetailModifyProduct")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailModifyProduct =>
      throw _privateConstructorUsedError;

  /// Serializes this OrderStoreDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderStoreDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderStoreDetailCopyWith<OrderStoreDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderStoreDetailCopyWith<$Res> {
  factory $OrderStoreDetailCopyWith(
          OrderStoreDetail value, $Res Function(OrderStoreDetail) then) =
      _$OrderStoreDetailCopyWithImpl<$Res, OrderStoreDetail>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "productName") String? productName,
      @JsonKey(name: "productImage") String? productImage,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "priceAfterDiscount") double? priceAfterDiscount,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "orderDetailOptionValues")
      List<OrderDetailOptionValue>? orderDetailOptionValues,
      @JsonKey(name: "orderStoreDetailAdditions")
      List<OrderStoreDetailAdditionElement>? orderStoreDetailAdditions,
      @JsonKey(name: "orderStoreDetailModifyProduct")
      List<OrderStoreDetailAdditionElement>? orderStoreDetailModifyProduct});
}

/// @nodoc
class _$OrderStoreDetailCopyWithImpl<$Res, $Val extends OrderStoreDetail>
    implements $OrderStoreDetailCopyWith<$Res> {
  _$OrderStoreDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderStoreDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? productName = freezed,
    Object? productImage = freezed,
    Object? price = freezed,
    Object? priceAfterDiscount = freezed,
    Object? quantity = freezed,
    Object? total = freezed,
    Object? orderDetailOptionValues = freezed,
    Object? orderStoreDetailAdditions = freezed,
    Object? orderStoreDetailModifyProduct = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      productImage: freezed == productImage
          ? _value.productImage
          : productImage // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      orderDetailOptionValues: freezed == orderDetailOptionValues
          ? _value.orderDetailOptionValues
          : orderDetailOptionValues // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailOptionValue>?,
      orderStoreDetailAdditions: freezed == orderStoreDetailAdditions
          ? _value.orderStoreDetailAdditions
          : orderStoreDetailAdditions // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetailAdditionElement>?,
      orderStoreDetailModifyProduct: freezed == orderStoreDetailModifyProduct
          ? _value.orderStoreDetailModifyProduct
          : orderStoreDetailModifyProduct // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetailAdditionElement>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderStoreDetailImplCopyWith<$Res>
    implements $OrderStoreDetailCopyWith<$Res> {
  factory _$$OrderStoreDetailImplCopyWith(_$OrderStoreDetailImpl value,
          $Res Function(_$OrderStoreDetailImpl) then) =
      __$$OrderStoreDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "productName") String? productName,
      @JsonKey(name: "productImage") String? productImage,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "priceAfterDiscount") double? priceAfterDiscount,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "orderDetailOptionValues")
      List<OrderDetailOptionValue>? orderDetailOptionValues,
      @JsonKey(name: "orderStoreDetailAdditions")
      List<OrderStoreDetailAdditionElement>? orderStoreDetailAdditions,
      @JsonKey(name: "orderStoreDetailModifyProduct")
      List<OrderStoreDetailAdditionElement>? orderStoreDetailModifyProduct});
}

/// @nodoc
class __$$OrderStoreDetailImplCopyWithImpl<$Res>
    extends _$OrderStoreDetailCopyWithImpl<$Res, _$OrderStoreDetailImpl>
    implements _$$OrderStoreDetailImplCopyWith<$Res> {
  __$$OrderStoreDetailImplCopyWithImpl(_$OrderStoreDetailImpl _value,
      $Res Function(_$OrderStoreDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderStoreDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? productName = freezed,
    Object? productImage = freezed,
    Object? price = freezed,
    Object? priceAfterDiscount = freezed,
    Object? quantity = freezed,
    Object? total = freezed,
    Object? orderDetailOptionValues = freezed,
    Object? orderStoreDetailAdditions = freezed,
    Object? orderStoreDetailModifyProduct = freezed,
  }) {
    return _then(_$OrderStoreDetailImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      productImage: freezed == productImage
          ? _value.productImage
          : productImage // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      orderDetailOptionValues: freezed == orderDetailOptionValues
          ? _value._orderDetailOptionValues
          : orderDetailOptionValues // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailOptionValue>?,
      orderStoreDetailAdditions: freezed == orderStoreDetailAdditions
          ? _value._orderStoreDetailAdditions
          : orderStoreDetailAdditions // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetailAdditionElement>?,
      orderStoreDetailModifyProduct: freezed == orderStoreDetailModifyProduct
          ? _value._orderStoreDetailModifyProduct
          : orderStoreDetailModifyProduct // ignore: cast_nullable_to_non_nullable
              as List<OrderStoreDetailAdditionElement>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderStoreDetailImpl implements _OrderStoreDetail {
  const _$OrderStoreDetailImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "productName") this.productName,
      @JsonKey(name: "productImage") this.productImage,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "priceAfterDiscount") this.priceAfterDiscount,
      @JsonKey(name: "quantity") this.quantity,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "orderDetailOptionValues")
      final List<OrderDetailOptionValue>? orderDetailOptionValues,
      @JsonKey(name: "orderStoreDetailAdditions")
      final List<OrderStoreDetailAdditionElement>? orderStoreDetailAdditions,
      @JsonKey(name: "orderStoreDetailModifyProduct")
      final List<OrderStoreDetailAdditionElement>?
          orderStoreDetailModifyProduct})
      : _orderDetailOptionValues = orderDetailOptionValues,
        _orderStoreDetailAdditions = orderStoreDetailAdditions,
        _orderStoreDetailModifyProduct = orderStoreDetailModifyProduct;

  factory _$OrderStoreDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderStoreDetailImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "productName")
  final String? productName;
  @override
  @JsonKey(name: "productImage")
  final String? productImage;
  @override
  @JsonKey(name: "price")
  final double? price;
  @override
  @JsonKey(name: "priceAfterDiscount")
  final double? priceAfterDiscount;
  @override
  @JsonKey(name: "quantity")
  final int? quantity;
  @override
  @JsonKey(name: "total")
  final double? total;
  final List<OrderDetailOptionValue>? _orderDetailOptionValues;
  @override
  @JsonKey(name: "orderDetailOptionValues")
  List<OrderDetailOptionValue>? get orderDetailOptionValues {
    final value = _orderDetailOptionValues;
    if (value == null) return null;
    if (_orderDetailOptionValues is EqualUnmodifiableListView)
      return _orderDetailOptionValues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<OrderStoreDetailAdditionElement>? _orderStoreDetailAdditions;
  @override
  @JsonKey(name: "orderStoreDetailAdditions")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailAdditions {
    final value = _orderStoreDetailAdditions;
    if (value == null) return null;
    if (_orderStoreDetailAdditions is EqualUnmodifiableListView)
      return _orderStoreDetailAdditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<OrderStoreDetailAdditionElement>? _orderStoreDetailModifyProduct;
  @override
  @JsonKey(name: "orderStoreDetailModifyProduct")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailModifyProduct {
    final value = _orderStoreDetailModifyProduct;
    if (value == null) return null;
    if (_orderStoreDetailModifyProduct is EqualUnmodifiableListView)
      return _orderStoreDetailModifyProduct;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'OrderStoreDetail(id: $id, productName: $productName, productImage: $productImage, price: $price, priceAfterDiscount: $priceAfterDiscount, quantity: $quantity, total: $total, orderDetailOptionValues: $orderDetailOptionValues, orderStoreDetailAdditions: $orderStoreDetailAdditions, orderStoreDetailModifyProduct: $orderStoreDetailModifyProduct)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderStoreDetailImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productImage, productImage) ||
                other.productImage == productImage) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceAfterDiscount, priceAfterDiscount) ||
                other.priceAfterDiscount == priceAfterDiscount) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(
                other._orderDetailOptionValues, _orderDetailOptionValues) &&
            const DeepCollectionEquality().equals(
                other._orderStoreDetailAdditions, _orderStoreDetailAdditions) &&
            const DeepCollectionEquality().equals(
                other._orderStoreDetailModifyProduct,
                _orderStoreDetailModifyProduct));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      productName,
      productImage,
      price,
      priceAfterDiscount,
      quantity,
      total,
      const DeepCollectionEquality().hash(_orderDetailOptionValues),
      const DeepCollectionEquality().hash(_orderStoreDetailAdditions),
      const DeepCollectionEquality().hash(_orderStoreDetailModifyProduct));

  /// Create a copy of OrderStoreDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderStoreDetailImplCopyWith<_$OrderStoreDetailImpl> get copyWith =>
      __$$OrderStoreDetailImplCopyWithImpl<_$OrderStoreDetailImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderStoreDetailImplToJson(
      this,
    );
  }
}

abstract class _OrderStoreDetail implements OrderStoreDetail {
  const factory _OrderStoreDetail(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "productName") final String? productName,
      @JsonKey(name: "productImage") final String? productImage,
      @JsonKey(name: "price") final double? price,
      @JsonKey(name: "priceAfterDiscount") final double? priceAfterDiscount,
      @JsonKey(name: "quantity") final int? quantity,
      @JsonKey(name: "total") final double? total,
      @JsonKey(name: "orderDetailOptionValues")
      final List<OrderDetailOptionValue>? orderDetailOptionValues,
      @JsonKey(name: "orderStoreDetailAdditions")
      final List<OrderStoreDetailAdditionElement>? orderStoreDetailAdditions,
      @JsonKey(name: "orderStoreDetailModifyProduct")
      final List<OrderStoreDetailAdditionElement>?
          orderStoreDetailModifyProduct}) = _$OrderStoreDetailImpl;

  factory _OrderStoreDetail.fromJson(Map<String, dynamic> json) =
      _$OrderStoreDetailImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "productName")
  String? get productName;
  @override
  @JsonKey(name: "productImage")
  String? get productImage;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "priceAfterDiscount")
  double? get priceAfterDiscount;
  @override
  @JsonKey(name: "quantity")
  int? get quantity;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "orderDetailOptionValues")
  List<OrderDetailOptionValue>? get orderDetailOptionValues;
  @override
  @JsonKey(name: "orderStoreDetailAdditions")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailAdditions;
  @override
  @JsonKey(name: "orderStoreDetailModifyProduct")
  List<OrderStoreDetailAdditionElement>? get orderStoreDetailModifyProduct;

  /// Create a copy of OrderStoreDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderStoreDetailImplCopyWith<_$OrderStoreDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderDetailOptionValue _$OrderDetailOptionValueFromJson(
    Map<String, dynamic> json) {
  return _OrderDetailOptionValue.fromJson(json);
}

/// @nodoc
mixin _$OrderDetailOptionValue {
  @JsonKey(name: "optionName")
  String? get optionName => throw _privateConstructorUsedError;
  @JsonKey(name: "group")
  String? get group => throw _privateConstructorUsedError;

  /// Serializes this OrderDetailOptionValue to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderDetailOptionValue
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderDetailOptionValueCopyWith<OrderDetailOptionValue> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderDetailOptionValueCopyWith<$Res> {
  factory $OrderDetailOptionValueCopyWith(OrderDetailOptionValue value,
          $Res Function(OrderDetailOptionValue) then) =
      _$OrderDetailOptionValueCopyWithImpl<$Res, OrderDetailOptionValue>;
  @useResult
  $Res call(
      {@JsonKey(name: "optionName") String? optionName,
      @JsonKey(name: "group") String? group});
}

/// @nodoc
class _$OrderDetailOptionValueCopyWithImpl<$Res,
        $Val extends OrderDetailOptionValue>
    implements $OrderDetailOptionValueCopyWith<$Res> {
  _$OrderDetailOptionValueCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderDetailOptionValue
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? optionName = freezed,
    Object? group = freezed,
  }) {
    return _then(_value.copyWith(
      optionName: freezed == optionName
          ? _value.optionName
          : optionName // ignore: cast_nullable_to_non_nullable
              as String?,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderDetailOptionValueImplCopyWith<$Res>
    implements $OrderDetailOptionValueCopyWith<$Res> {
  factory _$$OrderDetailOptionValueImplCopyWith(
          _$OrderDetailOptionValueImpl value,
          $Res Function(_$OrderDetailOptionValueImpl) then) =
      __$$OrderDetailOptionValueImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "optionName") String? optionName,
      @JsonKey(name: "group") String? group});
}

/// @nodoc
class __$$OrderDetailOptionValueImplCopyWithImpl<$Res>
    extends _$OrderDetailOptionValueCopyWithImpl<$Res,
        _$OrderDetailOptionValueImpl>
    implements _$$OrderDetailOptionValueImplCopyWith<$Res> {
  __$$OrderDetailOptionValueImplCopyWithImpl(
      _$OrderDetailOptionValueImpl _value,
      $Res Function(_$OrderDetailOptionValueImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderDetailOptionValue
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? optionName = freezed,
    Object? group = freezed,
  }) {
    return _then(_$OrderDetailOptionValueImpl(
      optionName: freezed == optionName
          ? _value.optionName
          : optionName // ignore: cast_nullable_to_non_nullable
              as String?,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderDetailOptionValueImpl implements _OrderDetailOptionValue {
  const _$OrderDetailOptionValueImpl(
      {@JsonKey(name: "optionName") this.optionName,
      @JsonKey(name: "group") this.group});

  factory _$OrderDetailOptionValueImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderDetailOptionValueImplFromJson(json);

  @override
  @JsonKey(name: "optionName")
  final String? optionName;
  @override
  @JsonKey(name: "group")
  final String? group;

  @override
  String toString() {
    return 'OrderDetailOptionValue(optionName: $optionName, group: $group)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderDetailOptionValueImpl &&
            (identical(other.optionName, optionName) ||
                other.optionName == optionName) &&
            (identical(other.group, group) || other.group == group));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, optionName, group);

  /// Create a copy of OrderDetailOptionValue
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderDetailOptionValueImplCopyWith<_$OrderDetailOptionValueImpl>
      get copyWith => __$$OrderDetailOptionValueImplCopyWithImpl<
          _$OrderDetailOptionValueImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderDetailOptionValueImplToJson(
      this,
    );
  }
}

abstract class _OrderDetailOptionValue implements OrderDetailOptionValue {
  const factory _OrderDetailOptionValue(
          {@JsonKey(name: "optionName") final String? optionName,
          @JsonKey(name: "group") final String? group}) =
      _$OrderDetailOptionValueImpl;

  factory _OrderDetailOptionValue.fromJson(Map<String, dynamic> json) =
      _$OrderDetailOptionValueImpl.fromJson;

  @override
  @JsonKey(name: "optionName")
  String? get optionName;
  @override
  @JsonKey(name: "group")
  String? get group;

  /// Create a copy of OrderDetailOptionValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderDetailOptionValueImplCopyWith<_$OrderDetailOptionValueImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OrderStoreDetailAdditionElement _$OrderStoreDetailAdditionElementFromJson(
    Map<String, dynamic> json) {
  return _OrderStoreDetailAdditionElement.fromJson(json);
}

/// @nodoc
mixin _$OrderStoreDetailAdditionElement {
  @JsonKey(name: "productAdditionName")
  String? get productAdditionName => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "discount")
  double? get discount => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;

  /// Serializes this OrderStoreDetailAdditionElement to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderStoreDetailAdditionElement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderStoreDetailAdditionElementCopyWith<OrderStoreDetailAdditionElement>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderStoreDetailAdditionElementCopyWith<$Res> {
  factory $OrderStoreDetailAdditionElementCopyWith(
          OrderStoreDetailAdditionElement value,
          $Res Function(OrderStoreDetailAdditionElement) then) =
      _$OrderStoreDetailAdditionElementCopyWithImpl<$Res,
          OrderStoreDetailAdditionElement>;
  @useResult
  $Res call(
      {@JsonKey(name: "productAdditionName") String? productAdditionName,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "discount") double? discount,
      @JsonKey(name: "total") double? total});
}

/// @nodoc
class _$OrderStoreDetailAdditionElementCopyWithImpl<$Res,
        $Val extends OrderStoreDetailAdditionElement>
    implements $OrderStoreDetailAdditionElementCopyWith<$Res> {
  _$OrderStoreDetailAdditionElementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderStoreDetailAdditionElement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productAdditionName = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      productAdditionName: freezed == productAdditionName
          ? _value.productAdditionName
          : productAdditionName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderStoreDetailAdditionElementImplCopyWith<$Res>
    implements $OrderStoreDetailAdditionElementCopyWith<$Res> {
  factory _$$OrderStoreDetailAdditionElementImplCopyWith(
          _$OrderStoreDetailAdditionElementImpl value,
          $Res Function(_$OrderStoreDetailAdditionElementImpl) then) =
      __$$OrderStoreDetailAdditionElementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "productAdditionName") String? productAdditionName,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "discount") double? discount,
      @JsonKey(name: "total") double? total});
}

/// @nodoc
class __$$OrderStoreDetailAdditionElementImplCopyWithImpl<$Res>
    extends _$OrderStoreDetailAdditionElementCopyWithImpl<$Res,
        _$OrderStoreDetailAdditionElementImpl>
    implements _$$OrderStoreDetailAdditionElementImplCopyWith<$Res> {
  __$$OrderStoreDetailAdditionElementImplCopyWithImpl(
      _$OrderStoreDetailAdditionElementImpl _value,
      $Res Function(_$OrderStoreDetailAdditionElementImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderStoreDetailAdditionElement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productAdditionName = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? total = freezed,
  }) {
    return _then(_$OrderStoreDetailAdditionElementImpl(
      productAdditionName: freezed == productAdditionName
          ? _value.productAdditionName
          : productAdditionName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderStoreDetailAdditionElementImpl
    implements _OrderStoreDetailAdditionElement {
  const _$OrderStoreDetailAdditionElementImpl(
      {@JsonKey(name: "productAdditionName") this.productAdditionName,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "discount") this.discount,
      @JsonKey(name: "total") this.total});

  factory _$OrderStoreDetailAdditionElementImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$OrderStoreDetailAdditionElementImplFromJson(json);

  @override
  @JsonKey(name: "productAdditionName")
  final String? productAdditionName;
  @override
  @JsonKey(name: "price")
  final double? price;
  @override
  @JsonKey(name: "discount")
  final double? discount;
  @override
  @JsonKey(name: "total")
  final double? total;

  @override
  String toString() {
    return 'OrderStoreDetailAdditionElement(productAdditionName: $productAdditionName, price: $price, discount: $discount, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderStoreDetailAdditionElementImpl &&
            (identical(other.productAdditionName, productAdditionName) ||
                other.productAdditionName == productAdditionName) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, productAdditionName, price, discount, total);

  /// Create a copy of OrderStoreDetailAdditionElement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderStoreDetailAdditionElementImplCopyWith<
          _$OrderStoreDetailAdditionElementImpl>
      get copyWith => __$$OrderStoreDetailAdditionElementImplCopyWithImpl<
          _$OrderStoreDetailAdditionElementImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderStoreDetailAdditionElementImplToJson(
      this,
    );
  }
}

abstract class _OrderStoreDetailAdditionElement
    implements OrderStoreDetailAdditionElement {
  const factory _OrderStoreDetailAdditionElement(
      {@JsonKey(name: "productAdditionName") final String? productAdditionName,
      @JsonKey(name: "price") final double? price,
      @JsonKey(name: "discount") final double? discount,
      @JsonKey(name: "total")
      final double? total}) = _$OrderStoreDetailAdditionElementImpl;

  factory _OrderStoreDetailAdditionElement.fromJson(Map<String, dynamic> json) =
      _$OrderStoreDetailAdditionElementImpl.fromJson;

  @override
  @JsonKey(name: "productAdditionName")
  String? get productAdditionName;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "discount")
  double? get discount;
  @override
  @JsonKey(name: "total")
  double? get total;

  /// Create a copy of OrderStoreDetailAdditionElement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderStoreDetailAdditionElementImplCopyWith<
          _$OrderStoreDetailAdditionElementImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TrakingOrder _$TrakingOrderFromJson(Map<String, dynamic> json) {
  return _TrakingOrder.fromJson(json);
}

/// @nodoc
mixin _$TrakingOrder {
  @JsonKey(name: "statusId")
  int? get statusId => throw _privateConstructorUsedError;
  @JsonKey(name: "orderStatus")
  String? get orderStatus => throw _privateConstructorUsedError;

  /// Serializes this TrakingOrder to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TrakingOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TrakingOrderCopyWith<TrakingOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrakingOrderCopyWith<$Res> {
  factory $TrakingOrderCopyWith(
          TrakingOrder value, $Res Function(TrakingOrder) then) =
      _$TrakingOrderCopyWithImpl<$Res, TrakingOrder>;
  @useResult
  $Res call(
      {@JsonKey(name: "statusId") int? statusId,
      @JsonKey(name: "orderStatus") String? orderStatus});
}

/// @nodoc
class _$TrakingOrderCopyWithImpl<$Res, $Val extends TrakingOrder>
    implements $TrakingOrderCopyWith<$Res> {
  _$TrakingOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TrakingOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusId = freezed,
    Object? orderStatus = freezed,
  }) {
    return _then(_value.copyWith(
      statusId: freezed == statusId
          ? _value.statusId
          : statusId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TrakingOrderImplCopyWith<$Res>
    implements $TrakingOrderCopyWith<$Res> {
  factory _$$TrakingOrderImplCopyWith(
          _$TrakingOrderImpl value, $Res Function(_$TrakingOrderImpl) then) =
      __$$TrakingOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "statusId") int? statusId,
      @JsonKey(name: "orderStatus") String? orderStatus});
}

/// @nodoc
class __$$TrakingOrderImplCopyWithImpl<$Res>
    extends _$TrakingOrderCopyWithImpl<$Res, _$TrakingOrderImpl>
    implements _$$TrakingOrderImplCopyWith<$Res> {
  __$$TrakingOrderImplCopyWithImpl(
      _$TrakingOrderImpl _value, $Res Function(_$TrakingOrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of TrakingOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusId = freezed,
    Object? orderStatus = freezed,
  }) {
    return _then(_$TrakingOrderImpl(
      statusId: freezed == statusId
          ? _value.statusId
          : statusId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TrakingOrderImpl implements _TrakingOrder {
  const _$TrakingOrderImpl(
      {@JsonKey(name: "statusId") this.statusId,
      @JsonKey(name: "orderStatus") this.orderStatus});

  factory _$TrakingOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$TrakingOrderImplFromJson(json);

  @override
  @JsonKey(name: "statusId")
  final int? statusId;
  @override
  @JsonKey(name: "orderStatus")
  final String? orderStatus;

  @override
  String toString() {
    return 'TrakingOrder(statusId: $statusId, orderStatus: $orderStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TrakingOrderImpl &&
            (identical(other.statusId, statusId) ||
                other.statusId == statusId) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, statusId, orderStatus);

  /// Create a copy of TrakingOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TrakingOrderImplCopyWith<_$TrakingOrderImpl> get copyWith =>
      __$$TrakingOrderImplCopyWithImpl<_$TrakingOrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TrakingOrderImplToJson(
      this,
    );
  }
}

abstract class _TrakingOrder implements TrakingOrder {
  const factory _TrakingOrder(
          {@JsonKey(name: "statusId") final int? statusId,
          @JsonKey(name: "orderStatus") final String? orderStatus}) =
      _$TrakingOrderImpl;

  factory _TrakingOrder.fromJson(Map<String, dynamic> json) =
      _$TrakingOrderImpl.fromJson;

  @override
  @JsonKey(name: "statusId")
  int? get statusId;
  @override
  @JsonKey(name: "orderStatus")
  String? get orderStatus;

  /// Create a copy of TrakingOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TrakingOrderImplCopyWith<_$TrakingOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
