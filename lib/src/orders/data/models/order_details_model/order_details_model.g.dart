// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderDetailsModelImpl _$$OrderDetailsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderDetailsModelImpl(
      id: (json['id'] as num?)?.toInt(),
      dateOrdered: json['dateOrdered'] == null
          ? null
          : DateTime.parse(json['dateOrdered'] as String),
      scheduleDate: json['scheduleDate'],
      customerName: json['customerName'] as String?,
      customerPhone: json['customerPhone'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      isSpecialOrder: json['isSpecialOrder'] as bool?,
      subTotal: (json['subTotal'] as num?)?.toDouble(),
      couponValue: (json['couponValue'] as num?)?.toDouble(),
      walletDiscount: (json['walletDiscount'] as num?)?.toDouble(),
      moneyAddedToCustomerWallet: json['moneyAddedToCustomerWallet'],
      deliveryValue: (json['deliveryValue'] as num?)?.toDouble(),
      total: (json['total'] as num?)?.toDouble(),
      customerAddress: json['customerAddress'] == null
          ? null
          : CustomerAddress.fromJson(
              json['customerAddress'] as Map<String, dynamic>),
      orderStores: (json['orderStores'] as List<dynamic>?)
          ?.map((e) => OrderStore.fromJson(e as Map<String, dynamic>))
          .toList(),
      trakingOrder: (json['trakingOrder'] as List<dynamic>?)
          ?.map((e) => TrakingOrder.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OrderDetailsModelImplToJson(
        _$OrderDetailsModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.dateOrdered?.toIso8601String() case final value?)
        'dateOrdered': value,
      if (instance.scheduleDate case final value?) 'scheduleDate': value,
      if (instance.customerName case final value?) 'customerName': value,
      if (instance.customerPhone case final value?) 'customerPhone': value,
      if (instance.paymentMethod case final value?) 'paymentMethod': value,
      if (instance.isSpecialOrder case final value?) 'isSpecialOrder': value,
      if (instance.subTotal case final value?) 'subTotal': value,
      if (instance.couponValue case final value?) 'couponValue': value,
      if (instance.walletDiscount case final value?) 'walletDiscount': value,
      if (instance.moneyAddedToCustomerWallet case final value?)
        'moneyAddedToCustomerWallet': value,
      if (instance.deliveryValue case final value?) 'deliveryValue': value,
      if (instance.total case final value?) 'total': value,
      if (instance.customerAddress case final value?) 'customerAddress': value,
      if (instance.orderStores case final value?) 'orderStores': value,
      if (instance.trakingOrder case final value?) 'trakingOrder': value,
    };

_$CustomerAddressImpl _$$CustomerAddressImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerAddressImpl(
      id: (json['id'] as num?)?.toInt(),
      cityName: json['cityName'] as String?,
      districtName: json['districtName'] as String?,
      flat: json['flat'] as String?,
      floor: json['floor'] as String?,
      building: json['building'] as String?,
      mark: json['mark'] as String?,
      street: json['street'] as String?,
      lat: json['lat'] as String?,
      lng: json['lng'] as String?,
    );

Map<String, dynamic> _$$CustomerAddressImplToJson(
        _$CustomerAddressImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.cityName case final value?) 'cityName': value,
      if (instance.districtName case final value?) 'districtName': value,
      if (instance.flat case final value?) 'flat': value,
      if (instance.floor case final value?) 'floor': value,
      if (instance.building case final value?) 'building': value,
      if (instance.mark case final value?) 'mark': value,
      if (instance.street case final value?) 'street': value,
      if (instance.lat case final value?) 'lat': value,
      if (instance.lng case final value?) 'lng': value,
    };

_$OrderStoreImpl _$$OrderStoreImplFromJson(Map<String, dynamic> json) =>
    _$OrderStoreImpl(
      id: (json['id'] as num?)?.toInt(),
      storeName: json['storeName'] as String?,
      total: (json['total'] as num?)?.toDouble(),
      subTotal: (json['subTotal'] as num?)?.toDouble(),
      discountTotal: (json['discountTotal'] as num?)?.toDouble(),
      orderStoreDetails: (json['orderStoreDetails'] as List<dynamic>?)
          ?.map((e) => OrderStoreDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OrderStoreImplToJson(_$OrderStoreImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.storeName case final value?) 'storeName': value,
      if (instance.total case final value?) 'total': value,
      if (instance.subTotal case final value?) 'subTotal': value,
      if (instance.discountTotal case final value?) 'discountTotal': value,
      if (instance.orderStoreDetails case final value?)
        'orderStoreDetails': value,
    };

_$OrderStoreDetailImpl _$$OrderStoreDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderStoreDetailImpl(
      id: (json['id'] as num?)?.toInt(),
      productName: json['productName'] as String?,
      productImage: json['productImage'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toDouble(),
      orderDetailOptionValues: (json['orderDetailOptionValues']
              as List<dynamic>?)
          ?.map(
              (e) => OrderDetailOptionValue.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderStoreDetailAdditions:
          (json['orderStoreDetailAdditions'] as List<dynamic>?)
              ?.map((e) => OrderStoreDetailAdditionElement.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
      orderStoreDetailModifyProduct:
          (json['orderStoreDetailModifyProduct'] as List<dynamic>?)
              ?.map((e) => OrderStoreDetailAdditionElement.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$OrderStoreDetailImplToJson(
        _$OrderStoreDetailImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.productName case final value?) 'productName': value,
      if (instance.productImage case final value?) 'productImage': value,
      if (instance.price case final value?) 'price': value,
      if (instance.priceAfterDiscount case final value?)
        'priceAfterDiscount': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.total case final value?) 'total': value,
      if (instance.orderDetailOptionValues case final value?)
        'orderDetailOptionValues': value,
      if (instance.orderStoreDetailAdditions case final value?)
        'orderStoreDetailAdditions': value,
      if (instance.orderStoreDetailModifyProduct case final value?)
        'orderStoreDetailModifyProduct': value,
    };

_$OrderDetailOptionValueImpl _$$OrderDetailOptionValueImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderDetailOptionValueImpl(
      optionName: json['optionName'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$$OrderDetailOptionValueImplToJson(
        _$OrderDetailOptionValueImpl instance) =>
    <String, dynamic>{
      if (instance.optionName case final value?) 'optionName': value,
      if (instance.group case final value?) 'group': value,
    };

_$OrderStoreDetailAdditionElementImpl
    _$$OrderStoreDetailAdditionElementImplFromJson(Map<String, dynamic> json) =>
        _$OrderStoreDetailAdditionElementImpl(
          productAdditionName: json['productAdditionName'] as String?,
          price: (json['price'] as num?)?.toDouble(),
          discount: (json['discount'] as num?)?.toDouble(),
          total: (json['total'] as num?)?.toDouble(),
        );

Map<String, dynamic> _$$OrderStoreDetailAdditionElementImplToJson(
        _$OrderStoreDetailAdditionElementImpl instance) =>
    <String, dynamic>{
      if (instance.productAdditionName case final value?)
        'productAdditionName': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.total case final value?) 'total': value,
    };

_$TrakingOrderImpl _$$TrakingOrderImplFromJson(Map<String, dynamic> json) =>
    _$TrakingOrderImpl(
      statusId: (json['statusId'] as num?)?.toInt(),
      orderStatus: json['orderStatus'] as String?,
    );

Map<String, dynamic> _$$TrakingOrderImplToJson(_$TrakingOrderImpl instance) =>
    <String, dynamic>{
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.orderStatus case final value?) 'orderStatus': value,
    };
