import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_details_response.g.dart';

@JsonSerializable()
class OrderDetailsResponse extends BaseResponse {
  final OrderDetailsModel? data;

  OrderDetailsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory OrderDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$OrderDetailsResponseToJson(this);
}
