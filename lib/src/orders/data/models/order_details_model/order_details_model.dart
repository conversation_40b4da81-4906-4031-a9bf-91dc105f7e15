// To parse this JSON data, do
//
//     final orderDetailsModel = orderDetailsModelFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_details_model.freezed.dart';
part 'order_details_model.g.dart';

OrderDetailsModel orderDetailsModelFromJson(String str) =>
    OrderDetailsModel.fromJson(json.decode(str));

String orderDetailsModelToJson(OrderDetailsModel data) =>
    json.encode(data.toJson());

@freezed
class OrderDetailsModel with _$OrderDetailsModel {
  const factory OrderDetailsModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON><PERSON>(name: "dateOrdered") DateTime? dateOrdered,
    @J<PERSON><PERSON><PERSON>(name: "scheduleDate") dynamic scheduleDate,
    @Json<PERSON>ey(name: "customerName") String? customerName,
    @<PERSON>son<PERSON><PERSON>(name: "customerPhone") String? customerPhone,
    @Json<PERSON>ey(name: "paymentMethod") String? paymentMethod,
    @JsonKey(name: "isSpecialOrder") bool? isSpecialOrder,
    @<PERSON>son<PERSON><PERSON>(name: "subTotal") double? subTotal,
    @JsonKey(name: "couponValue") double? couponValue,
    @JsonKey(name: "walletDiscount") double? walletDiscount,
    @JsonKey(name: "moneyAddedToCustomerWallet")
    dynamic moneyAddedToCustomerWallet,
    @JsonKey(name: "deliveryValue") double? deliveryValue,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "customerAddress") CustomerAddress? customerAddress,
    @JsonKey(name: "orderStores") List<OrderStore>? orderStores,
    @JsonKey(name: "trakingOrder") List<TrakingOrder>? trakingOrder,
  }) = _OrderDetailsModel;

  factory OrderDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailsModelFromJson(json);
}

@freezed
class CustomerAddress with _$CustomerAddress {
  const factory CustomerAddress({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "cityName") String? cityName,
    @JsonKey(name: "districtName") String? districtName,
    @JsonKey(name: "flat") String? flat,
    @JsonKey(name: "floor") String? floor,
    @JsonKey(name: "building") String? building,
    @JsonKey(name: "mark") String? mark,
    @JsonKey(name: "street") String? street,
    @JsonKey(name: "lat") String? lat,
    @JsonKey(name: "lng") String? lng,
  }) = _CustomerAddress;

  factory CustomerAddress.fromJson(Map<String, dynamic> json) =>
      _$CustomerAddressFromJson(json);
}

@freezed
class OrderStore with _$OrderStore {
  const factory OrderStore({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "storeName") String? storeName,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "subTotal") double? subTotal,
    @JsonKey(name: "discountTotal") double? discountTotal,
    @JsonKey(name: "orderStoreDetails")
    List<OrderStoreDetail>? orderStoreDetails,
  }) = _OrderStore;

  factory OrderStore.fromJson(Map<String, dynamic> json) =>
      _$OrderStoreFromJson(json);
}

@freezed
class OrderStoreDetail with _$OrderStoreDetail {
  const factory OrderStoreDetail({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "productName") String? productName,
    @JsonKey(name: "productImage") String? productImage,
    @JsonKey(name: "price") double? price,
    @JsonKey(name: "priceAfterDiscount") double? priceAfterDiscount,
    @JsonKey(name: "quantity") int? quantity,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "orderDetailOptionValues")
    List<OrderDetailOptionValue>? orderDetailOptionValues,
    @JsonKey(name: "orderStoreDetailAdditions")
    List<OrderStoreDetailAdditionElement>? orderStoreDetailAdditions,
    @JsonKey(name: "orderStoreDetailModifyProduct")
    List<OrderStoreDetailAdditionElement>? orderStoreDetailModifyProduct,
  }) = _OrderStoreDetail;

  factory OrderStoreDetail.fromJson(Map<String, dynamic> json) =>
      _$OrderStoreDetailFromJson(json);
}

@freezed
class OrderDetailOptionValue with _$OrderDetailOptionValue {
  const factory OrderDetailOptionValue({
    @JsonKey(name: "optionName") String? optionName,
    @JsonKey(name: "group") String? group,
  }) = _OrderDetailOptionValue;

  factory OrderDetailOptionValue.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailOptionValueFromJson(json);
}

@freezed
class OrderStoreDetailAdditionElement with _$OrderStoreDetailAdditionElement {
  const factory OrderStoreDetailAdditionElement({
    @JsonKey(name: "productAdditionName") String? productAdditionName,
    @JsonKey(name: "price") double? price,
    @JsonKey(name: "discount") double? discount,
    @JsonKey(name: "total") double? total,
  }) = _OrderStoreDetailAdditionElement;

  factory OrderStoreDetailAdditionElement.fromJson(Map<String, dynamic> json) =>
      _$OrderStoreDetailAdditionElementFromJson(json);
}

@freezed
class TrakingOrder with _$TrakingOrder {
  const factory TrakingOrder({
    @JsonKey(name: "statusId") int? statusId,
    @JsonKey(name: "orderStatus") String? orderStatus,
  }) = _TrakingOrder;

  factory TrakingOrder.fromJson(Map<String, dynamic> json) =>
      _$TrakingOrderFromJson(json);
}
