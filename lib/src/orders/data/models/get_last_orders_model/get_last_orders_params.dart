// To parse this JSON data, do
//
//     final getLastOrdersParams = getLastOrdersParamsFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'get_last_orders_params.g.dart';

GetLastOrdersParams getLastOrdersParamsFromJson(String str) =>
    GetLastOrdersParams.fromJson(json.decode(str));

String getLastOrdersParamsToJson(GetLastOrdersParams data) =>
    json.encode(data.toJson());

@JsonSerializable()
class GetLastOrdersParams {
  @Json<PERSON>ey(name: "skip")
  int? skip;
  @Json<PERSON>ey(name: "take")
  int? take;

  GetLastOrdersParams({
    this.skip,
    this.take,
  });

  factory GetLastOrdersParams.fromJson(Map<String, dynamic> json) =>
      _$GetLastOrdersParamsFromJson(json);

  Map<String, dynamic> toJson() => _$GetLastOrdersParamsToJson(this);
}
