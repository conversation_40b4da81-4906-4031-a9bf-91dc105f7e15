import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'orders_data_wrapper.g.dart';

OrdersDataWrapper ordersDataWrapperFromJson(String str) =>
    OrdersDataWrapper.fromJson(json.decode(str));

String ordersDataWrapperToJson(OrdersDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable()
class OrdersDataWrapper {
  @JsonKey(name: "orderStatusId")
  int? orderStatusId;
  @JsonKey(name: "skip")
  int? skip;
  @Json<PERSON>ey(name: "take")
  int? take;

  OrdersDataWrapper({
    this.orderStatusId,
    this.skip,
    this.take = 10,
  });

  factory OrdersDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$OrdersDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$OrdersDataWrapperToJson(this);
}
