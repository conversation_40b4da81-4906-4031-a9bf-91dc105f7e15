// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'orders_data_wrapper.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrdersDataWrapper _$OrdersDataWrapperFromJson(Map<String, dynamic> json) =>
    OrdersDataWrapper(
      orderStatusId: (json['orderStatusId'] as num?)?.toInt(),
      skip: (json['skip'] as num?)?.toInt(),
      take: (json['take'] as num?)?.toInt() ?? 10,
    );

Map<String, dynamic> _$OrdersDataWrapperToJson(OrdersDataWrapper instance) =>
    <String, dynamic>{
      if (instance.orderStatusId case final value?) 'orderStatusId': value,
      if (instance.skip case final value?) 'skip': value,
      if (instance.take case final value?) 'take': value,
    };
