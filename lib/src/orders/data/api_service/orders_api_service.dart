import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/orders/data/models/get_last_orders_model/get_last_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_response.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_response.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'orders_api_service.g.dart';

@RestApi()
abstract class OrdersApiService {
  factory OrdersApiService(
    Dio dio, {
    String baseUrl,
  }) = _OrdersApiService;

  @GET(EndPoints.getCurrentOrders)
  Future<GetOrdersResponse> getOrders(@Queries() GetOrdersParams params);

  @GET(EndPoints.getLastOrders)
  Future<GetOrdersResponse> getLastOrders(
      @Queries() GetLastOrdersParams params);

  @GET("${EndPoints.getOrderDetails}/{orderId}")
  Future<OrderDetailsResponse> getOrderDetails(@Path('orderId') int orderId);

  @GET("${EndPoints.getSpecialOrderDetails}/{orderId}")
  Future<SpecialOrderDetailsResponse> getSpecialOrderDetails(
      @Path('orderId') int orderId);

  @POST("${EndPoints.convertStatusOrders}/{orderId}/{statusId}")
  Future<BaseResponse> convertStatusOrder(
    @Path('orderId') int orderId,
    @Path('statusId') int statusId,
    @Query('cancelReason') String? reason,
  );
}
