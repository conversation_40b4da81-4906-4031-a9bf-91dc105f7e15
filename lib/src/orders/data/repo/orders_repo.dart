import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/orders/data/api_service/orders_api_service.dart';
import 'package:alsarea_store/src/orders/data/models/get_last_orders_model/get_last_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_params.dart';
import 'package:alsarea_store/src/orders/data/models/get_orders_model/get_orders_response.dart';
import 'package:alsarea_store/src/orders/data/models/order_details_model/order_details_response.dart';
import 'package:alsarea_store/src/orders/data/models/special_order_details_model/special_order_details_response.dart';

class OrdersRepo {
  final OrdersApiService _ordersApiService;
  OrdersRepo(this._ordersApiService);

  Future<Result<GetOrdersResponse>> getOrders(GetOrdersParams params) =>
      errorHandlerAsync(() => _ordersApiService.getOrders(params));

  Future<Result<GetOrdersResponse>> getLastOrders(GetLastOrdersParams params) =>
      errorHandlerAsync(() => _ordersApiService.getLastOrders(params));

  Future<Result<OrderDetailsResponse>> getOrderDetails(int orderId) =>
      errorHandlerAsync(() => _ordersApiService.getOrderDetails(orderId));

  Future<Result<SpecialOrderDetailsResponse>> getSpecialOrderDetails(
          int orderId) =>
      errorHandlerAsync(
          () => _ordersApiService.getSpecialOrderDetails(orderId));

  Future<Result<BaseResponse>> convertStatusOrder(
      int orderId, int statusId, String? reason) async {
    return errorHandlerAsync(
      () => _ordersApiService.convertStatusOrder(orderId, statusId, reason),
    );
  }
}
