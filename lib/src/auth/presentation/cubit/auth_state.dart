part of 'auth_cubit.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  // Login
  const factory AuthState.loginLoading() = _LoginLoading;
  const factory AuthState.loginSuccess() = _LoginSuccess;
  const factory AuthState.loginFailure(String message) = _LoginFailure;

  //resendVerify
  const factory AuthState.resendCodeLoading() = _ResendCodeLoading;
  const factory AuthState.resendCodeSuccess() = _ResendCodeSuccess;
  const factory AuthState.resendCodeFailure(String message) =
      _ResendCodeFailure;

  //verify
  const factory AuthState.verifyLoading() = _VerifyLoading;
  const factory AuthState.verifySuccess() = _VerifySuccess;
  const factory AuthState.verifyFailure(String message) = _VerifyFailure;
}
