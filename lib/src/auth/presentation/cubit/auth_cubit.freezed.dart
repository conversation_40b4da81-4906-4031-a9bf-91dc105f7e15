// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'AuthState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AuthState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoginLoadingImplCopyWith<$Res> {
  factory _$$LoginLoadingImplCopyWith(
          _$LoginLoadingImpl value, $Res Function(_$LoginLoadingImpl) then) =
      __$$LoginLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoginLoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoginLoadingImpl>
    implements _$$LoginLoadingImplCopyWith<$Res> {
  __$$LoginLoadingImplCopyWithImpl(
      _$LoginLoadingImpl _value, $Res Function(_$LoginLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoginLoadingImpl implements _LoginLoading {
  const _$LoginLoadingImpl();

  @override
  String toString() {
    return 'AuthState.loginLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoginLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return loginLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return loginLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginLoading != null) {
      return loginLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return loginLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return loginLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginLoading != null) {
      return loginLoading(this);
    }
    return orElse();
  }
}

abstract class _LoginLoading implements AuthState {
  const factory _LoginLoading() = _$LoginLoadingImpl;
}

/// @nodoc
abstract class _$$LoginSuccessImplCopyWith<$Res> {
  factory _$$LoginSuccessImplCopyWith(
          _$LoginSuccessImpl value, $Res Function(_$LoginSuccessImpl) then) =
      __$$LoginSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoginSuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoginSuccessImpl>
    implements _$$LoginSuccessImplCopyWith<$Res> {
  __$$LoginSuccessImplCopyWithImpl(
      _$LoginSuccessImpl _value, $Res Function(_$LoginSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoginSuccessImpl implements _LoginSuccess {
  const _$LoginSuccessImpl();

  @override
  String toString() {
    return 'AuthState.loginSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoginSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return loginSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return loginSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginSuccess != null) {
      return loginSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return loginSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return loginSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginSuccess != null) {
      return loginSuccess(this);
    }
    return orElse();
  }
}

abstract class _LoginSuccess implements AuthState {
  const factory _LoginSuccess() = _$LoginSuccessImpl;
}

/// @nodoc
abstract class _$$LoginFailureImplCopyWith<$Res> {
  factory _$$LoginFailureImplCopyWith(
          _$LoginFailureImpl value, $Res Function(_$LoginFailureImpl) then) =
      __$$LoginFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$LoginFailureImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoginFailureImpl>
    implements _$$LoginFailureImplCopyWith<$Res> {
  __$$LoginFailureImplCopyWithImpl(
      _$LoginFailureImpl _value, $Res Function(_$LoginFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$LoginFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoginFailureImpl implements _LoginFailure {
  const _$LoginFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AuthState.loginFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginFailureImplCopyWith<_$LoginFailureImpl> get copyWith =>
      __$$LoginFailureImplCopyWithImpl<_$LoginFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return loginFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return loginFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginFailure != null) {
      return loginFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return loginFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return loginFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (loginFailure != null) {
      return loginFailure(this);
    }
    return orElse();
  }
}

abstract class _LoginFailure implements AuthState {
  const factory _LoginFailure(final String message) = _$LoginFailureImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginFailureImplCopyWith<_$LoginFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResendCodeLoadingImplCopyWith<$Res> {
  factory _$$ResendCodeLoadingImplCopyWith(_$ResendCodeLoadingImpl value,
          $Res Function(_$ResendCodeLoadingImpl) then) =
      __$$ResendCodeLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResendCodeLoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$ResendCodeLoadingImpl>
    implements _$$ResendCodeLoadingImplCopyWith<$Res> {
  __$$ResendCodeLoadingImplCopyWithImpl(_$ResendCodeLoadingImpl _value,
      $Res Function(_$ResendCodeLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResendCodeLoadingImpl implements _ResendCodeLoading {
  const _$ResendCodeLoadingImpl();

  @override
  String toString() {
    return 'AuthState.resendCodeLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResendCodeLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return resendCodeLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return resendCodeLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeLoading != null) {
      return resendCodeLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return resendCodeLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return resendCodeLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeLoading != null) {
      return resendCodeLoading(this);
    }
    return orElse();
  }
}

abstract class _ResendCodeLoading implements AuthState {
  const factory _ResendCodeLoading() = _$ResendCodeLoadingImpl;
}

/// @nodoc
abstract class _$$ResendCodeSuccessImplCopyWith<$Res> {
  factory _$$ResendCodeSuccessImplCopyWith(_$ResendCodeSuccessImpl value,
          $Res Function(_$ResendCodeSuccessImpl) then) =
      __$$ResendCodeSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResendCodeSuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$ResendCodeSuccessImpl>
    implements _$$ResendCodeSuccessImplCopyWith<$Res> {
  __$$ResendCodeSuccessImplCopyWithImpl(_$ResendCodeSuccessImpl _value,
      $Res Function(_$ResendCodeSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResendCodeSuccessImpl implements _ResendCodeSuccess {
  const _$ResendCodeSuccessImpl();

  @override
  String toString() {
    return 'AuthState.resendCodeSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResendCodeSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return resendCodeSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return resendCodeSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeSuccess != null) {
      return resendCodeSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return resendCodeSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return resendCodeSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeSuccess != null) {
      return resendCodeSuccess(this);
    }
    return orElse();
  }
}

abstract class _ResendCodeSuccess implements AuthState {
  const factory _ResendCodeSuccess() = _$ResendCodeSuccessImpl;
}

/// @nodoc
abstract class _$$ResendCodeFailureImplCopyWith<$Res> {
  factory _$$ResendCodeFailureImplCopyWith(_$ResendCodeFailureImpl value,
          $Res Function(_$ResendCodeFailureImpl) then) =
      __$$ResendCodeFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ResendCodeFailureImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$ResendCodeFailureImpl>
    implements _$$ResendCodeFailureImplCopyWith<$Res> {
  __$$ResendCodeFailureImplCopyWithImpl(_$ResendCodeFailureImpl _value,
      $Res Function(_$ResendCodeFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ResendCodeFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ResendCodeFailureImpl implements _ResendCodeFailure {
  const _$ResendCodeFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AuthState.resendCodeFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResendCodeFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResendCodeFailureImplCopyWith<_$ResendCodeFailureImpl> get copyWith =>
      __$$ResendCodeFailureImplCopyWithImpl<_$ResendCodeFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return resendCodeFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return resendCodeFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeFailure != null) {
      return resendCodeFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return resendCodeFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return resendCodeFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (resendCodeFailure != null) {
      return resendCodeFailure(this);
    }
    return orElse();
  }
}

abstract class _ResendCodeFailure implements AuthState {
  const factory _ResendCodeFailure(final String message) =
      _$ResendCodeFailureImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResendCodeFailureImplCopyWith<_$ResendCodeFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerifyLoadingImplCopyWith<$Res> {
  factory _$$VerifyLoadingImplCopyWith(
          _$VerifyLoadingImpl value, $Res Function(_$VerifyLoadingImpl) then) =
      __$$VerifyLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerifyLoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$VerifyLoadingImpl>
    implements _$$VerifyLoadingImplCopyWith<$Res> {
  __$$VerifyLoadingImplCopyWithImpl(
      _$VerifyLoadingImpl _value, $Res Function(_$VerifyLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerifyLoadingImpl implements _VerifyLoading {
  const _$VerifyLoadingImpl();

  @override
  String toString() {
    return 'AuthState.verifyLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$VerifyLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return verifyLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return verifyLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifyLoading != null) {
      return verifyLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return verifyLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return verifyLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifyLoading != null) {
      return verifyLoading(this);
    }
    return orElse();
  }
}

abstract class _VerifyLoading implements AuthState {
  const factory _VerifyLoading() = _$VerifyLoadingImpl;
}

/// @nodoc
abstract class _$$VerifySuccessImplCopyWith<$Res> {
  factory _$$VerifySuccessImplCopyWith(
          _$VerifySuccessImpl value, $Res Function(_$VerifySuccessImpl) then) =
      __$$VerifySuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$VerifySuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$VerifySuccessImpl>
    implements _$$VerifySuccessImplCopyWith<$Res> {
  __$$VerifySuccessImplCopyWithImpl(
      _$VerifySuccessImpl _value, $Res Function(_$VerifySuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$VerifySuccessImpl implements _VerifySuccess {
  const _$VerifySuccessImpl();

  @override
  String toString() {
    return 'AuthState.verifySuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$VerifySuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return verifySuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return verifySuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifySuccess != null) {
      return verifySuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return verifySuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return verifySuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifySuccess != null) {
      return verifySuccess(this);
    }
    return orElse();
  }
}

abstract class _VerifySuccess implements AuthState {
  const factory _VerifySuccess() = _$VerifySuccessImpl;
}

/// @nodoc
abstract class _$$VerifyFailureImplCopyWith<$Res> {
  factory _$$VerifyFailureImplCopyWith(
          _$VerifyFailureImpl value, $Res Function(_$VerifyFailureImpl) then) =
      __$$VerifyFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$VerifyFailureImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$VerifyFailureImpl>
    implements _$$VerifyFailureImplCopyWith<$Res> {
  __$$VerifyFailureImplCopyWithImpl(
      _$VerifyFailureImpl _value, $Res Function(_$VerifyFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$VerifyFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerifyFailureImpl implements _VerifyFailure {
  const _$VerifyFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AuthState.verifyFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyFailureImplCopyWith<_$VerifyFailureImpl> get copyWith =>
      __$$VerifyFailureImplCopyWithImpl<_$VerifyFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loginLoading,
    required TResult Function() loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function() resendCodeLoading,
    required TResult Function() resendCodeSuccess,
    required TResult Function(String message) resendCodeFailure,
    required TResult Function() verifyLoading,
    required TResult Function() verifySuccess,
    required TResult Function(String message) verifyFailure,
  }) {
    return verifyFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loginLoading,
    TResult? Function()? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function()? resendCodeLoading,
    TResult? Function()? resendCodeSuccess,
    TResult? Function(String message)? resendCodeFailure,
    TResult? Function()? verifyLoading,
    TResult? Function()? verifySuccess,
    TResult? Function(String message)? verifyFailure,
  }) {
    return verifyFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loginLoading,
    TResult Function()? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function()? resendCodeLoading,
    TResult Function()? resendCodeSuccess,
    TResult Function(String message)? resendCodeFailure,
    TResult Function()? verifyLoading,
    TResult Function()? verifySuccess,
    TResult Function(String message)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifyFailure != null) {
      return verifyFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoginLoading value) loginLoading,
    required TResult Function(_LoginSuccess value) loginSuccess,
    required TResult Function(_LoginFailure value) loginFailure,
    required TResult Function(_ResendCodeLoading value) resendCodeLoading,
    required TResult Function(_ResendCodeSuccess value) resendCodeSuccess,
    required TResult Function(_ResendCodeFailure value) resendCodeFailure,
    required TResult Function(_VerifyLoading value) verifyLoading,
    required TResult Function(_VerifySuccess value) verifySuccess,
    required TResult Function(_VerifyFailure value) verifyFailure,
  }) {
    return verifyFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoginLoading value)? loginLoading,
    TResult? Function(_LoginSuccess value)? loginSuccess,
    TResult? Function(_LoginFailure value)? loginFailure,
    TResult? Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult? Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult? Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult? Function(_VerifyLoading value)? verifyLoading,
    TResult? Function(_VerifySuccess value)? verifySuccess,
    TResult? Function(_VerifyFailure value)? verifyFailure,
  }) {
    return verifyFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoginLoading value)? loginLoading,
    TResult Function(_LoginSuccess value)? loginSuccess,
    TResult Function(_LoginFailure value)? loginFailure,
    TResult Function(_ResendCodeLoading value)? resendCodeLoading,
    TResult Function(_ResendCodeSuccess value)? resendCodeSuccess,
    TResult Function(_ResendCodeFailure value)? resendCodeFailure,
    TResult Function(_VerifyLoading value)? verifyLoading,
    TResult Function(_VerifySuccess value)? verifySuccess,
    TResult Function(_VerifyFailure value)? verifyFailure,
    required TResult orElse(),
  }) {
    if (verifyFailure != null) {
      return verifyFailure(this);
    }
    return orElse();
  }
}

abstract class _VerifyFailure implements AuthState {
  const factory _VerifyFailure(final String message) = _$VerifyFailureImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyFailureImplCopyWith<_$VerifyFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
