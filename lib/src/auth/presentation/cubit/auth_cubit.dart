import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/src/auth/data/models/auth_body.dart';
import 'package:alsarea_store/src/auth/data/models/auth_operations.dart';
import 'package:alsarea_store/src/auth/data/repo/auth_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_cubit.freezed.dart';
part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepo _repo;
  AuthCubit(this._repo) : super(const AuthState.initial());

  final oprations = AuthOperations();

  Future<void> login() async {
    emit(const AuthState.loginLoading());
    final body = LoginBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.login(body);
    result.when(
        success: (_) => emit(const AuthState.loginSuccess()),
        failure: (message) => emit(AuthState.loginFailure(message)));
  }

  Future<void> verify() async {
    String token = await SharedPreferencesHelper.get('fcm_token');
    oprations.dataWrapper.fcmToken = token;
    emit(const AuthState.verifyLoading());
    final body = VerifyBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.verify(body);
    result.when(
        success: (_) => emit(const AuthState.verifySuccess()),
        failure: (message) => emit(AuthState.verifyFailure(message)));
  }

  Future<void> resendCode() async {
    emit(const AuthState.resendCodeLoading());
    final body = ResendCodeBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.resendCode(body);
    result.when(
        success: (_) => emit(const AuthState.resendCodeSuccess()),
        failure: (message) => emit(AuthState.resendCodeFailure(message)));
  }

  Future<void> logout() async {
    AuthOperations.removeToken();
  }
}
