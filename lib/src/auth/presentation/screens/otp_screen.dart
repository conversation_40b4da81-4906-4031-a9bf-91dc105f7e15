import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:alsarea_store/src/auth/presentation/widgets/otp_screen/pin_code_field.dart';
import 'package:alsarea_store/src/auth/presentation/widgets/otp_screen/resend_code.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class OtpScreen extends StatefulWidget implements AutoRouteWrapper {
  const OtpScreen({super.key});

  @override
  State<OtpScreen> createState() => _OtpScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<AuthCubit>(),
        child: this,
      );
}

class _OtpScreenState extends State<OtpScreen> {
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AuthCubit>();
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.activationCode),
      ),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          state.whenOrNull(
            verifyFailure: (message) => UiHelper.onFailure(context, message),
            verifyLoading: () => UiHelper.onLoading(context),
            verifySuccess: () {
              UiHelper.onSuccess(context);
              context.router.replaceAll([const BottomNavBarRoute()]);
            },
          );
        },
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 42),
            child: Column(
              children: [
                Text(
                  context.l10n.pleaseEnterTheActivationCode,
                  style: TextStyles.body16,
                ),
                const SizedBox(height: 12),
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: PinCodeField(
                    onCompleted: (value) => _onVerify(context),
                    onSaved: (value) =>
                        cubit.oprations.dataWrapper.code = value,
                  ),
                ),
                Assets.images.otp.svg(),
                const SizedBox(height: 20),
                Text(
                  context.l10n.ifYouDoNotReceiveTheOTPCodeYouCanResendItLater,
                  style: TextStyles.body16,
                ),
                const ResendCode(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onVerify(BuildContext context) {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    context.read<AuthCubit>().verify();
  }
}
