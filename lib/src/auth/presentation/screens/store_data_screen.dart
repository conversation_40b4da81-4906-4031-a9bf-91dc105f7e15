import 'dart:convert';
import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/core/widgets/shared/upload_image.dart';
import 'package:alsarea_store/src/products/data/models/modal/app_activity_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/city_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

@RoutePage()
class StoreDataScreen extends StatefulWidget implements AutoRouteWrapper {
  const StoreDataScreen({super.key});

  @override
  State<StoreDataScreen> createState() => _StoreDataScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _StoreDataScreenState extends State<StoreDataScreen> {
  final _formKey = GlobalKey<FormState>();
  final cubit = injector<CategoryCubit>();
  final List<CityModel> cities = injector<CategoryCubit>().oprations.cities;
  final List<AppActivityModel> appActivities =
      injector<CategoryCubit>().oprations.appActivities;
  bool isEditCover = false;
  bool isEditlogo = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(context, title: Text(context.l10n.storeData)),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16).copyWith(bottom: 32),
        child: BlocListener<CategoryCubit, CategoryState>(
          listener: (context, state) {
            state.whenOrNull(
              editProfileFailure: (message) =>
                  UiHelper.onFailure(context, message),
              editProfileLoading: () => UiHelper.onLoading(context),
              editProfileSuccess: (category) {
                UiHelper.onSuccess(context);
                setState(() {});
              },
            );
          },
          child: Form(
            key: _formKey,
            child: Column(
              spacing: 24,
              children: [
                SizedBox(
                  width: .9.sw,
                  child: RichText(
                    textAlign: TextAlign.start,
                    text: TextSpan(
                      text: context.l10n.profileCover,
                      style: TextStyles.body16.copyWith(
                        color: Colors.black,
                      ),
                      children: [
                        TextSpan(
                          text: '  * ',
                          style: TextStyles.body16.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                cubit.oprations.profile.cover ==
                            'http://3lsare3.flyfox-eg.com/images/Allimages/' ||
                        cubit.oprations.profile.cover == null ||
                        isEditCover == true
                    ? UploadImageWidget(
                        label: context.l10n.profileCover,
                        onUploadImage: (file) async {
                          if (file == null) {
                            cubit.oprations.profileDataWrapper.cover = null;
                          } else {
                            List<int> imageBytes = await file.readAsBytes();
                            cubit.oprations.profileDataWrapper.cover =
                                base64Encode(imageBytes);
                          }
                        },
                      )
                    : Stack(
                        children: [
                          Center(
                            child: CustomNetworkImage(
                              cubit.oprations.profile.cover ?? '',
                              width: .5.sw,
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                          Positioned(
                            child: IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: AppColors.primary,
                              ),
                              onPressed: () {
                                setState(() {
                                  isEditCover = true;
                                });
                              },
                            ),
                          )
                        ],
                      ),
                SizedBox(
                  width: .9.sw,
                  child: RichText(
                    textAlign: TextAlign.start,
                    text: TextSpan(
                      text: context.l10n.profileImage,
                      style: TextStyles.body16.copyWith(
                        color: Colors.black,
                      ),
                      children: [
                        TextSpan(
                          text: '  * ',
                          style: TextStyles.body16.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                cubit.oprations.profile.image ==
                            'http://3lsare3.flyfox-eg.com/images/Allimages/' ||
                        cubit.oprations.profile.image == null ||
                        isEditlogo == true
                    ? UploadImageWidget(
                        label: context.l10n.profileImage,
                        onUploadImage: (file) async {
                          if (file == null) {
                            cubit.oprations.profileDataWrapper.logo = null;
                          } else {
                            List<int> imageBytes = await file.readAsBytes();
                            cubit.oprations.profileDataWrapper.logo =
                                base64Encode(imageBytes);
                          }
                        },
                      )
                    : Stack(
                        children: [
                          Center(
                            child: CustomNetworkImage(
                              cubit.oprations.profile.image ?? '',
                              width: .5.sw,
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                          Positioned(
                            child: IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: AppColors.primary,
                              ),
                              onPressed: () {
                                setState(() {
                                  isEditlogo = true;
                                });
                              },
                            ),
                          )
                        ],
                      ),

                LabeledField(
                  isRequired: true,
                  label: context.l10n.storeNameAr,
                  field: CustomTextField(
                    hintText: context.l10n.storeNameAr,
                    initialValue: cubit.oprations.profile.nameAr,
                    onChanged: (value) {
                      cubit.oprations.profileDataWrapper.nameAr = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                  ),
                ),
                LabeledField(
                  isRequired: true,
                  label: context.l10n.storeNameEn,
                  field: CustomTextField(
                    hintText: context.l10n.storeNameEn,
                    initialValue: cubit.oprations.profile.nameEn,
                    onChanged: (value) {
                      cubit.oprations.profileDataWrapper.nameEn = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                  ),
                ),
                LabeledField(
                  isRequired: true,
                  label: context.l10n.specialty,
                  field: CustomDropDownButton(
                    hintText: context.l10n.specialty,
                    value: appActivities.firstWhere(
                      (item) =>
                          item.id == cubit.oprations.profile.appActivityId,
                      orElse: () => appActivities.first,
                    ),
                    onChanged: (value) {
                      log(value.toString());
                      cubit.oprations.profileDataWrapper.appActivityId =
                          value?.id;
                    },
                    items: appActivities
                        .map(
                          (e) => DropdownMenuItem(
                            value: e,
                            child: Text(e.name ?? ''),
                          ),
                        )
                        .toList(),
                  ),
                ),
                LabeledField(
                  isRequired: true,
                  label: context.l10n.phoneNumber,
                  field: CustomTextField(
                    hintText: context.l10n.phoneNumber,
                    initialValue: cubit.oprations.profile.phoneNumber,
                    onChanged: (value) {
                      cubit.oprations.profileDataWrapper.phone = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                  ),
                ),
                LabeledField(
                  isRequired: true,
                  label: context.l10n.city,
                  field: CustomDropDownButton(
                    hintText: context.l10n.city,
                    value: cities.firstWhere(
                      (item) => item.id == cubit.oprations.profile.cityId,
                      orElse: () => cities.first,
                    ),
                    onChanged: (value) {
                      cubit.oprations.profileDataWrapper.cityId = value?.id;
                    },
                    items: cities
                        .map(
                          (e) => DropdownMenuItem(
                            value: e,
                            child: Text(e.name ?? ''),
                          ),
                        )
                        .toList(),
                  ),
                ),
                LabeledField(
                  label: context.l10n.address,
                  field: CustomTextField(
                    hintText: context.l10n.address,
                    initialValue: cubit.oprations.profile.address,
                    onChanged: (value) {
                      cubit.oprations.profileDataWrapper.address = value;
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                  ),
                ),
                // CustomElevatedButton(
                //   child: Row(
                //     spacing: 8,
                //     mainAxisSize: MainAxisSize.min,
                //     children: [
                //       Assets.icons.location.svg(),
                //       Text(context.l10n.determineLocation),
                //     ],
                //   ),
                // ),
                const SizedBox.shrink(),
                CustomElevatedButton(
                  onPressed: () => {_onSave()},
                  child: Text(context.l10n.save),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onSave() {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    injector<CategoryCubit>().editProfile();
    setState(() {
      isEditCover = false;
      isEditlogo = false;
    });
  }
}
