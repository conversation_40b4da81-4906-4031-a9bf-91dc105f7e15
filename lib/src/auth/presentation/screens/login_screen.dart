import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

@RoutePage()
class LoginScreen extends StatefulWidget implements AutoRouteWrapper {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<AuthCubit>(),
        child: this,
      );
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AuthCubit>();
    return Scaffold(
      body: BlocListener<AuthCubit, AuthState>(
          listener: (context, state) {
            state.whenOrNull(
              loginFailure: (message) => UiHelper.onFailure(context, message),
              loginLoading: () => UiHelper.onLoading(context),
              loginSuccess: () {
                UiHelper.onSuccess(context);
                context.router.push(const OtpRoute());
              },
            );
          },
          child: Form(
            key: _formKey,
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.splash2.image().image,
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        Assets.icons.logo.svg(),
                        SizedBox(height: .08.sh),
                        CustomTextField(
                          hintText: context.l10n.phoneNumber,
                          prefixIcon: Assets.icons.phone.svg(),
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return context.l10n.phoneNumber;
                            } else if (value.length < 11) {
                              return context.l10n.phoneNumber;
                            }
                            return null;
                          },
                          onSaved: (value) {
                            cubit.oprations.dataWrapper.phone = value;
                            cubit.oprations.dataWrapper.mobile = value;
                          },
                        ),
                        const SizedBox(height: 20),
                        CustomElevatedButton(
                          child: Text(context.l10n.login),
                          onPressed: () => _onLogin(context),
                        ),
                        const SizedBox(height: 32),
                        GestureDetector(
                          onTap: () =>
                              context.router.replace(const BottomNavBarRoute()),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                context.l10n.skip,
                                style: TextStyles.body16,
                              ),
                              const SizedBox(width: 10),
                              Assets.icons.skip.svg(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }

  void _onLogin(BuildContext context) {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    context.read<AuthCubit>().login();
  }
}
