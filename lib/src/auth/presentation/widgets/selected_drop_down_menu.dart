import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:flutter/material.dart';

final selectedSpecialties = [];

class SelectedDropDownMenu extends StatefulWidget {
  const SelectedDropDownMenu({super.key});

  @override
  State<SelectedDropDownMenu> createState() => _SelectedDropDownMenuState();
}

class _SelectedDropDownMenuState extends State<SelectedDropDownMenu> {
  @override
  Widget build(BuildContext context) {
    final specialties = ['تخصص 1', 'تخصص 2', 'تخصص 3'];
    return Column(
      spacing: 8,
      children: [
        CustomDropDownButton(
          selectedItemBuilder: (_) => List.generate(
            specialties.length,
            (index) => Text(context.l10n.specialty),
          ),
          hintText: context.l10n.specialty,
          items: specialties
              .map(
                (e) => DropdownMenuItem(
                  value: e,
                  child: SpecialtySelectItem(
                    specialty: e,
                    onTap: () => setState(() {}),
                  ),
                ),
              )
              .toList(),
        ),
        SizedBox(
          width: double.infinity,
          child: Wrap(
            spacing: 8,
            children: List.generate(
              selectedSpecialties.length,
              (index) => SpecialtyChip(
                label: selectedSpecialties[index],
                onTap: () => setState(() {}),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class SpecialtySelectItem extends StatefulWidget {
  const SpecialtySelectItem({super.key, required this.specialty, this.onTap});

  final String specialty;
  final VoidCallback? onTap;

  @override
  State<SpecialtySelectItem> createState() => _SpecialtySelectItemState();
}

class _SpecialtySelectItemState extends State<SpecialtySelectItem> {
  bool _isSelected = false;

  @override
  void initState() {
    _isSelected = selectedSpecialties.contains(widget.specialty);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      visualDensity: const VisualDensity(horizontal: -4),
      side: const BorderSide(
        color: AppColors.lightGrey,
      ),
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
      activeColor: AppColors.yellow,
      value: _isSelected,
      onChanged: (value) {
        setState(() {
          _isSelected = value!;
          if (value) {
            selectedSpecialties.add(widget.specialty);
          } else {
            selectedSpecialties.remove(widget.specialty);
          }
          widget.onTap?.call();
        });
      },
      title: Text(widget.specialty),
    );
  }
}

class SpecialtyChip extends StatelessWidget {
  const SpecialtyChip({super.key, required this.label, this.onTap});

  final String label;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(
        Icons.close,
        color: AppColors.red,
        size: 16,
      ),
      onDeleted: () {
        selectedSpecialties.remove(label);
        onTap?.call();
      },
    );
  }
}
