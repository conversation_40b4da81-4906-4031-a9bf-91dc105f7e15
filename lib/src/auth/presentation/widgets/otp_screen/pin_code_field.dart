import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class PinCodeField extends StatelessWidget {
  const PinCodeField({
    super.key,
    this.onSaved,
    this.onCompleted,
  });

  final void Function(String? value)? onSaved;
  final void Function(String value)? onCompleted;

  @override
  Widget build(BuildContext context) {
    return PinCodeTextField(
      onSaved: onSaved,
      onCompleted: onCompleted,
      appContext: context,
      autoFocus: true,
      enableActiveFill: true,
      length: 4,
      showCursor: false,
      keyboardType: TextInputType.number,
      animationType: AnimationType.fade,
      textStyle: TextStyles.title20.copyWith(
        fontWeight: FontWeight.bold,
      ),
      hintStyle: TextStyles.title14.copyWith(
        color: AppColors.grey,
      ),
      pinTheme: PinTheme(
        borderWidth: 3,
        activeBorderWidth: 3,
        shape: PinCodeFieldShape.circle,
        fieldHeight: 70,
        fieldWidth: 70,
        activeColor: AppColors.lighterGrey,
        disabledColor: AppColors.lighterGrey,
        inactiveColor: AppColors.lighterGrey,
        selectedColor: AppColors.primary,
        inactiveFillColor: Colors.white,
        selectedFillColor: Colors.white,
        activeFillColor: Colors.white,
      ),
    );
  }
}
