import 'dart:async';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResendCode extends StatefulWidget implements AutoRouteWrapper {
  const ResendCode({super.key});

  @override
  State<ResendCode> createState() => _ResendCodeState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<AuthCubit>(),
        child: this,
      );
}

class _ResendCodeState extends State<ResendCode> {
  final int _countDownTimer = 180;
  int _remainingTime = 0;
  Timer? _timer;
  bool _isCountdownActive = false;

  @override
  void initState() {
    _startTimer();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    setState(() {
      _remainingTime = _countDownTimer;
      _isCountdownActive = true;
    });

    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          timer.cancel();
          _isCountdownActive = false;
        }
      });
    });
  }

  String secondsToMinutes(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            color: _isCountdownActive
                ? const Color(0xffB7B7B7)
                : AppColors.primary,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
            onPressed:
                !_isCountdownActive ? () => _onResendCode(context) : null,
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          secondsToMinutes(_remainingTime),
          style: TextStyles.headline28.copyWith(
            fontWeight: FontWeight.w700,
            color: _isCountdownActive
                ? AppColors.primary
                : const Color(0xffB7B7B7),
          ),
        ),
      ],
    );
  }

  void _onResendCode(BuildContext context) {
    context.read<AuthCubit>().resendCode();
    _startTimer();
  }
}
