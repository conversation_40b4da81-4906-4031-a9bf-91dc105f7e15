import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/auth/data/models/auth_body.dart';
import 'package:alsarea_store/src/auth/data/models/auth_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api_service.g.dart';

@RestApi()
abstract class AuthApiService {
  factory AuthApiService(
    Dio dio, {
    String baseUrl,
  }) = _AuthApiService;

  @POST(EndPoints.login)
  Future<BaseResponse> login(@Queries() LoginBody body);

  @POST(EndPoints.verify)
  Future<AuthResponse> verify(@Body() VerifyBody body);

  @POST(EndPoints.resendCode)
  Future<BaseResponse> resendCode(@Body() ResendCodeBody body);
}
