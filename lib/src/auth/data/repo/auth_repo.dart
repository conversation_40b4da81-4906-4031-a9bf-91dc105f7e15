import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/auth/data/api_service/auth_api_service.dart';
import 'package:alsarea_store/src/auth/data/models/auth_body.dart';
import 'package:alsarea_store/src/auth/data/models/auth_operations.dart';
import 'package:alsarea_store/src/auth/data/models/auth_response.dart';

class AuthRepo {
  final AuthApiService _apiService;
  AuthRepo(this._apiService);

  Future<Result<BaseResponse>> login(LoginBody body) =>
      errorHandlerAsync(() => _apiService.login(body));

  Future<Result<AuthResponse>> verify(VerifyBody body) =>
      errorHandlerAsync(() async {
        final result = await _apiService.verify(body);
        AuthOperations.setToken(result.data?.token);
        return result;
      });

  Future<Result<BaseResponse>> resendCode(ResendCodeBody body) =>
      errorHandlerAsync(() async {
        final result = await _apiService.resendCode(body);
        return result;
      });
}
