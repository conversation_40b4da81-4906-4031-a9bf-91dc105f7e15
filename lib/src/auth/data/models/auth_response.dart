// To parse this JSON data, do
//
//     final authResponse = authResponseFromJson(jsonString);

import 'dart:convert';

import 'package:alsarea_store/core/api/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_response.g.dart';

AuthResponse authResponseFromJson(String str) =>
    AuthResponse.fromJson(json.decode(str));

String authResponseToJson(AuthResponse data) => json.encode(data.toJson());

@JsonSerializable()
class AuthResponse extends BaseResponse {
  @JsonKey(name: "data")
  Data? data;

  AuthResponse({
    this.data,
    // super.status,
    // super.statusName,
    super.message,
    // super.debug,
    // super.pages,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "isDataComplete")
  bool? isDataComplete;
  @JsonKey(name: "token")
  String? token;
  @JsonKey(name: "expiration")
  DateTime? expiration;
  @JsonKey(name: "userType")
  String? userType;

  Data({
    this.isDataComplete,
    this.token,
    this.expiration,
    this.userType,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
