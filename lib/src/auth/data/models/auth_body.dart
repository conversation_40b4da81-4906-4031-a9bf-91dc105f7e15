import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_body.freezed.dart';
part 'auth_body.g.dart';

@freezed
class LoginBody with _$LoginBody {
  const factory LoginBody({
    String? mobile,
  }) = _LoginBody;

  factory LoginBody.fromJson(Map<String, dynamic> json) =>
      _$LoginBodyFromJson(json);
}

@freezed
class VerifyBody with _$VerifyBody {
  const factory VerifyBody({
    String? mobile,
    String? code,
    String? fcmToken,
  }) = _VerifyBody;

  factory VerifyBody.fromJson(Map<String, dynamic> json) =>
      _$VerifyBodyFromJson(json);
}

@freezed
class ResendCodeBody with _$ResendCodeBody {
  const factory ResendCodeBody({
    String? mobile,
  }) = _ResendCodeBody;

  factory ResendCodeBody.fromJson(Map<String, dynamic> json) =>
      _$ResendCodeBodyFromJson(json);
}
