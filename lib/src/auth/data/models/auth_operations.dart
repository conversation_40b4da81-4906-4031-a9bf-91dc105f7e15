import 'package:alsarea_store/core/api/dio_factory.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/src/auth/data/models/auth_data_wrapper.dart';

class AuthOperations {
  final dataWrapper = AuthDataWrapper();

  static void setToken(String? token) {
    if (token == null) return;
    DioFactory.setTokenIntoHeaderAfterLogin(token);
    SharedPreferencesHelper.setSecuredString('token', token);
    SharedPreferencesHelper.set<bool>('isTokenStored', true);
  }

  static void removeToken() {
    SharedPreferencesHelper.removeSecuredData('token');
    SharedPreferencesHelper.set<bool>('isTokenStored', false);
  }

  static bool isUserLoggedIn() {
    return SharedPreferencesHelper.get<bool?>('isTokenStored') ?? false;
  }
}
