// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_data_wrapper.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthDataWrapper _$AuthDataWrapperFromJson(Map<String, dynamic> json) {
  return _AuthDataWrapper.fromJson(json);
}

/// @nodoc
mixin _$AuthDataWrapper {
  String? get phone => throw _privateConstructorUsedError;
  set phone(String? value) => throw _privateConstructorUsedError;
  String? get mobile => throw _privateConstructorUsedError;
  set mobile(String? value) => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  set code(String? value) => throw _privateConstructorUsedError;
  String? get fcmToken => throw _privateConstructorUsedError;
  set fcmToken(String? value) => throw _privateConstructorUsedError;

  /// Serializes this AuthDataWrapper to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthDataWrapper
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthDataWrapperCopyWith<AuthDataWrapper> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthDataWrapperCopyWith<$Res> {
  factory $AuthDataWrapperCopyWith(
          AuthDataWrapper value, $Res Function(AuthDataWrapper) then) =
      _$AuthDataWrapperCopyWithImpl<$Res, AuthDataWrapper>;
  @useResult
  $Res call({String? phone, String? mobile, String? code, String? fcmToken});
}

/// @nodoc
class _$AuthDataWrapperCopyWithImpl<$Res, $Val extends AuthDataWrapper>
    implements $AuthDataWrapperCopyWith<$Res> {
  _$AuthDataWrapperCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthDataWrapper
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = freezed,
    Object? mobile = freezed,
    Object? code = freezed,
    Object? fcmToken = freezed,
  }) {
    return _then(_value.copyWith(
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthDataWrapperImplCopyWith<$Res>
    implements $AuthDataWrapperCopyWith<$Res> {
  factory _$$AuthDataWrapperImplCopyWith(_$AuthDataWrapperImpl value,
          $Res Function(_$AuthDataWrapperImpl) then) =
      __$$AuthDataWrapperImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? phone, String? mobile, String? code, String? fcmToken});
}

/// @nodoc
class __$$AuthDataWrapperImplCopyWithImpl<$Res>
    extends _$AuthDataWrapperCopyWithImpl<$Res, _$AuthDataWrapperImpl>
    implements _$$AuthDataWrapperImplCopyWith<$Res> {
  __$$AuthDataWrapperImplCopyWithImpl(
      _$AuthDataWrapperImpl _value, $Res Function(_$AuthDataWrapperImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthDataWrapper
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = freezed,
    Object? mobile = freezed,
    Object? code = freezed,
    Object? fcmToken = freezed,
  }) {
    return _then(_$AuthDataWrapperImpl(
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthDataWrapperImpl implements _AuthDataWrapper {
  _$AuthDataWrapperImpl({this.phone, this.mobile, this.code, this.fcmToken});

  factory _$AuthDataWrapperImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthDataWrapperImplFromJson(json);

  @override
  String? phone;
  @override
  String? mobile;
  @override
  String? code;
  @override
  String? fcmToken;

  @override
  String toString() {
    return 'AuthDataWrapper(phone: $phone, mobile: $mobile, code: $code, fcmToken: $fcmToken)';
  }

  /// Create a copy of AuthDataWrapper
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthDataWrapperImplCopyWith<_$AuthDataWrapperImpl> get copyWith =>
      __$$AuthDataWrapperImplCopyWithImpl<_$AuthDataWrapperImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthDataWrapperImplToJson(
      this,
    );
  }
}

abstract class _AuthDataWrapper implements AuthDataWrapper {
  factory _AuthDataWrapper(
      {String? phone,
      String? mobile,
      String? code,
      String? fcmToken}) = _$AuthDataWrapperImpl;

  factory _AuthDataWrapper.fromJson(Map<String, dynamic> json) =
      _$AuthDataWrapperImpl.fromJson;

  @override
  String? get phone;
  set phone(String? value);
  @override
  String? get mobile;
  set mobile(String? value);
  @override
  String? get code;
  set code(String? value);
  @override
  String? get fcmToken;
  set fcmToken(String? value);

  /// Create a copy of AuthDataWrapper
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthDataWrapperImplCopyWith<_$AuthDataWrapperImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
