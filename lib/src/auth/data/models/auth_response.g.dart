// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'],
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      if (instance.message case final value?) 'message': value,
      if (instance.data case final value?) 'data': value,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      isDataComplete: json['isDataComplete'] as bool?,
      token: json['token'] as String?,
      expiration: json['expiration'] == null
          ? null
          : DateTime.parse(json['expiration'] as String),
      userType: json['userType'] as String?,
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      if (instance.isDataComplete case final value?) 'isDataComplete': value,
      if (instance.token case final value?) 'token': value,
      if (instance.expiration?.toIso8601String() case final value?)
        'expiration': value,
      if (instance.userType case final value?) 'userType': value,
    };
