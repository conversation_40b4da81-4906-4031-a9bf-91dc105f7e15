// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LoginBodyImpl _$$LoginBodyImplFromJson(Map<String, dynamic> json) =>
    _$LoginBodyImpl(
      mobile: json['mobile'] as String?,
    );

Map<String, dynamic> _$$LoginBodyImplToJson(_$LoginBodyImpl instance) =>
    <String, dynamic>{
      if (instance.mobile case final value?) 'mobile': value,
    };

_$VerifyBodyImpl _$$VerifyBodyImplFromJson(Map<String, dynamic> json) =>
    _$VerifyBodyImpl(
      mobile: json['mobile'] as String?,
      code: json['code'] as String?,
      fcmToken: json['fcmToken'] as String?,
    );

Map<String, dynamic> _$$VerifyBodyImplToJson(_$VerifyBodyImpl instance) =>
    <String, dynamic>{
      if (instance.mobile case final value?) 'mobile': value,
      if (instance.code case final value?) 'code': value,
      if (instance.fcmToken case final value?) 'fcmToken': value,
    };

_$ResendCodeBodyImpl _$$ResendCodeBodyImplFromJson(Map<String, dynamic> json) =>
    _$ResendCodeBodyImpl(
      mobile: json['mobile'] as String?,
    );

Map<String, dynamic> _$$ResendCodeBodyImplToJson(
        _$ResendCodeBodyImpl instance) =>
    <String, dynamic>{
      if (instance.mobile case final value?) 'mobile': value,
    };
