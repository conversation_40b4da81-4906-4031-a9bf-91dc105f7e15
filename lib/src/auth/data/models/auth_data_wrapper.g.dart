// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_data_wrapper.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthDataWrapperImpl _$$AuthDataWrapperImplFromJson(
        Map<String, dynamic> json) =>
    _$AuthDataWrapperImpl(
      phone: json['phone'] as String?,
      mobile: json['mobile'] as String?,
      code: json['code'] as String?,
      fcmToken: json['fcmToken'] as String?,
    );

Map<String, dynamic> _$$AuthDataWrapperImplToJson(
        _$AuthDataWrapperImpl instance) =>
    <String, dynamic>{
      if (instance.phone case final value?) 'phone': value,
      if (instance.mobile case final value?) 'mobile': value,
      if (instance.code case final value?) 'code': value,
      if (instance.fcmToken case final value?) 'fcmToken': value,
    };
