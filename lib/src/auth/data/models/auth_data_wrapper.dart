import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_data_wrapper.freezed.dart';
part 'auth_data_wrapper.g.dart';

@unfreezed
class AuthDataWrapper with _$AuthDataWrapper {
  factory AuthDataWrapper({
    String? phone,
    String? mobile,
    String? code,
    String? fcmToken,
  }) = _AuthDataWrapper;

  factory AuthDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$AuthDataWrapperFromJson(json);
}
