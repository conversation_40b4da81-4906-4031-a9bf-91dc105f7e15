// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoginBody _$LoginBodyFromJson(Map<String, dynamic> json) {
  return _LoginBody.fromJson(json);
}

/// @nodoc
mixin _$LoginBody {
  String? get mobile => throw _privateConstructorUsedError;

  /// Serializes this LoginBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginBodyCopyWith<LoginBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginBodyCopyWith<$Res> {
  factory $LoginBodyCopyWith(LoginBody value, $Res Function(LoginBody) then) =
      _$LoginBodyCopyWithImpl<$Res, LoginBody>;
  @useResult
  $Res call({String? mobile});
}

/// @nodoc
class _$LoginBodyCopyWithImpl<$Res, $Val extends LoginBody>
    implements $LoginBodyCopyWith<$Res> {
  _$LoginBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
  }) {
    return _then(_value.copyWith(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginBodyImplCopyWith<$Res>
    implements $LoginBodyCopyWith<$Res> {
  factory _$$LoginBodyImplCopyWith(
          _$LoginBodyImpl value, $Res Function(_$LoginBodyImpl) then) =
      __$$LoginBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mobile});
}

/// @nodoc
class __$$LoginBodyImplCopyWithImpl<$Res>
    extends _$LoginBodyCopyWithImpl<$Res, _$LoginBodyImpl>
    implements _$$LoginBodyImplCopyWith<$Res> {
  __$$LoginBodyImplCopyWithImpl(
      _$LoginBodyImpl _value, $Res Function(_$LoginBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
  }) {
    return _then(_$LoginBodyImpl(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginBodyImpl implements _LoginBody {
  const _$LoginBodyImpl({this.mobile});

  factory _$LoginBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginBodyImplFromJson(json);

  @override
  final String? mobile;

  @override
  String toString() {
    return 'LoginBody(mobile: $mobile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginBodyImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mobile);

  /// Create a copy of LoginBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginBodyImplCopyWith<_$LoginBodyImpl> get copyWith =>
      __$$LoginBodyImplCopyWithImpl<_$LoginBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginBodyImplToJson(
      this,
    );
  }
}

abstract class _LoginBody implements LoginBody {
  const factory _LoginBody({final String? mobile}) = _$LoginBodyImpl;

  factory _LoginBody.fromJson(Map<String, dynamic> json) =
      _$LoginBodyImpl.fromJson;

  @override
  String? get mobile;

  /// Create a copy of LoginBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginBodyImplCopyWith<_$LoginBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifyBody _$VerifyBodyFromJson(Map<String, dynamic> json) {
  return _VerifyBody.fromJson(json);
}

/// @nodoc
mixin _$VerifyBody {
  String? get mobile => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  String? get fcmToken => throw _privateConstructorUsedError;

  /// Serializes this VerifyBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifyBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifyBodyCopyWith<VerifyBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyBodyCopyWith<$Res> {
  factory $VerifyBodyCopyWith(
          VerifyBody value, $Res Function(VerifyBody) then) =
      _$VerifyBodyCopyWithImpl<$Res, VerifyBody>;
  @useResult
  $Res call({String? mobile, String? code, String? fcmToken});
}

/// @nodoc
class _$VerifyBodyCopyWithImpl<$Res, $Val extends VerifyBody>
    implements $VerifyBodyCopyWith<$Res> {
  _$VerifyBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
    Object? code = freezed,
    Object? fcmToken = freezed,
  }) {
    return _then(_value.copyWith(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyBodyImplCopyWith<$Res>
    implements $VerifyBodyCopyWith<$Res> {
  factory _$$VerifyBodyImplCopyWith(
          _$VerifyBodyImpl value, $Res Function(_$VerifyBodyImpl) then) =
      __$$VerifyBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mobile, String? code, String? fcmToken});
}

/// @nodoc
class __$$VerifyBodyImplCopyWithImpl<$Res>
    extends _$VerifyBodyCopyWithImpl<$Res, _$VerifyBodyImpl>
    implements _$$VerifyBodyImplCopyWith<$Res> {
  __$$VerifyBodyImplCopyWithImpl(
      _$VerifyBodyImpl _value, $Res Function(_$VerifyBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
    Object? code = freezed,
    Object? fcmToken = freezed,
  }) {
    return _then(_$VerifyBodyImpl(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      fcmToken: freezed == fcmToken
          ? _value.fcmToken
          : fcmToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyBodyImpl implements _VerifyBody {
  const _$VerifyBodyImpl({this.mobile, this.code, this.fcmToken});

  factory _$VerifyBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyBodyImplFromJson(json);

  @override
  final String? mobile;
  @override
  final String? code;
  @override
  final String? fcmToken;

  @override
  String toString() {
    return 'VerifyBody(mobile: $mobile, code: $code, fcmToken: $fcmToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyBodyImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.fcmToken, fcmToken) ||
                other.fcmToken == fcmToken));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mobile, code, fcmToken);

  /// Create a copy of VerifyBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyBodyImplCopyWith<_$VerifyBodyImpl> get copyWith =>
      __$$VerifyBodyImplCopyWithImpl<_$VerifyBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyBodyImplToJson(
      this,
    );
  }
}

abstract class _VerifyBody implements VerifyBody {
  const factory _VerifyBody(
      {final String? mobile,
      final String? code,
      final String? fcmToken}) = _$VerifyBodyImpl;

  factory _VerifyBody.fromJson(Map<String, dynamic> json) =
      _$VerifyBodyImpl.fromJson;

  @override
  String? get mobile;
  @override
  String? get code;
  @override
  String? get fcmToken;

  /// Create a copy of VerifyBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyBodyImplCopyWith<_$VerifyBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ResendCodeBody _$ResendCodeBodyFromJson(Map<String, dynamic> json) {
  return _ResendCodeBody.fromJson(json);
}

/// @nodoc
mixin _$ResendCodeBody {
  String? get mobile => throw _privateConstructorUsedError;

  /// Serializes this ResendCodeBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ResendCodeBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResendCodeBodyCopyWith<ResendCodeBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResendCodeBodyCopyWith<$Res> {
  factory $ResendCodeBodyCopyWith(
          ResendCodeBody value, $Res Function(ResendCodeBody) then) =
      _$ResendCodeBodyCopyWithImpl<$Res, ResendCodeBody>;
  @useResult
  $Res call({String? mobile});
}

/// @nodoc
class _$ResendCodeBodyCopyWithImpl<$Res, $Val extends ResendCodeBody>
    implements $ResendCodeBodyCopyWith<$Res> {
  _$ResendCodeBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResendCodeBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
  }) {
    return _then(_value.copyWith(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ResendCodeBodyImplCopyWith<$Res>
    implements $ResendCodeBodyCopyWith<$Res> {
  factory _$$ResendCodeBodyImplCopyWith(_$ResendCodeBodyImpl value,
          $Res Function(_$ResendCodeBodyImpl) then) =
      __$$ResendCodeBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? mobile});
}

/// @nodoc
class __$$ResendCodeBodyImplCopyWithImpl<$Res>
    extends _$ResendCodeBodyCopyWithImpl<$Res, _$ResendCodeBodyImpl>
    implements _$$ResendCodeBodyImplCopyWith<$Res> {
  __$$ResendCodeBodyImplCopyWithImpl(
      _$ResendCodeBodyImpl _value, $Res Function(_$ResendCodeBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResendCodeBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = freezed,
  }) {
    return _then(_$ResendCodeBodyImpl(
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ResendCodeBodyImpl implements _ResendCodeBody {
  const _$ResendCodeBodyImpl({this.mobile});

  factory _$ResendCodeBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$ResendCodeBodyImplFromJson(json);

  @override
  final String? mobile;

  @override
  String toString() {
    return 'ResendCodeBody(mobile: $mobile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResendCodeBodyImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, mobile);

  /// Create a copy of ResendCodeBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResendCodeBodyImplCopyWith<_$ResendCodeBodyImpl> get copyWith =>
      __$$ResendCodeBodyImplCopyWithImpl<_$ResendCodeBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ResendCodeBodyImplToJson(
      this,
    );
  }
}

abstract class _ResendCodeBody implements ResendCodeBody {
  const factory _ResendCodeBody({final String? mobile}) = _$ResendCodeBodyImpl;

  factory _ResendCodeBody.fromJson(Map<String, dynamic> json) =
      _$ResendCodeBodyImpl.fromJson;

  @override
  String? get mobile;

  /// Create a copy of ResendCodeBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResendCodeBodyImplCopyWith<_$ResendCodeBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
