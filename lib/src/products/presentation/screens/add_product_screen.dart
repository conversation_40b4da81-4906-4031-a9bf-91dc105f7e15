import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_expansion_tile.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/base_info_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/options_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/pricing_discount_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class AddProductScreen extends StatefulWidget implements AutoRouteWrapper {
  const AddProductScreen({super.key, required this.subcategoryId});

  final int subcategoryId;

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AddProductScreenState extends State<AddProductScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    return Scaffold(
        appBar: DefaultAppBar(context, title: Text(context.l10n.addProduct)),
        body: BlocListener<CategoryCubit, CategoryState>(
          listener: (context, state) {
            state.whenOrNull(
              addProductFailure: (message) =>
                  UiHelper.onFailure(context, message),
              addProductLoading: () => UiHelper.onLoading(context),
              addProductsuccess: () {
                UiHelper.onSuccess(context);
                setState(() {});
              },
            );
          },
          child: Column(
            spacing: 16,
            children: [
              Expanded(
                child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16).copyWith(bottom: 32),
                    child:
                        // cubit.oprations.product == null
                        //     ?
                        Column(
                      spacing: 16,
                      children: [
                        CustomExpansionTile(
                          initiallyExpanded: true,
                          title: context.l10n.baseInformation,
                          children: [
                            Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12),
                                ),
                              ),
                              child: const BaseInfoSection(),
                            ),
                          ],
                        ),
                        CustomExpansionTile(
                          title: context.l10n.pricingAndDiscount,
                          children: [
                            Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12),
                                ),
                              ),
                              child: const PricingDiscountSection(),
                            ),
                          ],
                        ),
                        BlocBuilder<CategoryCubit, CategoryState>(
                          buildWhen: (previous, current) {
                            return current.maybeWhen(
                              toogleHasOptionsKey: () => true,
                              orElse: () => false,
                            );
                          },
                          builder: (context, state) {
                            return cubit.oprations.productDataWrapper
                                        .hasOptions ==
                                    true
                                ? CustomExpansionTile(
                                    title: context.l10n.options,
                                    children: [
                                      Container(
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.only(
                                            bottomLeft: Radius.circular(12),
                                            bottomRight: Radius.circular(12),
                                          ),
                                        ),
                                        child: const OptionsSection(),
                                      ),
                                    ],
                                  )
                                : const SizedBox();
                          },
                        )
                      ],
                    )
                    // :
                    // Column(spacing: 16, children: [
                    //     CustomExpansionTile(
                    //       initiallyExpanded: true,
                    //       title: context.l10n.additions,
                    //       children: [
                    //         Container(
                    //           decoration: const BoxDecoration(
                    //             color: Colors.white,
                    //             borderRadius: BorderRadius.only(
                    //               bottomLeft: Radius.circular(12),
                    //               bottomRight: Radius.circular(12),
                    //             ),
                    //           ),
                    //           child: const AdditionsSection(),
                    //         ),
                    //       ],
                    //     ),
                    //   ]),
                    ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0)
                    .copyWith(bottom: 32),
                child: CustomElevatedButton(
                  onPressed: () {
                    _onSave();
                  },
                  child: Text(context.l10n.save),
                ),
              )
            ],
          ),
        ));
  }

  void _onSave() async {
    final cubit = injector<CategoryCubit>();
    if (cubit.oprations.product == null) {
      cubit.oprations.productDataWrapper.productOptions =
          cubit.oprations.productOptionsView;
      if (cubit.oprations.productDataWrapper.image == null ||
          cubit.oprations.productDataWrapper.nameAr == null ||
          cubit.oprations.productDataWrapper.categoriesId == [] ||
          cubit.oprations.productDataWrapper.defaultPrice == null ||
          (cubit.oprations.productDataWrapper.hasOptions == true &&
              cubit.oprations.generatedProductsOptionsIds.isEmpty)) {
        UiHelper.showCustomSnackBar(
          context: context,
          message: context.l10n.pleaseEnterTheProductName,
          backgroundColor: Colors.red,
        );
      } else if (cubit.oprations.productDataWrapper.discountType == 0 &&
          cubit.oprations.productDataWrapper.discount != null &&
          cubit.oprations.productDataWrapper.discount! >=
              cubit.oprations.productDataWrapper.defaultPrice!) {
        UiHelper.showCustomSnackBar(
          context: context,
          message: context.l10n.discountCannotBeGreaterThanDefaultPrice,
          backgroundColor: Colors.red,
        );
      } else if (cubit.oprations.productDataWrapper.discountType == 1 &&
          cubit.oprations.productDataWrapper.discount != null &&
          cubit.oprations.productDataWrapper.discount! >= 100) {
        UiHelper.showCustomSnackBar(
          context: context,
          message: context.l10n.discountCannotBeGreaterThan100,
          backgroundColor: Colors.red,
        );
      } else {
        await cubit.addProduct();
        context.router.push(ProductsRoute(
            category: injector<CategoryCubit>().oprations.category,
            subcategoryId: widget.subcategoryId));
      }
    } else {
      context.router.maybePop();
    }
  }
}
