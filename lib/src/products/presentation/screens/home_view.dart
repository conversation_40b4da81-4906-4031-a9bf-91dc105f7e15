import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/cubit/bottom_nav_bar_cubit.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/home_top_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/orders_section.dart';
import 'package:alsarea_store/src/terms/presentation/screens/cubit/terms_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  late String? token = '';

  @override
  void initState() {
    super.initState();
    injector<CategoryCubit>()
      ..getCities()
      ..getAppActivites()
      ..storeOrdersCount()
      ..getGroups()
      ..subcategoriesList()
      ..additionList()
      ..getProfile();
    injector<TermsCubit>().getTerms();
    getToken();
  }

  void getToken() async {
    final String? token =
        await SharedPreferencesHelper.getSecuredString('token');
    setState(() {
      this.token = token;
    });
  }

  @override
  Widget build(BuildContext context) {
    final services = [
      {
        'icon': Assets.icons.orders.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.orders,
        'index': 1,
        'route': null,
        'count': true
      },
      {
        'icon': Assets.icons.products.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.products,
        'index': 2,
        'route': null,
        'count': false
      },
      {
        'icon': Assets.icons.options.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.choices,
        'index': 0,
        'route': const ChoicesRoute(),
        'count': false
      },
      {
        'icon': Assets.icons.additions.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.additions,
        'index': 0,
        'route': const AdditionsRoute(),
        'count': false
      },
      {
        'icon': Assets.icons.report.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.salesReport,
        'index': 0,
        'route': const SalesReportRoute(),
        'count': false
      },
      {
        'icon': Assets.icons.userIcon.svg(
            height: 50,
            width: 50,
            colorFilter:
                const ColorFilter.mode(AppColors.primary, BlendMode.srcIn)),
        'label': context.l10n.myAccount,
        'index': 3,
        'route': null,
        'count': false
      },
    ];
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 16),
        child: BlocBuilder<CategoryCubit, CategoryState>(
            buildWhen: (previous, current) => current.maybeWhen(
                  getProfileSuccess: (_) => true,
                  getProfileFailure: (_) => true,
                  getProfileLoading: () => true,
                  orElse: () => false,
                ),
            builder: (context, state) {
              return state.maybeWhen(
                orElse: () => const SizedBox.shrink(),
                getProfileLoading: () => SizedBox(
                  height: 1.sh,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                getProfileFailure: (message) => Center(
                  child: token == ''
                      ? SizedBox(
                          height: 1.sh,
                          width: .95.sw,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                context.l10n.pleaseLoginToUnlock,
                                style: TextStyles.title20,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: .7.sw,
                                child: CustomElevatedButton(
                                  child: Text(context.l10n.login),
                                  onPressed: () {
                                    context.router.push(const LoginRoute());
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      : Text(
                          message,
                          style: const TextStyle(color: Colors.red),
                        ),
                ),
                getProfileSuccess: (profile) {
                  return Column(
                    spacing: 24,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HomeTopSection(profile: profile),
                      Padding(
                          padding: EdgeInsets.all(.025.sw),
                          child: Column(
                            children: [
                              Container(
                                alignment: Alignment.topRight,
                                child: Text(
                                  context.l10n.services,
                                  style: TextStyles.headline24.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Wrap(
                                alignment: WrapAlignment.center,
                                direction: Axis.horizontal,
                                spacing: 10.0,
                                runSpacing: 10.0,
                                children: services.map((service) {
                                  return OrdersSection(
                                    label: service['label'] as String,
                                    icon: service['icon'] as Widget,
                                    onTap: () {
                                      if (service['index'] != 0) {
                                        context
                                            .read<BottomNavBarCubit>()
                                            .changeIndex(
                                                service['index'] as int);
                                        context.router.popUntilRoot();
                                      } else {
                                        context.router.push(
                                            service['route'] as PageRouteInfo);
                                      }
                                    },
                                    count: service['count'] as bool,
                                  );
                                }).toList(),
                              ),
                            ],
                          )),
                    ],
                  );
                },
              );
            }),
      ),
    );
  }
}
