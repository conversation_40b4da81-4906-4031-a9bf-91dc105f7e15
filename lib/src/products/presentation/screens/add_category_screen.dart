import 'dart:developer';

import 'package:alsarea_store/core/helpers/app_helper.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/widgets/default_bottom_nav_bar.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_category/category_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_category/sub_category_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class AddCategoryScreen extends StatefulWidget implements AutoRouteWrapper {
  const AddCategoryScreen({super.key, this.category});

  final CategoryModel? category;

  @override
  State<AddCategoryScreen> createState() => _AddCategoryScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AddCategoryScreenState extends State<AddCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  CategoryModel? _category;
  @override
  void initState() {
    _category = widget.category;
    if (_category != null) {
      injector<CategoryCubit>().getCategory(_category?.id);
    }
    setState(() {
      // _category = CategoryOperations().category;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    log(_category.toString());
    String locale = AppHelper.getLocale();
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(_category == null
            ? context.l10n.addCategory
            : locale == 'ar'
                ? _category!.nameAr!
                : _category!.nameEn!),
      ),
      body: BlocListener<CategoryCubit, CategoryState>(
        listener: (context, state) {
          state.whenOrNull(
            addcategoryFailure: (message) =>
                UiHelper.onFailure(context, message),
            addcategoryLoading: () => UiHelper.onLoading(context),
            addcategorySuccess: (category) {
              UiHelper.onSuccess(context);
              injector<CategoryCubit>().getCategory(category?.id);
              setState(() {
                _category = category;
              });
            },
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Stack(
            children: [
              CustomScrollView(
                slivers: [
                  Form(
                      key: _formKey,
                      child: CategorySection(
                        category: _category,
                      )),
                  if (_category?.id != null)
                    SubCategorySection(
                      category: _category,
                    ),
                ],
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: CustomElevatedButton(
                  onPressed: _onSave,
                  child: Text(
                    context.l10n.save,
                    style: TextStyles.title14,
                  ),
                ),
              )
            ],
          ),
        ),
      ),
      bottomNavigationBar: const DefaultBottomNavBar(),
    );
  }

  void _onSave() {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    if (_category == null) {
      context.read<CategoryCubit>().oprations.dataWrapper.id = null;
      context.read<CategoryCubit>().addCategory();
    } else {
      context.read<CategoryCubit>().oprations.dataWrapper.id = _category!.id;
      context.read<CategoryCubit>().editCategory();
    }
  }
}
