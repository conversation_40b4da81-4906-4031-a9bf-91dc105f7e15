import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/addition_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_addition_dialog.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class AdditionsScreen extends StatefulWidget implements AutoRouteWrapper {
  const AdditionsScreen({super.key});

  @override
  State<AdditionsScreen> createState() => _AdditionsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AdditionsScreenState extends State<AdditionsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.additions),
      ),
      body: BlocConsumer<CategoryCubit, CategoryState>(
        listener: (context, state) {
          state.whenOrNull(editAdditionFailure: (message) {
            UiHelper.onFailure(context, message);
          }, editAdditionLoading: () {
            UiHelper.onLoading(context);
          }, editAdditionSuccess: () {
            UiHelper.onSuccess(context);
          });
        },
        buildWhen: (previous, current) => current.maybeWhen(
          editAdditionSuccess: () => true,
          editAdditionFailure: (_) => true,
          editAdditionLoading: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          final additionsList =
              injector<CategoryCubit>().oprations.additionsList;
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: CustomScrollView(
              slivers: [
                SliverFloatingHeader(
                  child: AddCardWidget(
                    title: context.l10n.addChoices,
                    onTap: () => UiHelper.showCustomDialog(
                      context: context,
                      dialog: const AddAdditionDialog(),
                    ),
                  ),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  sliver: SliverList.separated(
                    itemBuilder: (_, index) => ChoiceCard(
                        addition: additionsList[index],
                        onTap: () {},
                        onDelete: () {
                          injector<CategoryCubit>()
                              .deleteAddition(additionsList[index].id ?? 0);
                          Navigator.pop(context);
                        }),
                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                    itemCount: additionsList.length,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ChoiceCard extends StatelessWidget {
  const ChoiceCard(
      {super.key, required this.addition, this.onTap, required this.onDelete});
  final AdditionModel addition;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        onTap: onTap,
        title: Text(
          addition.name ?? '',
          style: TextStyles.body16,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize
              .min, // ensures the row only takes as much space as needed
          children: [
            IconButton(
              icon: Assets.icons.edit.svg(),
              onPressed: () {
                UiHelper.showCustomDialog(
                  context: context,
                  dialog: AddAdditionDialog(
                    addition: addition,
                  ),
                );
              },
            ),
            IconButton(
              icon: Assets.icons.delete.svg(),
              onPressed: () {
                UiHelper.showCustomDialogV1(
                    context: context,
                    onYesPressed: onDelete,
                    title: context.l10n.areYouSure);
              },
            ),
          ],
        ),
      ),
    );
  }
}
