import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/add_option_dialog.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/additions_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_info/images_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_info/product_description_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_info/product_details_section.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_info/product_info_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class ProductInfoScreen extends StatefulWidget implements AutoRouteWrapper {
  const ProductInfoScreen({super.key, required this.product});

  final ProductModel product;

  @override
  State<ProductInfoScreen> createState() => _ProductInfoScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _ProductInfoScreenState extends State<ProductInfoScreen> {
  @override
  void initState() {
    super.initState();
    injector<CategoryCubit>().getProduct(widget.product.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CategoryCubit>();
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.baseInformation),
        // actions: [
        // IconButton(
        //     icon: const Icon(Icons.edit),
        //     onPressed: () {
        //       // context.router.push(
        //       // AddProductRoute(product: product),
        //       // ),
        //     }),
        // IconButton(
        //     icon: const Icon(Icons.delete),
        //     onPressed: () {
        //       // context.read<CategoryCubit>().deleteProduct(product.id ?? 0),
        //     }),
        // ],
      ),
      body: BlocBuilder<CategoryCubit, CategoryState>(
        buildWhen: (previous, current) => current.maybeWhen(
          productSuccess: () => true,
          productFailure: (_) => true,
          productLoading: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          return state.maybeWhen(
              orElse: () => const SizedBox.shrink(),
              productLoading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
              productFailure: (message) => Center(
                    child: Text(
                      message,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              productSuccess: () {
                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16).copyWith(bottom: 64),
                  child: Column(
                    spacing: 16,
                    children: [
                      ImagesSection(
                        items: [
                          cubit.oprations.product?.imageUrl ?? '-',
                          ...cubit.oprations.product?.sliders ?? [],
                        ],
                      ),
                      ProductInfoSection(product: cubit.oprations.product!),
                      if (cubit.oprations.product?.options?.isEmpty == false)
                        if (cubit.oprations.product?.options?.length == 1 &&
                            cubit.oprations.product?.options?[0].optionDetails
                                    ?.isEmpty ==
                                true &&
                            cubit.oprations.product?.options?[0].additions
                                    ?.isEmpty ==
                                true)
                          const SizedBox()
                        else
                          Column(
                            children: [
                              ProductDetailsSection(
                                  product: cubit.oprations.product!,
                                  numOfOptions: cubit
                                          .oprations.product?.options?.length ??
                                      0),
                              const SizedBox(height: 10),
                              cubit.oprations.product?.options?[0].optionDetails
                                          ?.isEmpty ==
                                      true
                                  ? const SizedBox()
                                  : AddCardWidget(
                                      title: context.l10n.addChoices,
                                      onTap: () => UiHelper.showCustomDialog(
                                        context: context,
                                        barrierColor: Colors.black12,
                                        dialog: BlocProvider.value(
                                          value: injector<CategoryCubit>(),
                                          child: const AddOptionDialog(),
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                      if (cubit.oprations.product?.description != null)
                        ProductDescriptionSection(
                            product: cubit.oprations.product!),
                      const AdditionsSection(),
                    ],
                  ),
                );
              });
        },
      ),
    );
  }
}
