import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/app_helper.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/widgets/custom/custom_refresh_widget.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/filter_sheet.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_card.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

@RoutePage()
class ProductsScreen extends StatefulWidget implements AutoRouteWrapper {
  const ProductsScreen({super.key, this.category, this.subcategoryId});

  final CategoryModel? category;
  final int? subcategoryId;

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _ProductsScreenState extends State<ProductsScreen>
    with SingleTickerProviderStateMixin {
  String searchText = '';
  late TextEditingController _controller;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    final cubit = context.read<CategoryCubit>();
    cubit.oprations.productsDataWrapper.subCategoryId = widget.subcategoryId;
    injector<CategoryCubit>().getProducts();
    _controller = TextEditingController(text: searchText);
    _tabController = TabController(
      length: (widget.category?.subCategories?.length ?? 0) + 1,
      vsync: this,
      initialIndex: widget.subcategoryId == null
          ? 0
          : ((widget.category?.subCategories?.indexWhere((element) =>
                      element.subCategoryId == widget.subcategoryId) ??
                  -1) +
              1),
    ); // Start on 2nd tab (index 1)

    // injector<CategoryCubit>().getProducts();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CategoryCubit>();
    final category = cubit.oprations.category;
    final subCategories = category.subCategories;
    String locale = AppHelper.getLocale();

    return DefaultTabController(
      length: subCategories != null ? subCategories.length + 1 : 1,
      child: Scaffold(
          appBar: DefaultAppBar(
            context,
            title: category.nameAr == null
                ? Text(context.l10n.products)
                : Text(category.nameAr!),
            actions: [
              IconButton(
                onPressed: () => UiHelper.showCustomBottomSheet(
                  context: context,
                  child: const FilterSheet(),
                ),
                icon: const Icon(Icons.filter_alt_outlined),
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorColor: AppColors.primary,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.black,
              tabAlignment: TabAlignment.start,
              isScrollable: true,
              onTap: (index) {
                if (index == 0) {
                  cubit.oprations.productsDataWrapper.subCategoryId = null;
                  injector<CategoryCubit>().getProducts();
                } else {
                  cubit.oprations.productsDataWrapper.subCategoryId =
                      subCategories![index - 1].subCategoryId;
                  injector<CategoryCubit>().getProducts();
                }
              },
              tabs: [
                Tab(text: context.l10n.all),
                if (subCategories != null)
                  ...subCategories.map((e) => Tab(
                      text: locale == 'ar'
                          ? e.subCategoryNameAr
                          : e.subCategoryNameEn)),
              ],
            ),
          ),
          body: Column(
            children: [
              const SizedBox(
                height: 5,
              ),
              SizedBox(
                width: .95.sw,
                child: CustomTextField(
                  controller: _controller,
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        searchText = '';
                        _controller.clear();
                      });
                      // if (searchText.isEmpty) return;
                      cubit.oprations.productsDataWrapper.search = '';
                      injector<CategoryCubit>()
                          .getProducts(); // Refresh UI to hide the icon
                    },
                  ),
                  // initialValue: searchText,
                  hintText: context.l10n.productName,
                  fillColor: AppColors.lighterGrey,
                  onChanged: (value) {
                    setState(() {
                      searchText = value!;
                    });
                    cubit.oprations.productsDataWrapper.search = value;
                    injector<CategoryCubit>().getProducts();
                  },
                  // onSubmitted: (value) {
                  //   cubit.oprations.productsDataWrapper.search =
                  //       value;
                  //   injector<CategoryCubit>().getProducts();
                  // },
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: AddCardWidget(
                  title: context.l10n.addProduct,
                  onTap: () {
                    injector<CategoryCubit>().oprations.product = null;
                    context.router.push(AddProductRoute(
                      subcategoryId: widget.subcategoryId ?? 0,
                    ));
                  },
                ),
              ),
              // const Expanded(child: ProductsEmptyContent()),
              BlocBuilder<CategoryCubit, CategoryState>(
                buildWhen: (previous, current) => current.maybeWhen(
                  productsSuccess: (_) => true,
                  productsFailure: (_) => true,
                  productsLoading: () => true,
                  orElse: () => false,
                ),
                builder: (context, state) {
                  return state.maybeWhen(
                    productsLoading: () => const Expanded(
                        child: Center(child: CircularProgressIndicator())),
                    productsFailure: (message) => Center(
                      child: Text(message,
                          style: const TextStyle(color: Colors.red)),
                    ),
                    productsSuccess: (products) {
                      return Expanded(
                        child: CustomRefreshWidget(
                          onRefresh: () async {
                            await injector<CategoryCubit>()
                                .getProducts(isLoadMore: false);
                          },
                          onLoading: () async {
                            await injector<CategoryCubit>()
                                .getProducts(isLoadMore: true);
                          },
                          child:
                              // products.isEmpty
                              //     ? const ProductsEmptyContent()
                              // :
                              ListView.separated(
                            padding: const EdgeInsets.all(16).copyWith(top: 0),
                            itemBuilder: (_, index) =>
                                ProductCard(product: products[index]),
                            separatorBuilder: (_, __) =>
                                const SizedBox(height: 16),
                            itemCount: products.length,
                          ),
                        ),
                      );
                    },
                    orElse: () => const SizedBox.shrink(),
                  );
                },
              ),
            ],
          )),
    );
  }
}

// class ProductCard extends StatelessWidget {
//   const ProductCard({super.key, required this.product});
//   final ProductModel product;

//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: ListTile(
//         onTap: () async {
//           log(product.id.toString());
//           // injector<CategoryCubit>().getProduct(product.id!);
//           context.router.push(ProductInfoRoute(product: product));
//         },
//         contentPadding: const EdgeInsets.all(4),
//         minVerticalPadding: 0,
//         visualDensity: const VisualDensity(
//           horizontal: 4,
//           vertical: 4,
//         ),
//         leading: CustomNetworkImage(
//           product.imageUrl ?? '',
//           width: 64.w,
//           height: 64.w,
//         ),
//         trailing: IconButton(
//           onPressed: () {
//             // context.router.push(AddCategoryRoute(category: category))
//           },
//           icon: Assets.icons.edit.svg(),
//         ),
//         title: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               product.name ?? '',
//               style: TextStyles.title16.copyWith(
//                 fontWeight: FontWeight.w400,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
