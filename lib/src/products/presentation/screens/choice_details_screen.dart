import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_choice_dialog.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class ChoiceDetailsScreen extends StatefulWidget implements AutoRouteWrapper {
  const ChoiceDetailsScreen({super.key, required this.group});

  final GroupModel group;

  @override
  State<ChoiceDetailsScreen> createState() => _ChoiceDetailsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _ChoiceDetailsScreenState extends State<ChoiceDetailsScreen> {
  GroupModel? _group;
  @override
  void initState() {
    _group = widget.group;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    log(_group.toString());
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(_group?.name ?? ''),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomScrollView(
          slivers: [
            SliverFloatingHeader(
              child: AddCardWidget(
                title: context.l10n.addChoices,
                onTap: () => UiHelper.showCustomDialog(
                  context: context,
                  dialog: AddChoiceDialog(
                    group: widget.group,
                  ),
                ),
              ),
            ),
            BlocConsumer<CategoryCubit, CategoryState>(
                listener: (context, state) {
                  state.whenOrNull(
                    createOptionFailure: (message) =>
                        UiHelper.onFailure(context, message),
                    createOptionLoading: () => UiHelper.onLoading(context),
                    createOptionSuccess: () {
                      UiHelper.onSuccess(context);
                    },
                  );
                },
                buildWhen: (previous, current) => current.maybeWhen(
                      createOptionSuccess: () => true,
                      createOptionFailure: (_) => true,
                      createOptionLoading: () => true,
                      orElse: () => false,
                    ),
                builder: (context, state) {
                  final options = injector<CategoryCubit>()
                      .oprations
                      .groupsList
                      .firstWhere((element) => element.id == widget.group.id)
                      .options;
                  log('gdfhg');
                  return SliverPadding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    sliver: SliverList.separated(
                      itemBuilder: (_, index) => SubChoiceCard(
                          group: widget.group,
                          option: options![index],
                          onDelete: () {
                            injector<CategoryCubit>()
                                .deleteOption(options[index].id ?? 0);
                            Navigator.pop(context);
                          }),
                      separatorBuilder: (_, __) => const SizedBox(height: 8),
                      itemCount: options?.length,
                    ),
                  );
                }),
          ],
        ),
      ),
    );
  }
}

class SubChoiceCard extends StatelessWidget {
  const SubChoiceCard(
      {super.key, required this.option, required this.group, this.onDelete});
  final OptionModel option;
  final GroupModel group;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(
          option.name ?? '',
          style: TextStyles.body16,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              visualDensity: VisualDensity.compact,
              onPressed: () {
                UiHelper.showCustomDialog(
                  context: context,
                  dialog: AddChoiceDialog(
                    group: group,
                    option: option,
                  ),
                );
              },
              icon: Assets.icons.edit.svg(),
            ),
            IconButton(
              icon: Assets.icons.delete.svg(),
              onPressed: () {
                UiHelper.showCustomDialogV1(
                    context: context,
                    onYesPressed: onDelete,
                    title: context.l10n.areYouSure);
              },
            ),
          ],
        ),
      ),
    );
  }
}
