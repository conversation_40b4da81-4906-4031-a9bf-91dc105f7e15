import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/product_choices_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

@RoutePage()
class ProductChoicesScreen extends StatelessWidget {
  const ProductChoicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<GroupModel> choices =
        injector<CategoryCubit>().oprations.groupsList;

    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.productChoices),
      ),
      body: Column(
        // spacing: 16,
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemBuilder: (_, index) => ChoiceSection(
                choice: choices[index],
                index: index,
              ),
              separatorBuilder: (_, __) => const SizedBox(height: 24),
              itemCount: choices.length,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16).copyWith(bottom: 32),
            child: Column(
              spacing: 16,
              children: [
                CustomElevatedButton(
                  onPressed: () {
                    context.router.maybePop();
                    injector<CategoryCubit>().generateProductsOptions();
                  },
                  child: Text(context.l10n.save),
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.error_rounded,
                      size: 20,
                      color: Colors.red,
                    ),
                    GestureDetector(
                      onTap: () {
                        context.router.push(const ChoicesRoute());
                      },
                      child: RichText(
                        text: TextSpan(
                          style: const TextStyle(fontFamily: FontFamily.somar),
                          children: [
                            TextSpan(
                              text: '${context.l10n.toAddMoreOptionsGoToPage} ',
                              style: TextStyles.body12.copyWith(
                                color: AppColors.black,
                              ),
                            ),
                            TextSpan(
                              text: context.l10n.productOptions,
                              style: TextStyles.body12.copyWith(
                                color: AppColors.primary,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
