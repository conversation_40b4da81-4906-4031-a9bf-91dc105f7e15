import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_group_dialog.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class ChoicesScreen extends StatefulWidget implements AutoRouteWrapper {
  const ChoicesScreen({super.key});

  @override
  State<ChoicesScreen> createState() => _ChoicesScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _ChoicesScreenState extends State<ChoicesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.choices),
      ),
      body: BlocConsumer<CategoryCubit, CategoryState>(
        listener: (context, state) {
          state.whenOrNull(editGroupFailure: (message) {
            UiHelper.onFailure(context, message);
          }, editGroupLoading: () {
            UiHelper.onLoading(context);
          }, editGroupSuccess: () {
            UiHelper.onSuccess(context);
          });
        },
        buildWhen: (previous, current) => current.maybeWhen(
          editGroupSuccess: () => true,
          editGroupFailure: (_) => true,
          editGroupLoading: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          final choices = injector<CategoryCubit>().oprations.groupsList;
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: CustomScrollView(
              slivers: [
                SliverFloatingHeader(
                  child: AddCardWidget(
                    title: context.l10n.addChoices,
                    onTap: () => UiHelper.showCustomDialog(
                      context: context,
                      dialog: const AddGroupDialog(),
                    ),
                  ),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  sliver: SliverList.separated(
                    itemBuilder: (_, index) => ChoiceCard(
                        group: choices[index],
                        onTap: () {
                          context.router
                              .push(ChoiceDetailsRoute(group: choices[index]));
                        },
                        onDelete: () {
                          injector<CategoryCubit>()
                              .deleteGroup(choices[index].id ?? 0);
                          Navigator.pop(context);
                        }),
                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                    itemCount: choices.length,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class ChoiceCard extends StatelessWidget {
  const ChoiceCard(
      {super.key, required this.group, this.onTap, required this.onDelete});
  final GroupModel group;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        onTap: onTap,
        title: Text(
          group.name ?? '',
          style: TextStyles.body16,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize
              .min, // ensures the row only takes as much space as needed
          children: [
            IconButton(
              icon: Assets.icons.edit.svg(),
              onPressed: () {
                UiHelper.showCustomDialog(
                  context: context,
                  dialog: AddGroupDialog(
                    group: group,
                  ),
                );
              },
            ),
            IconButton(
              icon: Assets.icons.delete.svg(),
              onPressed: () {
                UiHelper.showCustomDialogV1(
                    context: context,
                    onYesPressed: onDelete,
                    title: context.l10n.areYouSure);
              },
            ),
          ],
        ),
      ),
    );
  }
}
