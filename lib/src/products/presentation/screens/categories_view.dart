import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/core/widgets/custom/custom_refresh_widget.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoriesView extends StatefulWidget {
  const CategoriesView({super.key});

  @override
  State<CategoriesView> createState() => _CategoriesViewState();
}

class _CategoriesViewState extends State<CategoriesView> {
  late String? token = '';
  @override
  void initState() {
    super.initState();
    injector<CategoryCubit>().getCategories();
    getToken();
  }

  void getToken() async {
    final String? token =
        await SharedPreferencesHelper.getSecuredString('token');
    setState(() {
      this.token = token;
    });
  }

  @override
  Widget build(BuildContext context) {
    // final cubit = context.read<CategoryCubit>();
    // final categories = List.generate(16, (index) => index);
    return Scaffold(
      // appBar: DefaultAppBar(
      //   context,
      //   title: RichText(
      //     text: TextSpan(
      //       style: const TextStyle(fontFamily: FontFamily.somar),
      //       children: [
      //         TextSpan(
      //           text: '${context.l10n.welcome}, ',
      //           style: TextStyles.title16.copyWith(
      //             fontWeight: FontWeight.w400,
      //             color: Colors.black,
      //           ),
      //         ),
      //         TextSpan(
      //           text: 'مطعم اكلة هنية',
      //           style: TextStyles.title16.copyWith(
      //             fontWeight: FontWeight.w600,
      //             color: AppColors.primary,
      //           ),
      //         ),
      //       ],
      //     ),
      //   ),
      // ),
      body: BlocBuilder<CategoryCubit, CategoryState>(
        buildWhen: (previous, current) => current.maybeWhen(
          categoriesSuccess: (_) => true,
          categoriesFailure: (_) => true,
          categoriesLoading: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          return state.maybeWhen(
              orElse: () => const SizedBox.shrink(),
              categoriesLoading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
              categoriesFailure: (message) => Center(
                    child: token == ''
                        ? SizedBox(
                            height: 1.sh,
                            width: .95.sw,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  context.l10n.pleaseLoginToUnlock,
                                  style: TextStyles.title20,
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: .7.sw,
                                  child: CustomElevatedButton(
                                    child: Text(context.l10n.login),
                                    onPressed: () {
                                      context.router.push(const LoginRoute());
                                    },
                                  ),
                                ),
                              ],
                            ),
                          )
                        : Text(
                            message,
                            style: const TextStyle(color: Colors.red),
                          ),
                  ),
              categoriesSuccess: (categories) {
                return Padding(
                  padding: EdgeInsets.only(
                      bottom: 16.0, top: .08.sh, right: 0.05.sw, left: 0.05.sw),
                  child: Column(
                    spacing: 8.0,
                    children: [
                      AddCardWidget(
                        title: context.l10n.addCategory,
                        onTap: () => context.router.push(AddCategoryRoute()),
                      ),
                      Expanded(
                        child: CustomRefreshWidget(
                          onRefresh: () async {
                            await injector<CategoryCubit>().getCategories(
                              isLoadMore: false,
                            );
                          },
                          onLoading: () async {
                            await injector<CategoryCubit>().getCategories(
                              isLoadMore: true,
                            );
                          },
                          child: categories.isNotEmpty
                              ? ListView.separated(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  itemBuilder: (context, index) => CategoryCard(
                                    category: categories[index],
                                    onDelete: () {
                                      injector<CategoryCubit>().deleteCategory(
                                          categories[index].id ?? 0);
                                      setState(() {
                                        categories.removeWhere((item) =>
                                            item.id == categories[index].id);
                                      });
                                      Navigator.pop(context);
                                    },
                                  ),
                                  separatorBuilder: (_, __) =>
                                      const SizedBox(height: 8),
                                  itemCount: categories.length,
                                )
                              : SizedBox(
                                  child: Center(
                                    child: Text(
                                      context.l10n.noData,
                                      style: TextStyles.title22,
                                    ),
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                );
              });
        },
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  const CategoryCard(
      {super.key, required this.category, required this.onDelete});
  final CategoryModel category;
  final VoidCallback? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        onTap: () async => {
          context.router.push(AddCategoryRoute(category: category)),
        },
        contentPadding: const EdgeInsets.all(4),
        minVerticalPadding: 0,
        visualDensity: const VisualDensity(
          horizontal: 4,
          vertical: 4,
        ),
        leading: CustomNetworkImage(
          category.image ?? '',
          width: 64.w,
          height: 64.w,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize
              .min, // ensures the row only takes as much space as needed
          children: [
            IconButton(
              onPressed: () =>
                  context.router.push(AddCategoryRoute(category: category)),
              icon: Assets.icons.edit.svg(),
            ),
            IconButton(
              icon: Assets.icons.delete.svg(),
              onPressed: () {
                UiHelper.showCustomDialogV1(
                    context: context,
                    onYesPressed: onDelete,
                    title: context.l10n.areYouSure);
              },
            ),
          ],
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              category.nameAr ?? '',
              style: TextStyles.title16.copyWith(
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              '${category.subCategoriesCount ?? ''} ${context.l10n.subCategories}',
              style: TextStyles.body14.copyWith(
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
