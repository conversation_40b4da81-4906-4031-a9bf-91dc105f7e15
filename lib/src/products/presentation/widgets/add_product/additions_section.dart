import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/add_addition_dialog.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AdditionsSection extends StatefulWidget implements AutoRouteWrapper {
  const AdditionsSection({super.key});

  @override
  State<AdditionsSection> createState() => _AdditionsSectionState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AdditionsSectionState extends State<AdditionsSection> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: BlocListener<CategoryCubit, CategoryState>(
        listener: (context, state) {
          state.whenOrNull(
            addProductAdditionFailure: (message) =>
                UiHelper.onFailure(context, message),
            addProductAdditionLoading: () => UiHelper.onLoading(context),
            addProductAdditionsuccess: () {
              UiHelper.onSuccess(context);
              setState(() {});
            },
          );
        },
        child: Column(
          spacing: 16,
          children: [
            AddCardWidget(
              title: context.l10n.additions,
              onTap: () => UiHelper.showCustomDialog(
                context: context,
                dialog: BlocProvider.value(
                  value: injector<CategoryCubit>(),
                  child: const AddAdditionDialog(),
                ),
              ),
            ),
            // ListView.builder(
            //   padding: EdgeInsets.zero,
            //   physics: const NeverScrollableScrollPhysics(),
            //   shrinkWrap: true,
            //   itemCount:
            //       injector<CategoryCubit>().oprations.productAdditions.length,
            //   itemBuilder: (_, index) => AdditionItem(
            //     addition:
            //         injector<CategoryCubit>().oprations.productAdditions[index],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

class AdditionItem extends StatelessWidget {
  const AdditionItem({super.key, required this.addition});

  final ProductAdditionModel addition;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      visualDensity: VisualDensity.compact,
      title: Text('${addition.nameAr} - ${addition.price} ${context.l10n.egp}'),
      leading: Assets.icons.addition.svg(),
      // trailing: Row(
      //   mainAxisSize: MainAxisSize.min,
      //   children: [
      //     IconButton(
      //       onPressed: () {},
      //       icon: Assets.icons.edit.svg(),
      //       visualDensity: VisualDensity.compact,
      //     ),
      //     IconButton(
      //       onPressed: () {},
      //       icon: Assets.icons.delete.svg(),
      //       visualDensity: VisualDensity.compact,
      //     ),
      //   ],
      // ),
    );
  }
}
