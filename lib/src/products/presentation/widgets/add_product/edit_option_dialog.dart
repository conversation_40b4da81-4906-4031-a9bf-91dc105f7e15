import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EditOptionDialog extends StatelessWidget implements AutoRouteWrapper {
  EditOptionDialog({super.key, this.option});
  ProductOption? option;

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    final formKey = GlobalKey<FormState>();
    return BlocListener<CategoryCubit, CategoryState>(
      listener: (context, state) {
        state.whenOrNull(
            // addProductOptionFailure: (message) {
            //   UiHelper.onFailure(context, message);
            //   // UiHelper.showCustomDialog(
            //   //   context: context,
            //   //   dialog: const AlertDialog(),
            //   // );
            // },
            // addProductOptionLoading: () => UiHelper.onLoading(context),
            // addProductOptionsuccess: () {
            //   UiHelper.onSuccess(context);
            //   context.router.maybePop();
            // },
            );
      },
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(32),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
        content: SizedBox(
          width: 1.sw,
          child: Form(
            key: formKey,
            child: Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 16,
                children: [
                  LabeledField(
                    label: context.l10n.price,
                    field: CustomTextField(
                      hintText: '0.0',
                      suffix: Text(context.l10n.egp),
                      onChanged: (value) => cubit
                          .oprations
                          .productOptionDataWrapper
                          .price = double.tryParse(value ?? '0'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterTheProductName;
                        }
                        return null;
                      },
                    ),
                  ),
                  CustomElevatedButton(
                    onPressed: () {
                      if (!formKey.currentState!.validate()) return;
                      formKey.currentState!.save();
                      if (option?.id != null) {
                        editProductOption(option!);
                      } else {
                        editProductWithoutOption(cubit.oprations.product!);
                      }
                      context.router.maybePop();
                    },
                    child: Text(context.l10n.add),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );

  void editProductWithoutOption(ProductModel product) {
    final cubit = injector<CategoryCubit>().oprations;
    cubit.productOptionDataWrapper.productOptionId = product.options?.first.id;
    cubit.productOptionDataWrapper.hasOptions = false;
    cubit.productOptionDataWrapper.nameAr = product.name;
    cubit.productOptionDataWrapper.nameEn = product.name;
    cubit.productOptionDataWrapper.descriptionAr = product.description;
    cubit.productOptionDataWrapper.descriptionEn = product.description;
    cubit.productOptionDataWrapper.price = cubit.productOptionDataWrapper.price;
    cubit.productOptionDataWrapper.discountExpiration =
        product.options?.first.discountExpiration;
    cubit.productOptionDataWrapper.discount = product.options?.first.discount;
    cubit.productOptionDataWrapper.isActive = product.options?.first.isActive;
    cubit.productOptionDataWrapper.isAvailable = product.isAvailable;
    injector<CategoryCubit>().editProduct();
  }

  void editProductOption(ProductOption productOption) {
    final cubit = injector<CategoryCubit>().oprations;
    cubit.productOptionDataWrapper.productOptionId = productOption.id;
    cubit.productOptionDataWrapper.hasOptions = true;
    cubit.productOptionDataWrapper.nameAr = null;
    cubit.productOptionDataWrapper.nameEn = null;
    cubit.productOptionDataWrapper.descriptionAr = null;
    cubit.productOptionDataWrapper.descriptionEn = null;
    cubit.productOptionDataWrapper.price = cubit.productOptionDataWrapper.price;
    cubit.productOptionDataWrapper.discountExpiration =
        productOption.discountExpiration;
    cubit.productOptionDataWrapper.discount = productOption.discount;
    cubit.productOptionDataWrapper.isActive = productOption.isActive;
    cubit.productOptionDataWrapper.isAvailable = null;

    injector<CategoryCubit>().editProduct();
  }
}
