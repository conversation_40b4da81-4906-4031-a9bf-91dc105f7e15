import 'package:alsarea_store/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class IsProductHaveOptionsWidget extends StatelessWidget {
  const IsProductHaveOptionsWidget({
    super.key,
    required this.title,
    required this.value,
  });

  final String title;
  final String value;

  @override
  Widget build(BuildContext context) {
    final currentValue = context.watch<ToggleCubit<String>>().value;
    final bool isSelected = currentValue == value;
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.transparent,
        ),
      ),
      child: RadioListTile(
        visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
        fillColor: WidgetStateProperty.resolveWith(
          (state) => state.contains(WidgetState.selected)
              ? AppColors.primary
              : AppColors.lightGrey,
        ),
        title: Text(title),
        value: value,
        groupValue: currentValue,
        onChanged: (value) => {
          context.read<ToggleCubit<String>>().toggle(value!),
          if (value == 'yes')
            {
              injector<CategoryCubit>()
                  .oprations
                  .productDataWrapper
                  .hasOptions = true,
              injector<CategoryCubit>().toggleHasOptionKey()
            }
          else
            {
              injector<CategoryCubit>()
                  .oprations
                  .productDataWrapper
                  .hasOptions = false,
              injector<CategoryCubit>()
                  .oprations
                  .productDataWrapper
                  .productOptions = [],
              injector<CategoryCubit>().toggleHasOptionKey()
            }
        },
      ),
    );
  }
}
