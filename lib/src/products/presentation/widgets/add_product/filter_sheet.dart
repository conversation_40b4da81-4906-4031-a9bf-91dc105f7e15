import 'package:alsarea_store/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FilterSheet extends StatefulWidget {
  const FilterSheet({super.key});

  @override
  State<FilterSheet> createState() => _FilterSheetState();
}

class _FilterSheetState extends State<FilterSheet> {
  late final ToggleCubit<String?> salesToggle;
  late final ToggleCubit<String?> accordingToToggle;
  late final ToggleCubit<String?> quantityToggle;
  late final ToggleCubit<String?> discountToggle;

  @override
  void initState() {
    salesToggle = ToggleCubit<String?>();
    accordingToToggle = ToggleCubit<String?>();
    quantityToggle = ToggleCubit<String?>();
    discountToggle = ToggleCubit<String?>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        spacing: 10,
        children: [
          Container(
            width: 64,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 20,
                children: [
                  BlocProvider(
                    create: (context) => salesToggle,
                    child: FilterSection(
                      title: context.l10n.sales,
                      child: Row(
                        children: [
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.bestSelling,
                              value: 'BestSeller',
                              section: 'Sales',
                            ),
                          ),
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.lessSelling,
                              value: 'LowSeller',
                              section: 'Sales',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  BlocProvider(
                    create: (context) => accordingToToggle,
                    child: FilterSection(
                      title: context.l10n.accordingTo,
                      child: Row(
                        children: [
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.newest,
                              value: 'New',
                              section: 'ByDate',
                            ),
                          ),
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.oldest,
                              value: 'Old',
                              section: 'ByDate',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // BlocProvider(
                  //   create: (context) => quantityToggle,
                  //   child: FilterSection(
                  //     title: context.l10n.quantity,
                  //     child: Row(
                  //       children: [
                  //         Expanded(
                  //           child: OptionsItem(
                  //             title: context.l10n.most,
                  //             value: 'most',
                  //             section: 'Quantity',
                  //           ),
                  //         ),
                  //         Expanded(
                  //           child: OptionsItem(
                  //             title: context.l10n.least,
                  //             value: 'least',
                  //             section: 'Quantity',
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  // ),
                  BlocProvider(
                    create: (context) => discountToggle,
                    child: FilterSection(
                      title: context.l10n.discount,
                      child: Row(
                        children: [
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.withDiscount,
                              value: 'WithDiscount',
                              section: 'Discount',
                            ),
                          ),
                          Expanded(
                            child: OptionsItem(
                              title: context.l10n.withoutDiscount,
                              value: 'WithoutDiscount',
                              section: 'Discount',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox.shrink(),
                  Row(
                    spacing: 8,
                    children: [
                      Expanded(
                        flex: 2,
                        child: CustomElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            injector<CategoryCubit>().getProducts();
                          },
                          child: Text(context.l10n.confirm),
                        ),
                      ),
                      Expanded(
                        child: CustomElevatedButton.outline(
                          onPressed: () {
                            final cubit = injector<CategoryCubit>().oprations;
                            cubit.productsDataWrapper.sales = null;
                            cubit.productsDataWrapper.byDate = null;
                            cubit.productsDataWrapper.discount = null;
                            Navigator.pop(context);
                            injector<CategoryCubit>().getProducts();
                          },
                          foregroundColor: Colors.black,
                          child: Text(context.l10n.reset),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class FilterSection extends StatelessWidget {
  const FilterSection({super.key, required this.title, required this.child});

  final String title;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.title16,
        ),
        const Divider(height: 4),
        child,
      ],
    );
  }
}

class OptionsItem extends StatelessWidget {
  const OptionsItem({
    super.key,
    required this.title,
    required this.value,
    required this.section,
  });

  final String title;
  final String value;
  final String section;

  @override
  Widget build(BuildContext context) {
    final currentValue = context.watch<ToggleCubit<String?>>().value;
    return RadioListTile(
      toggleable: true,
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: TextStyles.body16.copyWith(
          fontWeight: FontWeight.w400,
        ),
      ),
      visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
      value: value,
      groupValue: currentValue,
      onChanged: (value) {
        context.read<ToggleCubit<String?>>().toggle(value);
        final cubit = injector<CategoryCubit>().oprations;
        if (section == 'Sales') {
          cubit.productsDataWrapper.sales = value;
        }
        if (section == 'ByDate') {
          cubit.productsDataWrapper.byDate = value;
        }
        if (section == 'Discount') {
          cubit.productsDataWrapper.discount = value;
        }
      },
      fillColor: WidgetStateProperty.resolveWith(
        (states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.yellow;
          }
          return AppColors.moreGrey;
        },
      ),
    );
  }
}
