import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddOptionDialog extends StatelessWidget implements AutoRouteWrapper {
  const AddOptionDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    final formKey = GlobalKey<FormState>();
    return BlocListener<CategoryCubit, CategoryState>(
      listener: (context, state) {
        state.whenOrNull(
          addProductOptionFailure: (message) {
            UiHelper.onFailure(context, message);
            // UiHelper.showCustomDialog(
            //   context: context,
            //   dialog: const AlertDialog(),
            // );
          },
          addProductOptionLoading: () => UiHelper.onLoading(context),
          addProductOptionsuccess: () {
            UiHelper.onSuccess(context);
            context.router.maybePop();
          },
        );
      },
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(32),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
        content: SizedBox(
          width: 1.sw,
          child: Form(
            key: formKey,
            child: Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 16,
                children: [
                  Column(
                    children: cubit.oprations.product!.groups!.map((group) {
                      return LabeledField(
                        label: group.name ?? '',
                        field: BlocBuilder<CategoryCubit, CategoryState>(
                          buildWhen: (previous, current) => current.maybeWhen(
                            additionListSuccess: () => true,
                            orElse: () => false,
                          ),
                          builder: (context, state) {
                            cubit.oprations.productOptionDataWrapper.optionIds =
                                [];
                            List<OptionModel> groupOptions =
                                cubit.oprations.groupsList
                                        .firstWhere(
                                          (groupListItem) =>
                                              groupListItem.id == group.id,
                                          orElse: () => const GroupModel(),
                                        )
                                        .options ??
                                    [];
                            // List<OptionModel> productGroups = cubit
                            //     .oprations.product!.groups!
                            //     .firstWhere(
                            //         (groupListItem) => groupListItem.id == group.id)
                            //     .options!;
                            return CustomDropDownButton(
                              hintText: group.name ?? '',
                              items: groupOptions.map((e) {
                                // bool isDisabled =
                                //     productGroups.any((g) => g.id == e.id);
                                return DropdownMenuItem(
                                    // enabled: !isDisabled,
                                    value: e,
                                    child: Text(
                                      e.name ?? '',
                                      // style: TextStyle(
                                      //   color:
                                      //       isDisabled ? Colors.grey : Colors.black,
                                      // ),
                                    ));
                              }).toList(),
                              onChanged: (value) {
                                final selectedOptionId =
                                    value?.id?.toInt() ?? 0;

                                // Find all option IDs from this group
                                final currentGroupOptionIds =
                                    groupOptions.map((e) => e.id).toList();
                                log(currentGroupOptionIds.toString());
                                // Remove any existing option from this group
                                cubit.oprations.productOptionDataWrapper
                                    .optionIds
                                    ?.removeWhere(
                                  (id) => currentGroupOptionIds.contains(id),
                                );
                                // Add the newly selected option ID
                                cubit.oprations.productOptionDataWrapper
                                    .optionIds
                                    ?.add(selectedOptionId);

                                log(cubit.oprations.productOptionDataWrapper
                                    .optionIds
                                    .toString());
                              },
                              onValidate: (value) {
                                if (value == null) {
                                  return context.l10n.pleaseEnterTheProductName;
                                }
                                return null;
                              },
                            );
                          },
                        ),
                      );
                    }).toList(),
                  ),
                  LabeledField(
                    label: context.l10n.price,
                    field: CustomTextField(
                      hintText: '0.0',
                      suffix: Text(context.l10n.egp),
                      onChanged: (value) => cubit
                          .oprations
                          .productOptionDataWrapper
                          .price = double.tryParse(value ?? '0'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterTheProductName;
                        }
                        return null;
                      },
                    ),
                  ),
                  CustomElevatedButton(
                    onPressed: () {
                      if (!formKey.currentState!.validate()) return;
                      formKey.currentState!.save();
                      // context.router.maybePop();
                      cubit.oprations.productOptionDataWrapper.productId =
                          cubit.oprations.product?.id;
                      cubit.oprations.productOptionDataWrapper.discount = 0;
                      cubit.oprations.productOptionDataWrapper.type = 0;
                      cubit.oprations.productOptionDataWrapper.quantity = 0;
                      cubit.oprations.productOptionDataWrapper.isActive = true;
                      injector<CategoryCubit>().createProductOption();
                    },
                    child: Text(context.l10n.add),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}
