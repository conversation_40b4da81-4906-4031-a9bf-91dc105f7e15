import 'dart:convert';

import 'package:alsarea_store/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/core/widgets/shared/upload_image.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/is_product_have_options_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BaseInfoSection extends StatefulWidget {
  const BaseInfoSection({
    super.key,
  });

  @override
  State<BaseInfoSection> createState() => _BaseInfoSectionState();
}

class _BaseInfoSectionState extends State<BaseInfoSection> {
  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    int subCategoryId = cubit.oprations.productsDataWrapper.subCategoryId ?? 0;
    cubit.oprations.productDataWrapper.categoriesId = [
      cubit.oprations.productsDataWrapper.subCategoryId ?? 0
    ];
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        spacing: 16,
        children: [
          Row(
            spacing: 16,
            children: [
              Expanded(
                  child: UploadImageWidget(
                label: context.l10n.baseImage,
                onUploadImage: (file) async {
                  if (file == null) {
                    cubit.oprations.productDataWrapper.image = null;
                  } else {
                    List<int> imageBytes = await file.readAsBytes();
                    cubit.oprations.productDataWrapper.image =
                        base64Encode(imageBytes);
                  }
                },
              )),
              Expanded(
                  child: UploadImageWidget(
                isMultiSelect: true,
                label: context.l10n.imageAlbum,
                onUploadImage: (file) async {
                  if (file == null) {
                  } else {
                    List<int> imageBytes = await file.readAsBytes();
                    cubit.oprations.productDataWrapper.covers = [
                      base64Encode(imageBytes)
                    ];
                  }
                },
              )),
            ],
          ),
          LabeledField(
            isRequired: true,
            label: context.l10n.productName,
            field: CustomTextField(
                hintText: context.l10n.productName,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.l10n.pleaseEnterTheProductName;
                  }
                  return null;
                },
                onChanged: (value) {
                  cubit.oprations.productDataWrapper.nameAr = value;
                  cubit.oprations.productDataWrapper.nameEn = value;
                }),
          ),
          // LabeledField(
          //   isRequired: true,
          //   label: context.l10n.productName,
          //   field: CustomTextField(
          //     hintText: context.l10n.productName,
          //     validator: (value) {
          //       if (value == null || value.isEmpty) {
          //         return context.l10n.pleaseEnterTheProductName;
          //       }
          //       return null;
          //     },
          //     onChanged: (value) =>
          //         cubit.oprations.productDataWrapper.nameEn = value,
          //   ),
          // ),
          LabeledField(
            isRequired: true,
            label: context.l10n.singleSubCategory,
            field: BlocBuilder<CategoryCubit, CategoryState>(
              buildWhen: (previous, current) => current.maybeWhen(
                subcategoriesListSuccess: () => true,
                orElse: () => false,
              ),
              builder: (context, state) {
                return CustomDropDownButton(
                  value: subCategoryId == 0
                      ? null
                      : injector<CategoryCubit>()
                          .oprations
                          .subCategoriesList
                          .firstWhere((item) => item.id == subCategoryId),
                  onChanged: (value) {
                    cubit.oprations.productDataWrapper.categoriesId = [
                      value?.id ?? 0
                    ];
                  },
                  hintText: context.l10n.selectCategory,
                  items: injector<CategoryCubit>()
                      .oprations
                      .subCategoriesList
                      .map((e) =>
                          DropdownMenuItem(value: e, child: Text(e.name ?? '')))
                      .toList(),
                );
              },
            ),
          ),
          BlocProvider(
            create: (context) => ToggleCubit<String>(value: 'yes'),
            child: LabeledField(
              label: context.l10n.isProductHaveMultipleOptions,
              field: Row(
                spacing: 16,
                children: [
                  Expanded(
                    child: IsProductHaveOptionsWidget(
                      title: context.l10n.yes,
                      value: 'yes',
                    ),
                  ),
                  Expanded(
                    child: IsProductHaveOptionsWidget(
                      title: context.l10n.no,
                      value: 'no',
                    ),
                  ),
                ],
              ),
            ),
          ),
          LabeledField(
            label: context.l10n.productDescription,
            field: CustomTextField(
                hintText: context.l10n.writeProductDescription,
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.l10n.pleaseEnterTheProductName;
                  }
                  return null;
                },
                onChanged: (value) {
                  cubit.oprations.productDataWrapper.descriptionAr = value;
                  cubit.oprations.productDataWrapper.descriptionEn = value;
                }),
          ),
          // LabeledField(
          //   label: context.l10n.productDescription,
          //   field: CustomTextField(
          //     hintText: context.l10n.writeProductDescription,
          //     maxLines: 5,
          //     validator: (value) {
          //       if (value == null || value.isEmpty) {
          //         return context.l10n.pleaseEnterTheProductName;
          //       }
          //       return null;
          //     },
          //     onChanged: (value) =>
          //         cubit.oprations.productDataWrapper.descriptionEn = value,
          //   ),
          // ),
        ],
      ),
    );
  }
}
