import 'dart:convert';

import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/core/widgets/shared/upload_image.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_view_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OptionsSection extends StatelessWidget {
  const OptionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: BlocBuilder<CategoryCubit, CategoryState>(
        buildWhen: (previous, current) => current.maybeWhen(
          generatedProductOptions: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          final cubit = injector<CategoryCubit>();
          return Column(
            spacing: 16,
            children: [
              LabeledField(
                label: context.l10n.productChoices,
                field: AddCardWidget(
                  onTap: () {
                    if (cubit.oprations.productDataWrapper.discountType == 0 &&
                        cubit.oprations.productDataWrapper.discount != null &&
                        cubit.oprations.productDataWrapper.discount! >=
                            cubit.oprations.productDataWrapper.defaultPrice!) {
                      UiHelper.showCustomSnackBar(
                        context: context,
                        message: context
                            .l10n.discountCannotBeGreaterThanDefaultPrice,
                        backgroundColor: Colors.red,
                      );
                    } else if (cubit
                                .oprations.productDataWrapper.discountType ==
                            1 &&
                        cubit.oprations.productDataWrapper.discount != null &&
                        cubit.oprations.productDataWrapper.discount! >= 100) {
                      UiHelper.showCustomSnackBar(
                        context: context,
                        message: context.l10n.discountCannotBeGreaterThan100,
                        backgroundColor: Colors.red,
                      );
                    } else {
                      context.router.push(const ProductChoicesRoute());
                    }
                  },
                  title: context.l10n.productChoices,
                ),
              ),
              // LabeledField(
              //   label: context.l10n.quantityOptions,
              //   field: CustomDropDownButton(
              //     hintText: context.l10n.quantityOptions,
              //     items: ['product', 'option']
              //         .map(
              //           (e) => DropdownMenuItem(
              //             value: e,
              //             child: Text(context.l10n.selectQuantityOptions(e)),
              //           ),
              //         )
              //         .toList(),
              //   ),
              // ),
              const FixedPriceForOptions(),
              ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemBuilder: (_, index) =>
                    BlocBuilder<CategoryCubit, CategoryState>(
                  buildWhen: (previous, current) => current.maybeWhen(
                    generatedProductOptionsPrices: () => true,
                    orElse: () => false,
                  ),
                  builder: (context, state) {
                    return ProductOptionCard(
                        option: injector<CategoryCubit>()
                            .oprations
                            .productOptionsView[index],
                        index: index);
                  },
                ),
                separatorBuilder: (_, __) => const SizedBox(height: 16),
                itemCount: injector<CategoryCubit>()
                    .oprations
                    .productOptionsView
                    .length,
              ),
            ],
          );
        },
      ),
    );
  }
}

class ProductOptionCard extends StatefulWidget {
  const ProductOptionCard(
      {super.key, required this.option, required this.index});

  final ProductOptionView option;
  final int index;

  @override
  State<ProductOptionCard> createState() => _ProductOptionCardState();
}

class _ProductOptionCardState extends State<ProductOptionCard> {
  late TextEditingController _priceController;
  final cubit = injector<CategoryCubit>();

  @override
  void initState() {
    super.initState();
    _priceController = TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    final newPrice = widget.option.price?.toString() ?? '';
    if (_priceController.text != newPrice) {
      _priceController.text = newPrice;
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGrey),
      ),
      height: 115,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          spacing: 16,
          children: [
            SizedBox(
              width: 72.w,
              child: UploadImageWidget(
                smallIcon: true,
                onUploadImage: (file) async {
                  if (file == null) {
                    cubit.oprations.productOptionsView[widget.index].image =
                        null;
                  } else {
                    List<int> imageBytes = await file.readAsBytes();
                    cubit.oprations.productOptionsView[widget.index].image =
                        base64Encode(imageBytes);
                  }
                },
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Row(
                    spacing: 16,
                    children: [
                      Expanded(
                        child: Text(
                          widget.option.name ?? '',
                          style: TextStyles.body16,
                        ),
                      ),
                      // const AvailableSwitch(),
                    ],
                  ),
                  Expanded(
                    child: Row(
                      spacing: 16,
                      children: [
                        // Expanded(
                        //   child: CustomTextField(
                        //     hintText: context.l10n.quantity,
                        //     keyboardType: TextInputType.number,
                        //     inputFormatters: [
                        //       FilteringTextInputFormatter.digitsOnly,
                        //     ],
                        //     onChanged: (value) => injector<CategoryCubit>()
                        //         .oprations
                        //         .productOptionsView[widget.index]
                        //         .quantity = int.tryParse(value ?? '0'),
                        //   ),
                        // ),
                        Expanded(
                          child: CustomTextField(
                            controller: _priceController,
                            hintText: context.l10n.price,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            suffix: Text(context.l10n.egp),
                            onChanged: (value) => injector<CategoryCubit>()
                                .oprations
                                .productOptionsView[widget.index]
                                .price = double.tryParse(value ?? '0'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class FixedPriceForOptions extends StatefulWidget {
  const FixedPriceForOptions({super.key});

  @override
  State<FixedPriceForOptions> createState() => _FixedPriceForOptionsState();
}

class _FixedPriceForOptionsState extends State<FixedPriceForOptions> {
  bool _isFixedPrice = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        injector<CategoryCubit>().generateProductsOptionsPrices(!_isFixedPrice);
        setState(() {
          _isFixedPrice = !_isFixedPrice;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: _isFixedPrice ? AppColors.yellow : Colors.white,
          borderRadius: BorderRadius.circular(100),
          border: Border.all(color: AppColors.lightGrey),
        ),
        child: Text(
          context.l10n.onePriceForAllChoices,
          style: TextStyles.body16,
        ),
      ),
    );
    // CheckboxListTile(
    //   controlAffinity: ListTileControlAffinity.leading,
    //   visualDensity: VisualDensity.compact,
    //   activeColor: AppColors.yellow,
    //   contentPadding: EdgeInsets.zero,
    //   title: Text(context.l10n.onePriceForAllChoices),
    //   value: _isFixedPrice,
    //   onChanged: (value) => setState(() {
    //     _isFixedPrice = value!;
    //     injector<CategoryCubit>().generateProductsOptionsPrices(value);
    //   }),
    // );
  }
}
