import 'dart:developer';

import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';

class ChoiceSection extends StatefulWidget {
  const ChoiceSection({super.key, required this.choice, required this.index});

  final GroupModel choice;
  final int index;

  @override
  State<ChoiceSection> createState() => _ChoiceSectionState();
}

class _ChoiceSectionState extends State<ChoiceSection> {
  @override
  void initState() {
    if (injector<CategoryCubit>().oprations.selectedOptionsPerGroup.length <
        widget.index + 1) {
      injector<CategoryCubit>().oprations.selectedOptionsPerGroup.add([]);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16,
      children: [
        Text(
          widget.choice.name ?? '',
          style: TextStyles.body16.copyWith(
            color: AppColors.black,
          ),
        ),
        const Divider(
          height: 0,
          color: AppColors.moreGrey,
        ),
        GridView.builder(
          padding: EdgeInsets.zero,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 4,
          ),
          itemBuilder: (_, index) => OptionItem(
            option: widget.choice.options?[index] ?? const OptionModel(),
            index: widget.index,
          ),
          itemCount: widget.choice.options?.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
        ),
      ],
    );
  }
}

class OptionItem extends StatefulWidget {
  const OptionItem({super.key, required this.option, required this.index});

  final OptionModel option;
  final int index;

  @override
  State<OptionItem> createState() => _OptionItemState();
}

class _OptionItemState extends State<OptionItem> {
  final inject = injector<CategoryCubit>();
  @override
  Widget build(BuildContext context) {
    bool isSelected = inject.oprations.selectedOptionsPerGroup[widget.index]
        .contains(widget.option.id);
    return CheckboxListTile(
      contentPadding: EdgeInsets.zero,
      controlAffinity: ListTileControlAffinity.leading,
      visualDensity: VisualDensity.compact,
      side: const BorderSide(color: AppColors.lightGrey),
      activeColor: AppColors.yellow,
      title: Text(widget.option.name ?? ''),
      value: isSelected,
      onChanged: (value) => setState(() {
        isSelected = value!;
        value
            ? inject.oprations.selectedOptionsPerGroup[widget.index]
                .add(widget.option.id!)
            : inject.oprations.selectedOptionsPerGroup[widget.index]
                .remove(widget.option.id!);
        log(inject.oprations.selectedOptionsPerGroup.toString());
      }),
    );
  }
}
