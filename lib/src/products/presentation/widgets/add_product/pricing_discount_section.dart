import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';

class PricingDiscountSection extends StatefulWidget {
  const PricingDiscountSection({super.key});

  @override
  State<PricingDiscountSection> createState() => _PricingDiscountSectionState();
}

class _PricingDiscountSectionState extends State<PricingDiscountSection> {
  final myController = TextEditingController();
  @override
  void dispose() {
    myController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        spacing: 16,
        children: [
          LabeledField(
            isRequired: true,
            label: context.l10n.virtualPrice,
            field: CustomTextField(
              hintText: '0.0',
              keyboardType: TextInputType.number,
              suffix: Text(context.l10n.egp),
              onChanged: (value) => cubit.oprations.productDataWrapper
                  .defaultPrice = double.tryParse(value ?? '0'),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return context.l10n.pleaseEnterTheProductName;
                }
                return null;
              },
            ),
          ),
          LabeledField(
            label: context.l10n.discountType,
            field: CustomDropDownButton(
              hintText: context.l10n.discountType,
              items: ['fixed', 'percentage']
                  .map(
                    (e) => DropdownMenuItem(
                      value: e,
                      onTap: () {
                        if (e == 'fixed') {
                          cubit.oprations.productDataWrapper.discountType = 0;
                        } else {
                          cubit.oprations.productDataWrapper.discountType = 1;
                        }
                      },
                      child: Text(context.l10n.discountTypeOptions(e)),
                    ),
                  )
                  .toList(),
            ),
          ),
          LabeledField(
            label: context.l10n.discountPercentage,
            field: CustomTextField(
                hintText: '0.0',
                keyboardType: TextInputType.number,
                onChanged: (value) => cubit.oprations.productDataWrapper
                    .discount = double.tryParse(value ?? '0')),
          ),
          LabeledField(
            label: context.l10n.discountExpiryDate,
            field: CustomTextField(
              controller: myController,
              hintText: context.l10n.discountExpiryDate,
              readOnly: true,
              onTap: () async {
                DateTime? selectedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (selectedDate != null) {
                  cubit.oprations.productDataWrapper.discountExpiration =
                      selectedDate;
                  myController.text = UiHelper.dateFormatting(selectedDate,
                      format: 'dd/MM/yyyy');
                }
              },
              suffixIcon: const Icon(
                Icons.calendar_today,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
