import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddAdditionDialog extends StatelessWidget {
  const AddAdditionDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    final formKey = GlobalKey<FormState>();
    return AlertDialog(
      insetPadding: const EdgeInsets.all(32),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
      content: SizedBox(
        width: 1.sw,
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            spacing: 16,
            children: [
              LabeledField(
                label: context.l10n.addition,
                field: BlocBuilder<CategoryCubit, CategoryState>(
                  buildWhen: (previous, current) => current.maybeWhen(
                    additionListSuccess: () => true,
                    orElse: () => false,
                  ),
                  builder: (context, state) {
                    return CustomDropDownButton(
                      hintText: context.l10n.addition,
                      items: injector<CategoryCubit>()
                          .oprations
                          .additionsList
                          .map((e) => DropdownMenuItem(
                              value: e, child: Text(e.name ?? '')))
                          .toList(),
                      onChanged: (value) {
                        cubit.oprations.productAdditionDataWrapper.additionId =
                            value?.id;
                      },
                      onValidate: (value) {
                        if (value == null) {
                          return context.l10n.pleaseEnterTheProductName;
                        }
                        return null;
                      },
                    );
                  },
                ),
              ),
              LabeledField(
                label: context.l10n.choice,
                field: BlocBuilder<CategoryCubit, CategoryState>(
                  buildWhen: (previous, current) => current.maybeWhen(
                    additionListSuccess: () => true,
                    orElse: () => false,
                  ),
                  builder: (context, state) {
                    return CustomDropDownButton(
                      hintText: context.l10n.choice,
                      items: injector<CategoryCubit>()
                          .oprations
                          .product!
                          .options!
                          .map((option) {
                        final labelParts =
                            option.optionDetails!.expand<String>((group) {
                          final groupOptions = group.options as List;
                          return groupOptions
                              .map<String>((opt) => opt.name.toString().trim());
                        }).toList();

                        log(labelParts.toString());
                        final label = labelParts.isEmpty
                            ? context.l10n.defaultOption
                            : labelParts.join(' - ');

                        return DropdownMenuItem<int>(
                          value: option.id,
                          child: Text(label),
                        );
                      }).toList(),
                      onChanged: (value) {
                        cubit.oprations.productAdditionDataWrapper
                            .productOptionId = value;
                      },
                      onValidate: (value) {
                        if (value == null) {
                          return context.l10n.pleaseEnterTheProductName;
                        }
                        return null;
                      },
                    );
                  },
                ),
              ),
              LabeledField(
                label: context.l10n.price,
                field: CustomTextField(
                  hintText: '0.0',
                  suffix: Text(context.l10n.egp),
                  onChanged: (value) => cubit
                      .oprations
                      .productAdditionDataWrapper
                      .price = double.tryParse(value ?? '0'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.l10n.pleaseEnterTheProductName;
                    }
                    return null;
                  },
                ),
              ),
              CustomElevatedButton(
                onPressed: () {
                  if (!formKey.currentState!.validate()) return;
                  formKey.currentState!.save();
                  context.router.maybePop();
                  cubit.oprations.productAdditionDataWrapper.id = 0;
                  cubit.oprations.productAdditionDataWrapper.isActive = true;
                  injector<CategoryCubit>().createProductAddition();
                },
                child: Text(context.l10n.add),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
