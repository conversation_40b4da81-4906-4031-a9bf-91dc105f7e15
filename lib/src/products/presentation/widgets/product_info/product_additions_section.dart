import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/available_switch.dart';
import 'package:flutter/material.dart';

class ProductAdditionsSection extends StatelessWidget {
  const ProductAdditionsSection({super.key, required this.productOption});

  final ProductOption productOption;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          // Text(
          //   context.l10n.additions,
          //   style: TextStyles.title20.copyWith(
          //     fontWeight: FontWeight.w600,
          //     color: AppColors.yellow,
          //   ),
          // ),
          ListView.separated(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (_, index) => ProductAdditionItem(
              option: productOption,
              addition: productOption.additions![index],
            ),
            separatorBuilder: (_, __) => const SizedBox(height: 8),
            itemCount: productOption.additions!.length,
          ),
        ],
      ),
    );
  }
}

class ProductAdditionItem extends StatelessWidget {
  const ProductAdditionItem(
      {super.key, required this.option, required this.addition});

  final ProductOption option;
  final ProductAdditionModel addition;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 16,
      children: [
        const CustomNetworkImage(
          '',
          width: 25,
          height: 25,
        ),
        Expanded(
          child: Row(
            children: [
              Text(
                '${addition.name ?? '-'} - ',
                style: TextStyles.body16,
              ),
              Text(
                '${addition.price ?? 0} ${context.l10n.egp}',
                style: TextStyles.body16.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.green,
                ),
              ),
            ],
          ),
        ),
        AvailableSwitch(
          isActive: addition.isActive ?? false,
          onTap: (value) {
            editProductAddition(option, addition, value);
          },
        ),
      ],
    );
  }
}

void editProductAddition(ProductOption productOption,
    ProductAdditionModel productAddition, bool value) {
  final cubit = injector<CategoryCubit>().oprations;
  cubit.productAdditionDataWrapper.id = productAddition.id;
  cubit.productAdditionDataWrapper.additionId = productAddition.additionId;
  cubit.productAdditionDataWrapper.productOptionId = productOption.id;
  cubit.productAdditionDataWrapper.price = productAddition.price;
  cubit.productAdditionDataWrapper.isActive = value;
  injector<CategoryCubit>().editProductAddition();
}
