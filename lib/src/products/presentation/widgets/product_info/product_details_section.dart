import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/edit_option_dialog.dart';
import 'package:alsarea_store/src/products/presentation/widgets/available_switch.dart';
import 'package:alsarea_store/src/products/presentation/widgets/product_info/product_additions_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductDetailsSection extends StatelessWidget {
  const ProductDetailsSection(
      {super.key, required this.product, required this.numOfOptions});

  final ProductModel product;
  final int numOfOptions;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 8,
      children: [
        Text(
          numOfOptions == 1 &&
                  product.options?[0].optionDetails?.isEmpty == true
              ? context.l10n.additions
              : context.l10n.orderDetails,
          style: TextStyles.title20.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.yellow,
          ),
        ),
        ListView.separated(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (_, index) => ProductDetailsItem(
              productOption: product.options![index],
              numOfOptions: numOfOptions),
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemCount: product.options!.length,
        ),
      ],
    );
  }
}

class ProductDetailsItem extends StatefulWidget implements AutoRouteWrapper {
  const ProductDetailsItem(
      {super.key, required this.productOption, required this.numOfOptions});

  final ProductOption productOption;
  final int numOfOptions;

  @override
  State<ProductDetailsItem> createState() => _ProductDetailsItemState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _ProductDetailsItemState extends State<ProductDetailsItem> {
  final cubit = injector<CategoryCubit>().oprations;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      child: widget.numOfOptions != 1 ||
              (widget.numOfOptions == 1 &&
                  widget.productOption.optionDetails?.isEmpty == false)
          ? Column(
              children: [
                Row(
                  spacing: 16,
                  children: [
                    CustomNetworkImage(
                      widget.productOption.image ?? '',
                      width: 40,
                      height: 40,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.productOption.optionDetails!
                                .map((group) => group.options![0].name)
                                .toList()
                                .join(" - "),
                            style: TextStyles.body16,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Row(
                            children: [
                              Text(
                                "${widget.productOption.priceAfterDiscount} ${context.l10n.egp} ",
                                style: TextStyles.body16.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.green,
                                ),
                              ),
                              if (widget.productOption.discount != 0)
                                Text(
                                  '${widget.productOption.price ?? 0}',
                                  style: TextStyles.body16.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.grey,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: .04.sw,
                      child: IconButton(
                        icon: Assets.icons.edit.svg(),
                        padding: EdgeInsets.zero,
                        onPressed: () => UiHelper.showCustomDialog(
                          context: context,
                          dialog: BlocProvider.value(
                            value: injector<CategoryCubit>(),
                            child: EditOptionDialog(
                              option: widget.productOption,
                            ),
                          ),
                        ),
                      ),
                    ),
                    AvailableSwitch(
                      isActive: widget.productOption.isActive ?? false,
                      onTap: (value) {
                        editProductOption(widget.productOption, value);
                      },
                    ),
                  ],
                ),
                if (widget.productOption.additions?.isNotEmpty == true)
                  ProductAdditionsSection(productOption: widget.productOption),
              ],
            )
          : ProductAdditionsSection(productOption: widget.productOption),
    );
  }
}

void editProductOption(ProductOption productOption, bool value) {
  final cubit = injector<CategoryCubit>().oprations;
  cubit.productOptionDataWrapper.productOptionId = productOption.id;
  cubit.productOptionDataWrapper.hasOptions = true;
  cubit.productOptionDataWrapper.price = productOption.price;
  cubit.productOptionDataWrapper.discountExpiration =
      productOption.discountExpiration;
  cubit.productOptionDataWrapper.discount = productOption.discount;
  cubit.productOptionDataWrapper.isActive = value;
  injector<CategoryCubit>().editProduct();
}
