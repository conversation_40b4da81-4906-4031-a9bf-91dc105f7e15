import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:flutter/material.dart';

class ProductDescriptionSection extends StatelessWidget {
  const ProductDescriptionSection({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          Text(
            context.l10n.productDescription,
            style: TextStyles.title16.copyWith(
              fontWeight: FontWeight.w700,
              color: AppColors.yellow,
            ),
          ),
          Text(
            product.description ?? '-',
            style: TextStyles.title20.copyWith(
              fontWeight: FontWeight.w700,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
