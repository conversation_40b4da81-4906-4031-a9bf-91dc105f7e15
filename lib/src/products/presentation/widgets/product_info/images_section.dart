import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class ImagesSection extends StatefulWidget {
  const ImagesSection({super.key, required this.items});

  final List<String> items;

  @override
  State<ImagesSection> createState() => _HomeSliderState();
}

class _HomeSliderState extends State<ImagesSection> {
  int currentIndex = 0;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: CarouselSlider(
            items: widget.items
                .map(
                  (item) => CustomNetworkImage(item),
                )
                .toList(),
            options: CarouselOptions(
              height: 200.h,
              enlargeCenterPage: false,
              aspectRatio: 2 / 1,
              viewportFraction: 1.0,
              autoPlay: true,
              onPageChanged: (index, reason) {
                setState(() {
                  currentIndex = index;
                });
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        // AnimatedSmoothIndicator(
        //   activeIndex: currentIndex,
        //   count: widget.items.length,
        //   effect: const ExpandingDotsEffect(
        //     activeDotColor: AppColors.primary,
        //     dotHeight: 8,
        //     dotWidth: 8,
        //   ),
        // ),
      ],
    );
  }
}
