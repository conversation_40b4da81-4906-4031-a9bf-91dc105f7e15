import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_product/edit_option_dialog.dart';
import 'package:alsarea_store/src/products/presentation/widgets/available_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProductInfoSection extends StatelessWidget {
  const ProductInfoSection({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          product.name ?? '-',
          style: TextStyles.headline32.copyWith(
            fontWeight: FontWeight.w400,
          ),
        ),
        // Text(
        //   'She In',
        //   style: TextStyles.title16.copyWith(
        //     fontWeight: FontWeight.w700,
        //     color: AppColors.yellow,
        //   ),
        // ),
        Row(
          children: [
            Expanded(
              child: product.hasOptions == false
                  ? RichText(
                      text: TextSpan(
                        style: const TextStyle(
                          fontFamily: FontFamily.somar,
                          color: AppColors.green,
                        ),
                        children: [
                          TextSpan(
                            text:
                                '${product.options?.first.priceAfterDiscount} ${context.l10n.egp} ',
                            style: TextStyles.headline24.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          if (product.options?.first.discount != 0)
                            TextSpan(
                              text: '${product.options?.first.price ?? 0}',
                              style: TextStyles.headline24.copyWith(
                                fontWeight: FontWeight.w400,
                                color: AppColors.grey,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                        ],
                      ),
                    )
                  : RichText(
                      text: TextSpan(
                        text: context.l10n.productHaveMultipleOptions,
                        style: TextStyles.headline24.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.green,
                        ),
                      ),
                    ),
            ),
            product.hasOptions == false
                ? IconButton(
                    icon: Assets.icons.edit.svg(),
                    padding: EdgeInsets.zero,
                    onPressed: () => UiHelper.showCustomDialog(
                      context: context,
                      dialog: BlocProvider.value(
                        value: injector<CategoryCubit>(),
                        child: EditOptionDialog(),
                      ),
                    ),
                  )
                : const SizedBox(),
            AvailableSwitch(
                isActive: product.isActive ?? false,
                onTap: (value) {
                  editProductWithoutOption(product, value);
                }),
          ],
        ),
        // product.options?.length == 1
        //     ? Container(
        //         padding: const EdgeInsets.all(8),
        //         color: AppColors.lighterGrey,
        //         child: Row(
        //           spacing: 8,
        //           children: [
        //             Expanded(
        //               child: Text(context.l10n.quantityOnStock),
        //             ),
        //             Text(context.l10n
        //                 .peaceCount(product.options?.first.quantity ?? 0)),
        //           ],
        //         ),
        //       )
        //     : const SizedBox(),
      ],
    );
  }
}

void editProductWithoutOption(ProductModel product, bool value) {
  final cubit = injector<CategoryCubit>().oprations;
  cubit.productOptionDataWrapper.productOptionId = product.options?.first.id;
  cubit.productOptionDataWrapper.hasOptions = false;
  cubit.productOptionDataWrapper.nameAr = product.name;
  cubit.productOptionDataWrapper.nameEn = product.name;
  cubit.productOptionDataWrapper.descriptionAr = product.description;
  cubit.productOptionDataWrapper.descriptionEn = product.description;
  cubit.productOptionDataWrapper.price = product.options?.first.price;
  cubit.productOptionDataWrapper.discountExpiration =
      product.options?.first.discountExpiration;
  cubit.productOptionDataWrapper.discount = product.options?.first.discount;
  cubit.productOptionDataWrapper.isActive = value;
  cubit.productOptionDataWrapper.isAvailable = product.isAvailable;
  injector<CategoryCubit>().editProduct();
}
