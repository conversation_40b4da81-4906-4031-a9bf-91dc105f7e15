import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

class ProductsEmptyContent extends StatelessWidget {
  const ProductsEmptyContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              injector<CategoryCubit>().oprations.product = null;
              context.router.push(AddProductRoute(subcategoryId: 0));
            },
            icon: const Icon(
              Icons.add_circle_rounded,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.l10n.youDidNotAddAnyProductsYet,
            style: TextStyles.title20.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
