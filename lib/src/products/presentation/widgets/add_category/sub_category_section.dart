import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/app_helper.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/add_card_widget.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/add_category/add_sub_category_dialog.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SubCategorySection extends StatefulWidget implements AutoRouteWrapper {
  const SubCategorySection({super.key, this.category});

  final CategoryModel? category;

  @override
  State<SubCategorySection> createState() => _SubCategorySectionState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _SubCategorySectionState extends State<SubCategorySection> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CategoryCubit, CategoryState>(
      buildWhen: (previous, current) => current.maybeWhen(
        categorySuccess: (_) => true,
        categoryFailure: (_) => true,
        categoryLoading: () => true,
        orElse: () => false,
      ),
      builder: (context, state) {
        return state.maybeWhen(
          categoryLoading: () => const SliverToBoxAdapter(
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
          categoryFailure: (message) => SliverToBoxAdapter(
            child: Center(
              child: Text(
                message,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ),
          categorySuccess: (category) => SliverMainAxisGroup(
            slivers: [
              // Title
              SliverPadding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                sliver: SliverToBoxAdapter(
                  child: Text(
                    context.l10n.subCategories,
                    style: TextStyles.title16.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              // Add button
              SliverFloatingHeader(
                child: Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: AddCardWidget(
                    title: context.l10n.addSubCategory,
                    onTap: () => UiHelper.showCustomDialog(
                      context: context,
                      dialog: AddSubCategoryDialog(
                        category: category,
                        subcategory: null,
                      ),
                    ),
                  ),
                ),
              ),
              // Subcategories list
              SliverPadding(
                padding: const EdgeInsets.symmetric(vertical: 16)
                    .copyWith(bottom: 40),
                sliver: SliverList.separated(
                  itemBuilder: (context, index) => SubCategoryCard(
                    subCategory: category.subCategories![index],
                    onDelete: () {
                      injector<CategoryCubit>().deleteCategory(
                          category.subCategories![index].subCategoryId ?? 0);
                      setState(() {
                        injector<CategoryCubit>().getCategory(category.id);
                      });
                      Navigator.pop(context);
                    },
                  ),
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 8),
                  itemCount: category.subCategories!.length,
                ),
              ),
              const SliverPadding(
                padding: EdgeInsets.symmetric(vertical: 10),
              ),
            ],
          ),
          orElse: () => const SliverToBoxAdapter(child: SizedBox.shrink()),
        );
      },
    );
  }
}

class SubCategoryCard extends StatefulWidget {
  const SubCategoryCard({super.key, required this.subCategory, this.onDelete});

  final SubcategoryModel subCategory;
  final VoidCallback? onDelete;

  @override
  State<SubCategoryCard> createState() => _SubCategoryCardState();
}

class _SubCategoryCardState extends State<SubCategoryCard> {
  @override
  Widget build(BuildContext context) {
    String locale = AppHelper.getLocale();
    return Card(
      child: ListTile(
        onTap: () => context.router.push(ProductsRoute(
            category: injector<CategoryCubit>().oprations.category,
            subcategoryId: widget.subCategory.subCategoryId)),
        contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        visualDensity: VisualDensity.compact,
        title: RichText(
          text: TextSpan(
            style: const TextStyle(fontFamily: FontFamily.somar),
            children: [
              TextSpan(
                text: locale == 'ar'
                    ? widget.subCategory.subCategoryNameAr
                    : widget.subCategory.subCategoryNameEn,
                style: TextStyles.title16.copyWith(
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
              TextSpan(
                text:
                    '   ( ${widget.subCategory.numberOfProducts ?? ''} ${context.l10n.productsIndefinite})',
                style: TextStyles.title14.copyWith(
                  fontWeight: FontWeight.w400,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        trailing: IntrinsicWidth(
          child: Row(
            children: [
              IconButton(
                icon: Assets.icons.edit.svg(),
                padding: EdgeInsets.zero,
                onPressed: () => UiHelper.showCustomDialog(
                  context: context,
                  dialog: AddSubCategoryDialog(subcategory: widget.subCategory),
                ),
              ),
              IconButton(
                icon: Assets.icons.delete.svg(),
                onPressed: () {
                  UiHelper.showCustomDialogV1(
                      context: context,
                      onYesPressed: widget.onDelete,
                      title: context.l10n.areYouSure);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
