import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddSubCategoryDialog extends StatefulWidget implements AutoRouteWrapper {
  const AddSubCategoryDialog({super.key, this.subcategory, this.category});

  final SubcategoryModel? subcategory;
  final CategoryModel? category;

  @override
  State<AddSubCategoryDialog> createState() => _AddSubCategoryDialogState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AddSubCategoryDialogState extends State<AddSubCategoryDialog> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();

    return AlertDialog(
      insetPadding: const EdgeInsets.all(32),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
      content: SizedBox(
        width: 1.sw,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LabeledField(
                label: context.l10n.addSubCategoryAr,
                field: CustomTextField(
                    fillColor: AppColors.lighterGrey,
                    hintText: widget.subcategory != null
                        ? widget.subcategory?.subCategoryNameAr
                        : context.l10n.addSubCategoryAr,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                    onSaved: (value) => {
                          cubit.oprations.subDataWrapper.nameArSubcategory =
                              value,
                          cubit.oprations.subDataWrapper.nameEnSubcategory =
                              value,
                        }),
              ),
              const SizedBox(height: 24),
              // LabeledField(
              //   label: context.l10n.addSubCategoryEn,
              //   field: CustomTextField(
              //       fillColor: AppColors.lighterGrey,
              //       hintText: widget.subcategory != null
              //           ? widget.subcategory?.subCategoryNameEn
              //           : context.l10n.addSubCategoryEn,
              //       validator: (value) {
              //         if (value == null || value.isEmpty) {
              //           return context.l10n.pleaseEnterTheStoreName;
              //         }
              //         return null;
              //       },
              //       onSaved: (value) => {
              //             cubit.oprations.subDataWrapper.nameEnSubcategory =
              //                 value,
              //           }),
              // ),
              // const SizedBox(height: 24),
              CustomElevatedButton(
                onPressed: _onSave,
                child: Text(
                  context.l10n.add,
                  style: TextStyles.title14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onSave() {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();
    if (widget.subcategory == null) {
      injector<CategoryCubit>().addSubcategory();
      Navigator.pop(context);
    } else {
      injector<CategoryCubit>().oprations.subDataWrapper.subId =
          widget.subcategory!.subCategoryId;
      injector<CategoryCubit>()
          .editSubcategory(widget.subcategory!.subCategoryId!);
      Navigator.pop(context);
    }
  }
}
