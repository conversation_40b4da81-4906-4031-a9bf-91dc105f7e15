import 'dart:convert';
import 'dart:io';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/core/widgets/shared/upload_image.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategorySection extends StatefulWidget {
  const CategorySection({super.key, this.category});

  final CategoryModel? category;

  @override
  State<CategorySection> createState() => _CategorySectionState();
}

class _CategorySectionState extends State<CategorySection> {
  late File imageFile;
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CategoryCubit>();
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabeledField(
            label: context.l10n.categoryNameAr,
            field: CustomTextField(
              initialValue: widget.category?.nameAr,
              hintText: context.l10n.categoryNameAr,
              fillColor: AppColors.lighterGrey,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return context.l10n.pleaseEnterTheStoreName;
                }
                return null;
              },
              onSaved: (value) => {
                cubit.oprations.dataWrapper.nameAr = value,
                cubit.oprations.dataWrapper.nameEn = value,
              },
            ),
          ),
          const SizedBox(height: 16),
          // LabeledField(
          //   label: context.l10n.categoryNameEn,
          //   field: CustomTextField(
          //     initialValue: widget.category?.nameEn,
          //     hintText: context.l10n.categoryNameEn,
          //     fillColor: AppColors.lighterGrey,
          //     validator: (value) {
          //       if (value == null || value.isEmpty) {
          //         return context.l10n.pleaseEnterTheStoreName;
          //       }
          //       return null;
          //     },
          //     onSaved: (value) => cubit.oprations.dataWrapper.nameEn = value,
          //   ),
          // ),
          const SizedBox(height: 16),
          Text(
            context.l10n.categoryImage,
            style: TextStyles.body14,
          ),
          const SizedBox(height: 8),
          widget.category?.image ==
                      'http://3lsare3.flyfox-eg.com/images/Allimages/' ||
                  widget.category?.image == null
              ? UploadImageWidget(
                  label: context.l10n.categoryImage,
                  onUploadImage: (file) async {
                    if (file == null) {
                      cubit.oprations.dataWrapper.image = null;
                    } else {
                      List<int> imageBytes = await file.readAsBytes();
                      cubit.oprations.dataWrapper.image =
                          base64Encode(imageBytes);
                    }
                  },
                )
              : Center(
                  child: CustomNetworkImage(
                    widget.category?.image ?? '',
                    width: .5.sw,
                    fit: BoxFit.fitWidth,
                  ),
                ),
        ],
      ),
    );
  }
}
