import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OrdersSection extends StatelessWidget {
  const OrdersSection({
    super.key,
    required this.label,
    required this.icon,
    required this.onTap,
    required this.count,
  });

  final String label;
  final Widget icon;
  final Function onTap;
  final bool count;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => {onTap()},
      child: Container(
        width: .25.sw,
        padding: const EdgeInsets.all(4).copyWith(bottom: 8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: .06),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Container(
              width: 1.sw,
              padding: const EdgeInsets.symmetric(
                vertical: 18,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: icon,
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: TextStyles.title14.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(
                  width: 2,
                ),
                if (count &&
                    injector<CategoryCubit>().oprations.ordersCount != 0)
                  CircleAvatar(
                    radius: .035.sw,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      injector<CategoryCubit>()
                          .oprations
                          .ordersCount
                          .toString(),
                      style: TextStyles.title14.copyWith(
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
              ],
            )
          ],
        ),
      ),
    );
  }
}
