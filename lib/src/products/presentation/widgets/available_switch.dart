import 'package:flutter/material.dart';

class AvailableSwitch extends StatefulWidget {
  const AvailableSwitch(
      {super.key, required this.isActive, required this.onTap});

  final bool isActive;
  final Function onTap;

  @override
  State<AvailableSwitch> createState() => _AvailableSwitchState();
}

class _AvailableSwitchState extends State<AvailableSwitch> {
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _isAvailable = widget.isActive;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 8,
      children: [
        // Text(
        //   context.l10n.available,
        //   style: TextStyles.body12.copyWith(
        //     fontWeight: FontWeight.w700,
        //     color: AppColors.primary,
        //   ),
        // ),
        Transform.scale(
          scale: 0.75,
          child: FittedBox(
            fit: BoxFit.fill,
            child: Switch(
              value: _isAvailable,
              onChanged: (value) {
                setState(() {
                  _isAvailable = value;
                });
                widget.onTap(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}
