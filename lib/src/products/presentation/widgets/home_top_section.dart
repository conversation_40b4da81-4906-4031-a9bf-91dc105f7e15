import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/profile_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/widgets/available_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeTopSection extends StatelessWidget {
  const HomeTopSection({
    super.key,
    required this.profile,
  });

  final ProfileModel profile;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: const Color(0xffFDF2F5),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(40),
              bottomRight: Radius.circular(40),
            ),
            image: DecorationImage(
              image: NetworkImage(
                profile.cover ?? '',
              ),
              fit: BoxFit.cover,
            ),
          ),
          child: Container(
            padding: const EdgeInsets.only(top: 80, bottom: 28),
            decoration: BoxDecoration(
              color: const Color(0xffFDF2F5),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(40),
              ),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  AppColors.red.withValues(alpha: .94),
                  Colors.white.withValues(alpha: 0),
                ],
              ),
            ),
            child: Column(
              children: [
                Row(
                  spacing: 16,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    SizedBox(
                      width: 0.3.sw,
                      height: .14.sh,
                      child: CustomNetworkImage(
                        profile.image ?? '',
                        fit: BoxFit.cover,
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: TextSpan(
                            text: context.l10n.welcome,
                            style: TextStyles.title22.copyWith(
                              color: Colors.white,
                              fontFamily: FontFamily.somar,
                            ),
                            children: [
                              TextSpan(
                                text:
                                    ' - ${context.currentLocaleCode == 'ar' ? profile.nameAr : profile.nameEn}',
                                style: TextStyles.title22.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontFamily: FontFamily.somar,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          profile.phoneNumber ?? '',
                          style: TextStyles.title22.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            fontFamily: FontFamily.somar,
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Assets.icons.star.svg(width: .04.sw),
                            const SizedBox(width: 5),
                            Text(
                              profile.storeRate ?? '',
                              style: TextStyles.title22.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontFamily: FontFamily.somar,
                              ),
                            ),
                            AvailableSwitch(
                                isActive: profile.isActive ?? false,
                                onTap: () {
                                  final cubit =
                                      injector<CategoryCubit>().oprations;
                                  cubit.profileDataWrapper.isActive =
                                      cubit.profileDataWrapper.isActive == true
                                          ? false
                                          : true;
                                  injector<CategoryCubit>().editProfile();
                                })
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
        // Positioned(
        //   top: 70,
        //   right: 16,
        //   child: IconButton(
        //     icon: const Icon(Icons.menu),
        //     onPressed: () => Scaffold.of(context).openDrawer(),
        //   ),
        // ),
      ],
    );
  }
}


