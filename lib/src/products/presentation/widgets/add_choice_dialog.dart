import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/labeled_field.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddChoiceDialog extends StatefulWidget implements AutoRouteWrapper {
  const AddChoiceDialog({super.key, required this.group, this.option});

  final GroupModel group;
  final OptionModel? option;

  @override
  State<AddChoiceDialog> createState() => _AddChoiceDialogState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<CategoryCubit>(),
        child: this,
      );
}

class _AddChoiceDialogState extends State<AddChoiceDialog> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final cubit = injector<CategoryCubit>();
    cubit.oprations.optionsDataWrapper.groupId = widget.group.id;
    cubit.oprations.optionsDataWrapper.id = widget.option?.id;
    return AlertDialog(
      insetPadding: const EdgeInsets.all(32),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
      content: SizedBox(
        width: 1.sw,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LabeledField(
                label: context.l10n.choices,
                field: CustomTextField(
                    fillColor: AppColors.lighterGrey,
                    hintText: widget.option != null
                        ? widget.option?.name
                        : context.l10n.choices,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseEnterTheStoreName;
                      }
                      return null;
                    },
                    onSaved: (value) => {
                          cubit.oprations.optionsDataWrapper.nameAr = value,
                          cubit.oprations.optionsDataWrapper.nameEn = value,
                        }),
              ),
              const SizedBox(height: 24),
              CustomElevatedButton(
                onPressed: _onSave,
                child: Text(context.l10n.add),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onSave() {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();

    if (widget.option == null) {
      injector<CategoryCubit>().createOption();
      Navigator.pop(context);
    } else {
      injector<CategoryCubit>().editOption();
      Navigator.pop(context);
    }
  }
}
