import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_network_image.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/gen/fonts.gen.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

class ProductCard extends StatelessWidget {
  const ProductCard({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            context.router.push(ProductInfoRoute(product: product));
          },
          child: Row(
            spacing: 16,
            children: [
              CustomNetworkImage(
                product.imageUrl ?? '-',
                width: 115,
                height: 115,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name ?? '-',
                      style: TextStyles.title20.copyWith(
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    product.hasOptions == false
                        ? RichText(
                            text: TextSpan(
                              style: const TextStyle(
                                fontFamily: FontFamily.somar,
                                color: AppColors.green,
                              ),
                              children: [
                                TextSpan(
                                  text:
                                      '${product.priceAfterDiscount ?? '0'} ${context.l10n.egp} ',
                                  style: TextStyles.title20.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                product.discount != 0
                                    ? TextSpan(
                                        text: '${product.price ?? '0'}',
                                        style: TextStyles.title16.copyWith(
                                          fontWeight: FontWeight.w400,
                                          color: AppColors.grey,
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ))
                                    : const TextSpan(),
                              ],
                            ),
                          )
                        : RichText(
                            text: TextSpan(
                              text: context.l10n.productHaveMultipleOptions,
                              style: TextStyles.title20.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppColors.green,
                              ),
                            ),
                          ),
                  ],
                ),
              ),
            ],
          ),
        ),
        PositionedDirectional(
          top: 0,
          end: 0,
          child: IconButton(
            onPressed: () {
              context.router.push(ProductInfoRoute(product: product));
            },
            icon: Assets.icons.edit.svg(width: 20, height: 20),
            iconSize: 25,
          ),
        ),
      ],
    );
  }
}
