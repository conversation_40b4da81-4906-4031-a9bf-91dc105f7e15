import 'dart:developer';

import 'package:alsarea_store/src/products/data/models/body/add_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/categories_body.dart';
import 'package:alsarea_store/src/products/data/models/body/create_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_group_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_profile_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_groups_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_quieries.dart';
import 'package:alsarea_store/src/products/data/models/category_operations.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_view_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/profile_modal.dart';
import 'package:alsarea_store/src/products/data/repo/category_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_cubit.freezed.dart';
part 'category_state.dart';

class CategoryCubit extends Cubit<CategoryState> {
  final CategoryRepo _repo;
  CategoryCubit(this._repo) : super(CategoryState.initial());

  final oprations = CategoryOperations();
  final List<GlobalKey<FormState>> productFormKey =
      List.generate(3, (_) => GlobalKey<FormState>());

  Future<void> getCategories({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(CategoryState.categoriesLoading());
      oprations.categories.clear();
      oprations.currentPage = 1;
    }
    if (oprations.currentPage > oprations.totalPages) return;
    oprations.skip = (oprations.currentPage - 1) * 10;
    oprations.dataWrapper.take = 10;
    oprations.dataWrapper.skip = oprations.skip;
    // emit(CategoryState.categoriesLoading());
    final body = CategoriesBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.getCategories(body);
    result.when(
        success: (result) => {
              oprations.categories.addAll(result.data ?? []),
              oprations.totalPages = result.pages?.totalPages ?? 1,
              oprations.currentPage++,
              emit(CategoryState.categoriesSuccess(oprations.categories)),
            },
        failure: (message) => emit(CategoryState.categoriesFailure(message)));
  }

  Future<void> getCategory(id) async {
    emit(CategoryState.categoryLoading());
    final result = await _repo.getCategory(id);
    result.when(
        success: (result) => {
              oprations.category = result.data!,
              oprations.subDataWrapper.id = result.data!.id,
              oprations.productsDataWrapper.categoryId = id,
              // oprations.productsDataWrapper.categoryId = id,
              oprations.subCategories = result.data!.subCategories!,
              emit(CategoryState.categorySuccess(
                result.data ?? const CategoryModel(),
              )),
            },
        failure: (message) => {emit(CategoryState.categoryFailure(message))});
  }

  Future<void> addCategory() async {
    emit(CategoryState.addcategoryLoading());
    final body = AddCategoryBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.addCategyry(body);
    result.when(
        success: (result) => {
              oprations.category = result.data ?? const CategoryModel(),
              oprations.categories.add(result.data ?? const CategoryModel()),
              emit(CategoryState.addcategorySuccess(
                category: result.data ?? const CategoryModel(),
              )),
              emit(CategoryState.categoriesSuccess(oprations.categories)),
            },
        failure: (message) => emit(CategoryState.addcategoryFailure(message)));
  }

  Future<void> editCategory() async {
    emit(CategoryState.addcategoryLoading());
    final body = EditCategoryBody.fromJson(oprations.dataWrapper.toJson());
    final result = await _repo.editCategory(body);
    result.when(
        success: (result) {
          oprations.category = result.data ?? const CategoryModel();
          final index = oprations.categories
              .indexWhere((element) => element.id == result.data?.id);
          oprations.categories[index] = result.data ?? const CategoryModel();
          emit(CategoryState.addcategorySuccess(
            category: result.data ?? const CategoryModel(),
          ));
          emit(CategoryState.categoriesSuccess(oprations.categories));
        },
        failure: (message) => emit(CategoryState.addcategoryFailure(message)));
  }

  Future<void> deleteCategory(int id) async {
    final result = await _repo.deleteCategory(id);
    result.when(
        success: (result) async {
          oprations.categories.removeWhere((item) => item.id == id);
        },
        failure: (message) => {});
  }

  Future<void> addSubcategory() async {
    emit(CategoryState.addsubcategoryLoading());
    final body = AddSubcategoryBody.fromJson(oprations.subDataWrapper.toJson());
    final result = await _repo.addSubcategyry(body);
    // final index = oprations.categories
    //     .indexWhere((element) => element.id == oprations.category.id);
    result.when(
        success: (result) async {
          oprations.subcategory = result.data ?? const SubcategoryModel();
          subcategoriesList();
          emit(CategoryState.addsubcategorySuccess(
            subcategory: result.data ?? const SubcategoryModel(),
          ));
          oprations.category = oprations.category.copyWith(
            subCategories: [
              ...?oprations.category.subCategories,
              result.data ?? const SubcategoryModel()
            ],
          );
          emit(CategoryState.categorySuccess(oprations.category));
        },
        failure: (message) => emit(CategoryState.addcategoryFailure(message)));
  }

  Future<void> editSubcategory(int id) async {
    emit(CategoryState.addsubcategoryLoading());
    final body =
        EditSubcategoryBody.fromJson(oprations.subDataWrapper.toJson());
    final result = await _repo.editSubcategory(body);
    final index = oprations.category.subCategories!
        .indexWhere((element) => element.subCategoryId == id);
    result.when(
        success: (result) => {
              oprations.category = oprations.category.copyWith(
                subCategories: List<SubcategoryModel>.from(
                    oprations.category.subCategories!)
                  ..[index] = result.data ?? const SubcategoryModel(),
              ),
              oprations.subcategory = result.data ?? const SubcategoryModel(),
              subcategoriesList(),
              emit(CategoryState.addsubcategorySuccess(
                subcategory: result.data ?? const SubcategoryModel(),
              )),
              emit(CategoryState.categorySuccess(oprations.category)),
            },
        failure: (message) => emit(CategoryState.addcategoryFailure(message)));
  }

  Future<void> deleteSubcategory(int id) async {
    final result = await _repo.deleteSubcategory(id);
    result.when(
        success: (result) async {
          oprations.category = oprations.category.copyWith(
            subCategories: oprations.category.subCategories!
                .where((element) => element.subCategoryId != id)
                .toList(),
          );
        },
        failure: (message) => {});
  }

  Future<dynamic> subcategoriesList() async {
    emit(CategoryState.subcategoriesListLoading());
    final result = await _repo.subcategoriesList();
    result.when(
        success: (result) => {
              oprations.subCategoriesList = result.data!,
              emit(CategoryState.subcategoriesListSuccess()),
            },
        failure: (message) =>
            {emit(CategoryState.subcategoriesListFailure(message))});
    return oprations.subCategoriesList;
  }

  Future<dynamic> additionList() async {
    emit(CategoryState.additionListLoading());
    final result = await _repo.additionList();
    result.when(
        success: (result) => {
              oprations.additionsList = result.data!,
              emit(CategoryState.additionListSuccess()),
            },
        failure: (message) =>
            {emit(CategoryState.additionListFailure(message))});
    return oprations.additionsList;
  }

  Future<void> createAddition() async {
    emit(CategoryState.editAdditionLoading());

    final body =
        EditAdditionBody.fromJson(oprations.additionDataWrapper.toJson());
    final result = await _repo.createAddition(body);
    result.when(
        success: (result) async {
          await additionList();
          emit(CategoryState.editAdditionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.editAdditionFailure(message))});
  }

  Future<void> editAddition(int id) async {
    emit(CategoryState.editAdditionLoading());

    final body =
        EditAdditionBody.fromJson(oprations.additionDataWrapper.toJson());
    final result = await _repo.editAddition(body, id);
    result.when(
        success: (result) async {
          await additionList();
          emit(CategoryState.editAdditionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.editAdditionFailure(message))});
  }

  Future<void> deleteAddition(int id) async {
    emit(CategoryState.editAdditionLoading());
    final result = await _repo.deleteAddition(id);
    result.when(
        success: (result) {
          oprations.additionsList.removeWhere((item) => item.id == id);
          emit(CategoryState.editAdditionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.editAdditionFailure(message))});
  }

  Future<dynamic> getGroups({bool isLoadMore = false}) async {
    // emit(const CategoryState.additionListLoading());
    final body = GetGroupsBody.fromJson(oprations.productsDataWrapper.toJson());
    final result = await _repo.getGroups(body);
    result.when(
        success: (result) => {
              oprations.groupsList = result.data!,
              // emit(CategoryState.additionListSuccess(
              //   additionList: result.data,
              // )),
            },
        failure: (message) => {
              // emit(CategoryState.additionListFailure(message))
            });
    return oprations.groupsList;
  }

  Future<void> createGroup() async {
    emit(CategoryState.editGroupLoading());

    final body = EditGroupBody.fromJson(oprations.groupDataWrapper.toJson());
    final result = await _repo.createGroup(body);
    result.when(
        success: (result) async {
          await getGroups();
          emit(CategoryState.editGroupSuccess());
        },
        failure: (message) => {emit(CategoryState.editGroupFailure(message))});
  }

  Future<void> editGroup(int id) async {
    emit(CategoryState.editGroupLoading());

    final body = EditGroupBody.fromJson(oprations.groupDataWrapper.toJson());
    final result = await _repo.editGroup(body, id);
    result.when(
        success: (result) async {
          await getGroups();
          emit(CategoryState.editGroupSuccess());
        },
        failure: (message) => {emit(CategoryState.editGroupFailure(message))});
  }

  Future<void> deleteGroup(int id) async {
    emit(CategoryState.editGroupLoading());
    final result = await _repo.deleteGroup(id);
    result.when(
        success: (result) {
          oprations.groupsList.removeWhere((item) => item.id == id);
          emit(CategoryState.editGroupSuccess());
        },
        failure: (message) => {emit(CategoryState.editGroupFailure(message))});
  }

  // Products

  Future<void> addProduct() async {
    emit(CategoryState.addProductLoading());
    if (oprations.productDataWrapper.hasOptions == false) {
      final defaultOption = buildDefaultProductOption();
      oprations.productDataWrapper.productOptions?.add(defaultOption);
    }
    final body = AddProductBody.fromJson(oprations.productDataWrapper.toJson());
    final result = await _repo.createProduct(body);
    result.when(
        success: (result) => {
              oprations.product = result.data ?? const ProductModel(),
              oprations.productAdditions = [],
              oprations.selectedOptionsPerGroup.clear(),
              oprations.generatedProductsOptionsIds.clear(),
              oprations.productOptionsView.clear(),
              oprations.productDataWrapper.clear(),
              emit(CategoryState.addProductsuccess()),
            },
        failure: (message) => emit(CategoryState.addProductFailure(message)));
  }

  Future<void> editProduct() async {
    // emit(CategoryState.productLoading());
    final body =
        EditProductBody.fromJson(oprations.productOptionDataWrapper.toJson());
    final result = await _repo.editProduct(body);
    result.when(
        success: (result) => {
              // oprations.product = result.data ?? const ProductModel(),
              // emit(CategoryState.productSuccess()),
              getProduct(oprations.product!.id!),
            },
        failure: (message) => {
              // emit(CategoryState.productFailure(message))
            });
  }

  Future<void> createProductOption() async {
    emit(CategoryState.addProductOptionLoading());
    final body = AddProductOptionBody.fromJson(
        oprations.productOptionDataWrapper.toJson());
    final result = await _repo.createProductOption(body);
    result.when(
        success: (result) => {
              oprations.product = result.data ?? const ProductModel(),
              emit(CategoryState.addProductOptionsuccess()),
              emit(CategoryState.productSuccess()),
            },
        failure: (message) =>
            {emit(CategoryState.addProductOptionFailure(message))});
  }

  Future<void> createProductAddition() async {
    emit(CategoryState.addProductAdditionLoading());
    final body = EditProductAdditionBody.fromJson(
        oprations.productAdditionDataWrapper.toJson());
    final result = await _repo.createProductAddition(body);
    result.when(
        success: (result) => {
              oprations.productAdditions.add(result.data!),
              emit(CategoryState.addProductAdditionsuccess()),
              getProduct(oprations.product!.id!),
            },
        failure: (message) =>
            {emit(CategoryState.addProductAdditionFailure(message))});
  }

  Future<void> editProductAddition() async {
    // emit(CategoryState.productLoading());
    final body = EditProductAdditionBody.fromJson(
        oprations.productAdditionDataWrapper.toJson());
    final result = await _repo.editProductAddition(body);
    result.when(
        success: (result) => {
              // oprations.product = result.data ?? const ProductModel(),
              // emit(CategoryState.productSuccess()),
            },
        failure: (message) => {
              // emit(CategoryState.productFailure(message))
            });
  }

  Future<void> createOption() async {
    oprations.optionsDataWrapper.id = null;
    emit(CategoryState.createOptionLoading());
    final body =
        CreateOptionBody.fromJson(oprations.optionsDataWrapper.toJson());
    final result = await _repo.createOption(body);
    result.when(
        success: (result) async {
          await getGroups();
          emit(CategoryState.createOptionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.createOptionFailure(message))});
  }

  Future<void> editOption() async {
    emit(CategoryState.createOptionLoading());
    final body =
        CreateOptionBody.fromJson(oprations.optionsDataWrapper.toJson());
    final result = await _repo.editOption(body);
    result.when(
        success: (result) async {
          await getGroups();
          emit(CategoryState.createOptionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.createOptionFailure(message))});
  }

  Future<void> deleteOption(int id) async {
    emit(CategoryState.createOptionLoading());
    final result = await _repo.deleteOption(id);
    result.when(
        success: (result) async {
          await getGroups();
          emit(CategoryState.createOptionSuccess());
        },
        failure: (message) =>
            {emit(CategoryState.createOptionFailure(message))});
  }

  Future<void> getProducts({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(CategoryState.productsLoading());
      oprations.products.clear();
      oprations.productsCurrentPage = 1;
    }
    if (oprations.productsCurrentPage > oprations.productsTotalPages) return;
    oprations.productsSkip = (oprations.productsCurrentPage - 1) * 10;
    oprations.productsDataWrapper.take = 10;
    oprations.productsDataWrapper.skip = oprations.productsSkip;
    // emit(CategoryState.productsLoading());
    final body =
        GetProductsBody.fromJson(oprations.productsDataWrapper.toJson());
    final queiries =
        GetProductsQuieries.fromJson(oprations.productsDataWrapper.toJson());
    final result = await _repo.getProducts(body, queiries);
    result.when(
        success: (result) => {
              oprations.products.addAll(result.data ?? []),
              oprations.productsTotalPages = result.pages?.totalPages ?? 1,
              oprations.productsCurrentPage++,
              emit(CategoryState.productsSuccess(oprations.products)),
            },
        failure: (message) => emit(CategoryState.productsFailure(message)));
  }

  Future<void> getProduct(int id) async {
    emit(CategoryState.productLoading());
    final result = await _repo.getProduct(id);
    result.when(
        success: (result) => {
              oprations.product = result.data ?? const ProductModel(),
              emit(CategoryState.productSuccess()),
            },
        failure: (message) => emit(CategoryState.productFailure(message)));
  }

  void generateProductsOptions() {
    final selectedOptionsPerGroup = oprations.selectedOptionsPerGroup;
    List<List<int>> cleanedSelectedOptionsPerGroup =
        selectedOptionsPerGroup.where((list) => list.isNotEmpty).toList();

    List<List<int>> results = [];

    void backtrack(int depth, List<int> current) {
      if (depth == cleanedSelectedOptionsPerGroup.length) {
        results.add(List.from(current));
        return;
      }

      for (int option in cleanedSelectedOptionsPerGroup[depth]) {
        current.add(option);
        backtrack(depth + 1, current);
        current.removeLast();
      }
    }

    if (cleanedSelectedOptionsPerGroup.isNotEmpty) {
      backtrack(0, []);
    }

    log(results.toString());

    oprations.generatedProductsOptionsIds = results;

    final views = buildProductOptionViews(oprations.groupsList, results);

    oprations.productOptionsView = views;

    emit(CategoryState.generatedProductOptions());
  }

  ProductOptionView buildDefaultProductOption() {
    return ProductOptionView(
      name: '',
      optionIds: [],
      quantity: 0,
      price: oprations.productDataWrapper.defaultPrice,
      discount: oprations.productDataWrapper.discount,
      type: oprations.productDataWrapper.discountType,
      priceAfterDiscount: 0,
      isActive: true,
      image: '',
      discountExpiration: oprations.productDataWrapper.discountExpiration,
    );
  }

  List<ProductOptionView> buildProductOptionViews(
      List<GroupModel> groups, List<List<int>> selectedOptionIdsList) {
    final Map<int, String> optionIdToName = {};

    for (var group in groups) {
      for (var option in group.options ?? []) {
        optionIdToName[option.id] = option.name;
      }
    }

    return selectedOptionIdsList.map((optionIds) {
      final names = optionIds.map((id) => optionIdToName[id] ?? '').toList();
      final combinedName = names.join(' - '); // Customize separator if needed
      log(oprations.productDataWrapper.defaultPrice.toString());

      return ProductOptionView(
        name: combinedName,
        optionIds: optionIds,
        quantity: 0,
        price: 0,
        discount: oprations.productDataWrapper.discount,
        type: oprations.productDataWrapper.discountType,
        priceAfterDiscount: 0,
        isActive: true,
        image: '',
        discountExpiration: oprations.productDataWrapper.discountExpiration,
      );
    }).toList();
  }

  void generateProductsOptionsPrices(value) {
    final views = buildProductOptionPricesViews(value);
    oprations.productOptionsView = views ?? [];
    emit(CategoryState.generatedProductOptionsPrices());
  }

  List<ProductOptionView>? buildProductOptionPricesViews(value) {
    return oprations.productOptionsView.map((optionView) {
      return optionView.copyWith(
        price: value ? oprations.productDataWrapper.defaultPrice : 0,
      );
    }).toList();
  }

  void toggleHasOptionKey() {
    emit(CategoryState.toogleHasOptionsKey());
  }

  Future<void> getProfile() async {
    emit(CategoryState.getProfileLoading());
    final result = await _repo.getProfile();
    result.when(
        success: (result) => {
              oprations.profile = result.data ?? const ProfileModel(),
              oprations.profileDataWrapper.nameAr = result.data?.nameAr,
              oprations.profileDataWrapper.nameEn = result.data?.nameEn,
              oprations.profileDataWrapper.phone = result.data?.phoneNumber,
              oprations.profileDataWrapper.address = result.data?.address,
              oprations.profileDataWrapper.cityId = result.data?.cityId,
              oprations.profileDataWrapper.lat = result.data?.lat,
              oprations.profileDataWrapper.lng = result.data?.lng,
              oprations.profileDataWrapper.appActivityId =
                  result.data?.appActivityId,
              emit(CategoryState.getProfileSuccess(
                result.data ?? const ProfileModel(),
              )),
            },
        failure: (message) => emit(CategoryState.getProfileFailure(message)));
  }

  Future<void> editProfile() async {
    emit(CategoryState.editProfileLoading());
    final body =
        EditProfileBody.fromJson(oprations.profileDataWrapper.toJson());
    final result = await _repo.editProfile(body);
    result.when(
        success: (result) => {
              oprations.profile = result.data ?? const ProfileModel(),
              oprations.profileDataWrapper.nameAr = result.data?.nameAr,
              oprations.profileDataWrapper.nameEn = result.data?.nameEn,
              oprations.profileDataWrapper.phone = result.data?.phoneNumber,
              oprations.profileDataWrapper.isActive = result.data?.isActive,
              oprations.profileDataWrapper.address = result.data?.address,
              oprations.profileDataWrapper.cityId = result.data?.cityId,
              oprations.profileDataWrapper.lat = result.data?.lat,
              oprations.profileDataWrapper.lng = result.data?.lng,
              oprations.profileDataWrapper.appActivityId =
                  result.data?.appActivityId,
              emit(CategoryState.editProfileSuccess(
                result.data ?? const ProfileModel(),
              )),
            },
        failure: (message) => emit(CategoryState.editProfileFailure(message)));
  }

  Future<void> storeOrdersCount() async {
    final result = await _repo.storeOrdersCount();
    result.when(
        success: (result) => {
              oprations.ordersCount = result.data ?? 0,
            },
        failure: (message) => {});
  }

  Future<void> getCities() async {
    final result = await _repo.getCities();
    result.when(
        success: (result) => {
              oprations.cities = result.data ?? [],
            },
        failure: (message) => {});
  }

  Future<void> getAppActivites() async {
    final result = await _repo.getAppActivites();
    result.when(
        success: (result) => {
              oprations.appActivities = result.data ?? [],
            },
        failure: (message) => {});
  }
}
