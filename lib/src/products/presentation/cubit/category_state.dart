part of 'category_cubit.dart';

@unfreezed
class CategoryState with _$CategoryState {
  factory CategoryState.initial() = _Initial;
  // categories
  factory CategoryState.categoriesLoading() = _CategoriesLoading;
  factory CategoryState.categoriesSuccess(List<CategoryModel> categories) =
      _CategoriesSuccess;
  factory CategoryState.categoriesFailure(String message) = _CategoriesFailure;
  // category
  factory CategoryState.categoryLoading() = _CategoryLoading;
  factory CategoryState.categorySuccess(CategoryModel category) =
      _CategorySuccess;
  factory CategoryState.categoryFailure(String message) = _CategoryFailure;
  // Add category
  factory CategoryState.addcategoryLoading() = _AddCategoryLoading;
  factory CategoryState.addcategorySuccess({CategoryModel? category}) =
      _AddCategorySuccess;
  factory CategoryState.addcategoryFailure(String message) =
      _AddCategoryFailure;
  // Add subcategory
  factory CategoryState.addsubcategoryLoading() = _AddSubcategoryLoading;
  factory CategoryState.addsubcategorySuccess({SubcategoryModel? subcategory}) =
      _AddSubcategorySuccess;
  factory CategoryState.addsubcategoryFailure(String message) =
      _AddSubcategoryFailure;

  // Essientials list

  factory CategoryState.additionListLoading() = _AdditionListLoading;
  factory CategoryState.additionListSuccess() = _AdditionListSuccess;
  factory CategoryState.additionListFailure(String message) =
      _AdditionListFailure;

  factory CategoryState.subcategoriesListLoading() = _SubcategoriesListLoading;
  factory CategoryState.subcategoriesListSuccess() = _SubcategoriesListSuccess;
  factory CategoryState.subcategoriesListFailure(String message) =
      _SubcategoriesListFailure;

// generated product option
  factory CategoryState.generatedProductOptions() = _GeneratedProductOptions;
  factory CategoryState.generatedProductOptionsPrices() =
      _GeneratedProductOptionsPrices;

  // products
  factory CategoryState.productsLoading() = _ProductsLoading;
  factory CategoryState.productsSuccess(List<ProductModel> products) =
      _ProductsSuccess;
  factory CategoryState.productsFailure(String message) = _ProductsFailure;

  factory CategoryState.productLoading() = _ProductLoading;
  factory CategoryState.productSuccess() = _ProductSuccess;
  factory CategoryState.productFailure(String message) = _ProductFailure;

  factory CategoryState.addProductLoading() = _AddProductLoading;
  factory CategoryState.addProductsuccess() = _AddProductSuccess;
  factory CategoryState.addProductFailure(String message) = _AddProductFailure;

  factory CategoryState.addProductOptionLoading() = _AddProductOptionLoading;
  factory CategoryState.addProductOptionsuccess() = _AddProductOptionSuccess;
  factory CategoryState.addProductOptionFailure(String message) =
      _AddProductOptionFailure;

  factory CategoryState.addProductAdditionLoading() =
      _AddProductAdditionLoading;
  factory CategoryState.addProductAdditionsuccess() =
      _AddProductAdditionSuccess;
  factory CategoryState.addProductAdditionFailure(String message) =
      _AddProductAdditionFailure;

  factory CategoryState.toogleHasOptionsKey() = _ToogleHasOptionsKey;

  // options
  factory CategoryState.createOptionLoading() = _CreateOptionLoading;
  factory CategoryState.createOptionSuccess() = _CreateOptionSuccess;
  factory CategoryState.createOptionFailure(String message) =
      _CreateOptionFailure;

  // profile
  factory CategoryState.getProfileLoading() = _GetProfileLoading;
  factory CategoryState.getProfileSuccess(ProfileModel profile) =
      _GetProfileSuccess;
  factory CategoryState.getProfileFailure(String message) = _GetProfileFailure;

  factory CategoryState.editProfileLoading() = _EditProfileLoading;
  factory CategoryState.editProfileSuccess(ProfileModel profile) =
      _EditProfileSuccess;
  factory CategoryState.editProfileFailure(String message) =
      _EditProfileFailure;

  //Group

  factory CategoryState.editGroupLoading() = _EditGroupLoading;
  factory CategoryState.editGroupSuccess() = _EditGroupSuccess;
  factory CategoryState.editGroupFailure(String message) = _EditGroupFailure;

  //Addition

  factory CategoryState.editAdditionLoading() = _EditAdditionLoading;
  factory CategoryState.editAdditionSuccess() = _EditAdditionSuccess;
  factory CategoryState.editAdditionFailure(String message) =
      _EditAdditionFailure;
}
