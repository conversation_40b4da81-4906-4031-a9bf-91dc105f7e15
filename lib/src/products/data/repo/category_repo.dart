import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/products/data/api_service/category_api_service.dart';
import 'package:alsarea_store/src/products/data/models/body/add_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/categories_body.dart';
import 'package:alsarea_store/src/products/data/models/body/create_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_group_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_profile_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_groups_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_quieries.dart';
import 'package:alsarea_store/src/products/data/models/response/add_category_response.dart';
import 'package:alsarea_store/src/products/data/models/response/add_products_response.dart';
import 'package:alsarea_store/src/products/data/models/response/add_subcategory_response.dart';
import 'package:alsarea_store/src/products/data/models/response/addition_list_response.dart';
import 'package:alsarea_store/src/products/data/models/response/create_option_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_addition_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_group_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_product_addition_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_product_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_app_activites_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_categories_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_cities_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_groups_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_orders_count_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_product_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_products_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_profile_response.dart';
import 'package:alsarea_store/src/products/data/models/response/subcategories_list_response.dart';

class CategoryRepo {
  final CategoryApiService _apiService;
  CategoryRepo(this._apiService);

  Future<Result<GetCategoriesResponse>> getCategories(CategoriesBody body) =>
      errorHandlerAsync(() => _apiService.getCategories(body));

  Future<Result<AddCategoryResponse>> getCategory(int id) =>
      errorHandlerAsync(() => _apiService.getCategory(id));

  Future<Result<AddCategoryResponse>> addCategyry(AddCategoryBody body) =>
      errorHandlerAsync(() => _apiService.addCategory(body));

  Future<Result<AddCategoryResponse>> editCategory(EditCategoryBody body) =>
      errorHandlerAsync(() => _apiService.editCategory(body));

  Future<Result<BaseResponse>> deleteCategory(int id) =>
      errorHandlerAsync(() => _apiService.deleteCategory(id));

  Future<Result<AddSubcategoryResponse>> addSubcategyry(
          AddSubcategoryBody body) =>
      errorHandlerAsync(() => _apiService.addSubcategory(body));

  Future<Result<AddSubcategoryResponse>> editSubcategory(
          EditSubcategoryBody body) =>
      errorHandlerAsync(() => _apiService.editSubcategory(body));

  Future<Result<BaseResponse>> deleteSubcategory(int id) =>
      errorHandlerAsync(() => _apiService.deleteSubcategory(id));

  Future<Result<SubcategoriesListResponse>> subcategoriesList() =>
      errorHandlerAsync(() => _apiService.subcategoriesList());

  Future<Result<AdditionListResponse>> additionList() =>
      errorHandlerAsync(() => _apiService.additionList());

  Future<Result<EditAdditionResponse>> createAddition(EditAdditionBody body) =>
      errorHandlerAsync(() => _apiService.createAddition(body));

  Future<Result<EditAdditionResponse>> editAddition(
          EditAdditionBody body, int id) =>
      errorHandlerAsync(() => _apiService.editAddition(body, id));

  Future<Result<BaseResponse>> deleteAddition(int id) =>
      errorHandlerAsync(() => _apiService.deleteAddition(id));

  Future<Result<GetProductsResponse>> getProducts(
          GetProductsBody body, GetProductsQuieries queires) =>
      errorHandlerAsync(() => _apiService.getProducts(body, queires));

  Future<Result<GetProductResponse>> getProduct(int id) =>
      errorHandlerAsync(() => _apiService.getProduct(id));

  Future<Result<GetGroupsResponse>> getGroups(GetGroupsBody body) =>
      errorHandlerAsync(() => _apiService.getGroups(body));

  Future<Result<EditGroupResponse>> createGroup(EditGroupBody body) =>
      errorHandlerAsync(() => _apiService.createGroup(body));

  Future<Result<EditGroupResponse>> editGroup(EditGroupBody body, int id) =>
      errorHandlerAsync(() => _apiService.editGroup(body, id));

  Future<Result<BaseResponse>> deleteGroup(int id) =>
      errorHandlerAsync(() => _apiService.deleteGroup(id));

  Future<Result<AddProductResponse>> createProduct(AddProductBody body) =>
      errorHandlerAsync(() => _apiService.createProduct(body));

  Future<Result<EditProductResponse>> editProduct(EditProductBody body) =>
      errorHandlerAsync(() => _apiService.editProduct(body));

  Future<Result<GetProductResponse>> createProductOption(
          AddProductOptionBody body) =>
      errorHandlerAsync(() => _apiService.createProductOption(body));

  Future<Result<EditProductAdditionResponse>> editProductAddition(
          EditProductAdditionBody body) =>
      errorHandlerAsync(() => _apiService.editProductAddition(body));

  Future<Result<EditProductAdditionResponse>> createProductAddition(
          EditProductAdditionBody body) =>
      errorHandlerAsync(() => _apiService.createProductAddition(body));

  Future<Result<CreateOptionResponse>> createOption(CreateOptionBody body) =>
      errorHandlerAsync(() => _apiService.createOption(body));

  Future<Result<CreateOptionResponse>> editOption(CreateOptionBody body) =>
      errorHandlerAsync(() => _apiService.editOption(body));

  Future<Result<BaseResponse>> deleteOption(int id) =>
      errorHandlerAsync(() => _apiService.deleteOption(id));

  Future<Result<GetCitiesResponse>> getCities() =>
      errorHandlerAsync(() => _apiService.getCities());

  Future<Result<GetAppActivitesResponse>> getAppActivites() =>
      errorHandlerAsync(() => _apiService.getAppActivites());

  Future<Result<GetProfileResponse>> getProfile() =>
      errorHandlerAsync(() => _apiService.getProfile());

  Future<Result<GetProfileResponse>> editProfile(EditProfileBody body) =>
      errorHandlerAsync(() => _apiService.editProfile(body));

  Future<Result<GetOrdersCountResponse>> storeOrdersCount() =>
      errorHandlerAsync(() => _apiService.storeOrdersCount());
}
