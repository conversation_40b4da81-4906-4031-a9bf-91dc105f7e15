// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_wrapper.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryDataWrapper _$CategoryDataWrapperFromJson(Map<String, dynamic> json) =>
    CategoryDataWrapper(
      skip: (json['skip'] as num?)?.toInt(),
      take: (json['take'] as num?)?.toInt() ?? 10,
      id: (json['id'] as num?)?.toInt(),
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      image: json['image'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      orderNumber: (json['orderNumber'] as num?)?.toInt() ?? 1,
    );

Map<String, dynamic> _$CategoryDataWrapperToJson(
        CategoryDataWrapper instance) =>
    <String, dynamic>{
      if (instance.skip case final value?) 'skip': value,
      if (instance.take case final value?) 'take': value,
      if (instance.id case final value?) 'id': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.image case final value?) 'image': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.orderNumber case final value?) 'orderNumber': value,
    };

SubcategoryDataWrapper _$SubcategoryDataWrapperFromJson(
        Map<String, dynamic> json) =>
    SubcategoryDataWrapper(
      subId: (json['id'] as num?)?.toInt(),
      id: (json['parentId'] as num?)?.toInt(),
      nameArSubcategory: json['nameAr'] as String?,
      nameEnSubcategory: json['nameEn'] as String?,
      isActiveSubcategory: json['isActive'] as bool? ?? true,
      orderNumberSubcategory: (json['orderNumber'] as num?)?.toInt() ?? 1,
      numberOfProducts: (json['numberOfProducts'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SubcategoryDataWrapperToJson(
        SubcategoryDataWrapper instance) =>
    <String, dynamic>{
      if (instance.subId case final value?) 'id': value,
      if (instance.id case final value?) 'parentId': value,
      if (instance.nameArSubcategory case final value?) 'nameAr': value,
      if (instance.nameEnSubcategory case final value?) 'nameEn': value,
      if (instance.isActiveSubcategory case final value?) 'isActive': value,
      if (instance.orderNumberSubcategory case final value?)
        'orderNumber': value,
      if (instance.numberOfProducts case final value?)
        'numberOfProducts': value,
    };

ProductsDataWrapper _$ProductsDataWrapperFromJson(Map<String, dynamic> json) =>
    ProductsDataWrapper(
      skip: (json['skip'] as num?)?.toInt(),
      take: (json['take'] as num?)?.toInt() ?? 10,
      search: json['search'] as String? ?? '',
      sales: json['sales'] as String? ?? '',
      byDate: json['byDate'] as String? ?? '',
      discount: json['discount'] as String? ?? '',
      categoryId: (json['categoryId'] as num?)?.toInt(),
      subCategoryId: (json['subCategoryId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProductsDataWrapperToJson(
        ProductsDataWrapper instance) =>
    <String, dynamic>{
      if (instance.skip case final value?) 'skip': value,
      if (instance.take case final value?) 'take': value,
      if (instance.search case final value?) 'search': value,
      if (instance.sales case final value?) 'sales': value,
      if (instance.byDate case final value?) 'byDate': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.categoryId case final value?) 'categoryId': value,
      if (instance.subCategoryId case final value?) 'subCategoryId': value,
    };

ProductDataWrapper _$ProductDataWrapperFromJson(Map<String, dynamic> json) =>
    ProductDataWrapper(
      id: (json['id'] as num?)?.toInt(),
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      descriptionEn: json['descriptionEn'] as String?,
      image: json['image'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      hasOptions: json['hasOptions'] as bool? ?? true,
      categoriesId: (json['categoriesId'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      covers:
          (json['covers'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isForPoints: json['isForPoints'] as bool? ?? false,
      hasOffer: json['hasOffer'] as bool? ?? true,
      isAvailable: json['isAvailable'] as bool? ?? true,
      showOnMainPage: json['showOnMainPage'] as bool? ?? true,
      productOptions: (json['productOptions'] as List<dynamic>?)
          ?.map((e) => ProductOptionView.fromJson(e as Map<String, dynamic>))
          .toList(),
      defaultPrice: (json['defaultPrice'] as num?)?.toDouble(),
      discountType: (json['discountType'] as num?)?.toInt(),
      discount: (json['discount'] as num?)?.toDouble(),
      discountExpiration: json['discountExpiration'] == null
          ? null
          : DateTime.parse(json['discountExpiration'] as String),
    );

Map<String, dynamic> _$ProductDataWrapperToJson(ProductDataWrapper instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.descriptionAr case final value?) 'descriptionAr': value,
      if (instance.descriptionEn case final value?) 'descriptionEn': value,
      if (instance.image case final value?) 'image': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.hasOptions case final value?) 'hasOptions': value,
      if (instance.categoriesId case final value?) 'categoriesId': value,
      if (instance.covers case final value?) 'covers': value,
      if (instance.isForPoints case final value?) 'isForPoints': value,
      if (instance.hasOffer case final value?) 'hasOffer': value,
      if (instance.isAvailable case final value?) 'isAvailable': value,
      if (instance.showOnMainPage case final value?) 'showOnMainPage': value,
      if (instance.productOptions?.map((e) => e.toJson()).toList()
          case final value?)
        'productOptions': value,
      if (instance.defaultPrice case final value?) 'defaultPrice': value,
      if (instance.discountType case final value?) 'discountType': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.discountExpiration?.toIso8601String() case final value?)
        'discountExpiration': value,
    };

ProductOptionDataWrapper _$ProductOptionDataWrapperFromJson(
        Map<String, dynamic> json) =>
    ProductOptionDataWrapper(
      productOptionId: (json['productOptionId'] as num?)?.toInt(),
      productId: (json['productId'] as num?)?.toInt(),
      optionIds: (json['optionIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      hasOptions: json['hasOptions'] as bool?,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      descriptionEn: json['descriptionEn'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      isAvailable: json['isAvailable'] as bool? ?? true,
      discountExpiration: json['discountExpiration'] == null
          ? null
          : DateTime.parse(json['discountExpiration'] as String),
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toInt(),
      image: json['image'] as String?,
    );

Map<String, dynamic> _$ProductOptionDataWrapperToJson(
        ProductOptionDataWrapper instance) =>
    <String, dynamic>{
      if (instance.productOptionId case final value?) 'productOptionId': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.optionIds case final value?) 'optionIds': value,
      if (instance.hasOptions case final value?) 'hasOptions': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.descriptionAr case final value?) 'descriptionAr': value,
      if (instance.descriptionEn case final value?) 'descriptionEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.isAvailable case final value?) 'isAvailable': value,
      if (instance.discountExpiration?.toIso8601String() case final value?)
        'discountExpiration': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.type case final value?) 'type': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.image case final value?) 'image': value,
    };

ProductAdditionDataWrapper _$ProductAdditionDataWrapperFromJson(
        Map<String, dynamic> json) =>
    ProductAdditionDataWrapper(
      id: (json['id'] as num?)?.toInt() ?? 0,
      additionId: (json['additionId'] as num?)?.toInt(),
      productOptionId: (json['productOptionId'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$ProductAdditionDataWrapperToJson(
        ProductAdditionDataWrapper instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.additionId case final value?) 'additionId': value,
      if (instance.productOptionId case final value?) 'productOptionId': value,
      if (instance.price case final value?) 'price': value,
      if (instance.isActive case final value?) 'isActive': value,
    };

OptionDataWrapper _$OptionDataWrapperFromJson(Map<String, dynamic> json) =>
    OptionDataWrapper(
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      groupId: (json['groupId'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$OptionDataWrapperToJson(OptionDataWrapper instance) =>
    <String, dynamic>{
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.groupId case final value?) 'groupId': value,
      if (instance.id case final value?) 'id': value,
    };

ProfileDataWrapper _$ProfileDataWrapperFromJson(Map<String, dynamic> json) =>
    ProfileDataWrapper(
      id: json['id'] as String?,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      phone: json['phone'] as String?,
      logo: json['logo'] as String?,
      isActive: json['isActive'] as bool?,
      cover: json['cover'] as String?,
      address: json['address'] as String?,
      cityId: (json['cityId'] as num?)?.toInt(),
      lat: (json['lat'] as num?)?.toInt(),
      lng: (json['lng'] as num?)?.toInt(),
      appActivityId: (json['appActivityId'] as num?)?.toInt(),
      storeRate: json['storeRate'] as String?,
    );

Map<String, dynamic> _$ProfileDataWrapperToJson(ProfileDataWrapper instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.logo case final value?) 'logo': value,
      if (instance.cover case final value?) 'cover': value,
      if (instance.address case final value?) 'address': value,
      if (instance.cityId case final value?) 'cityId': value,
      if (instance.lat case final value?) 'lat': value,
      if (instance.lng case final value?) 'lng': value,
      if (instance.appActivityId case final value?) 'appActivityId': value,
      if (instance.storeRate case final value?) 'storeRate': value,
    };

GroupDataWrapper _$GroupDataWrapperFromJson(Map<String, dynamic> json) =>
    GroupDataWrapper(
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      groupType: (json['groupType'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$GroupDataWrapperToJson(GroupDataWrapper instance) =>
    <String, dynamic>{
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.groupType case final value?) 'groupType': value,
    };

AdditionDataWrapper _$AdditionDataWrapperFromJson(Map<String, dynamic> json) =>
    AdditionDataWrapper(
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      defaultPrice: (json['defaultPrice'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$AdditionDataWrapperToJson(
        AdditionDataWrapper instance) =>
    <String, dynamic>{
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.defaultPrice case final value?) 'defaultPrice': value,
    };
