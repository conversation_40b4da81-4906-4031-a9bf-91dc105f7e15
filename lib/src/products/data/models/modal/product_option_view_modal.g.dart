// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_option_view_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductOptionView _$ProductOptionViewFromJson(Map<String, dynamic> json) =>
    ProductOptionView(
      productId: (json['productId'] as num?)?.toInt(),
      name: json['name'] as String?,
      optionIds: (json['optionIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      quantity: (json['quantity'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toInt(),
      isActive: json['isActive'] as bool?,
      image: json['image'] as String?,
      discountExpiration: json['discountExpiration'] == null
          ? null
          : DateTime.parse(json['discountExpiration'] as String),
    );

Map<String, dynamic> _$ProductOptionViewToJson(ProductOptionView instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.name case final value?) 'name': value,
      if (instance.optionIds case final value?) 'optionIds': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.type case final value?) 'type': value,
      if (instance.priceAfterDiscount case final value?)
        'priceAfterDiscount': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.image case final value?) 'image': value,
      if (instance.discountExpiration?.toIso8601String() case final value?)
        'discountExpiration': value,
    };
