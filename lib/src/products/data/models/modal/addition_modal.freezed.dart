// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'addition_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdditionModel _$AdditionModelFromJson(Map<String, dynamic> json) {
  return _AdditionModel.fromJson(json);
}

/// @nodoc
mixin _$AdditionModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "isActive")
  bool? get isActive => throw _privateConstructorUsedError;

  /// Serializes this AdditionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdditionModelCopyWith<AdditionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdditionModelCopyWith<$Res> {
  factory $AdditionModelCopyWith(
          AdditionModel value, $Res Function(AdditionModel) then) =
      _$AdditionModelCopyWithImpl<$Res, AdditionModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "isActive") bool? isActive});
}

/// @nodoc
class _$AdditionModelCopyWithImpl<$Res, $Val extends AdditionModel>
    implements $AdditionModelCopyWith<$Res> {
  _$AdditionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdditionModelImplCopyWith<$Res>
    implements $AdditionModelCopyWith<$Res> {
  factory _$$AdditionModelImplCopyWith(
          _$AdditionModelImpl value, $Res Function(_$AdditionModelImpl) then) =
      __$$AdditionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "isActive") bool? isActive});
}

/// @nodoc
class __$$AdditionModelImplCopyWithImpl<$Res>
    extends _$AdditionModelCopyWithImpl<$Res, _$AdditionModelImpl>
    implements _$$AdditionModelImplCopyWith<$Res> {
  __$$AdditionModelImplCopyWithImpl(
      _$AdditionModelImpl _value, $Res Function(_$AdditionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_$AdditionModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdditionModelImpl implements _AdditionModel {
  const _$AdditionModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "isActive") this.isActive});

  factory _$AdditionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdditionModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "isActive")
  final bool? isActive;

  @override
  String toString() {
    return 'AdditionModel(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdditionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, isActive);

  /// Create a copy of AdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdditionModelImplCopyWith<_$AdditionModelImpl> get copyWith =>
      __$$AdditionModelImplCopyWithImpl<_$AdditionModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdditionModelImplToJson(
      this,
    );
  }
}

abstract class _AdditionModel implements AdditionModel {
  const factory _AdditionModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "name") final String? name,
      @JsonKey(name: "isActive") final bool? isActive}) = _$AdditionModelImpl;

  factory _AdditionModel.fromJson(Map<String, dynamic> json) =
      _$AdditionModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "isActive")
  bool? get isActive;

  /// Create a copy of AdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdditionModelImplCopyWith<_$AdditionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
