// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CategoryModelImpl _$$CategoryModelImplFromJson(Map<String, dynamic> json) =>
    _$CategoryModelImpl(
      id: (json['id'] as num?)?.toInt(),
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      store: json['store'] as String?,
      image: json['image'] as String?,
      subCategoriesCount: (json['subCategoriesCount'] as num?)?.toInt(),
      subCategories: (json['subCategories'] as List<dynamic>?)
          ?.map((e) => SubcategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CategoryModelImplToJson(_$CategoryModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.store case final value?) 'store': value,
      if (instance.image case final value?) 'image': value,
      if (instance.subCategoriesCount case final value?)
        'subCategoriesCount': value,
      if (instance.subCategories case final value?) 'subCategories': value,
    };

_$SubcategoryModelImpl _$$SubcategoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SubcategoryModelImpl(
      subCategoryId: (json['subCategoryId'] as num?)?.toInt(),
      subCategoryNameAr: json['subCategoryNameAr'] as String?,
      subCategoryNameEn: json['subCategoryNameEn'] as String?,
      isActive: json['isActive'] as bool?,
      orderNumber: (json['orderNumber'] as num?)?.toInt(),
      numberOfProducts: (json['numberOfProducts'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SubcategoryModelImplToJson(
        _$SubcategoryModelImpl instance) =>
    <String, dynamic>{
      if (instance.subCategoryId case final value?) 'subCategoryId': value,
      if (instance.subCategoryNameAr case final value?)
        'subCategoryNameAr': value,
      if (instance.subCategoryNameEn case final value?)
        'subCategoryNameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.orderNumber case final value?) 'orderNumber': value,
      if (instance.numberOfProducts case final value?)
        'numberOfProducts': value,
    };
