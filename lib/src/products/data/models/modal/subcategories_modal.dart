import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'subcategories_modal.freezed.dart';
part 'subcategories_modal.g.dart';

SubcategoriesModel subcategoriesModelFromJson(String str) =>
    SubcategoriesModel.fromJson(json.decode(str));

String subcategoriesModelToJson(SubcategoriesModel data) =>
    json.encode(data.toJson());

@freezed
class SubcategoriesModel with _$SubcategoriesModel {
  const factory SubcategoriesModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON>ey(name: "name") String? name,
  }) = _SubcategoriesModel;

  factory SubcategoriesModel.fromJson(Map<String, dynamic> json) =>
      _$SubcategoriesModelFromJson(json);
}
