// To parse this JSON data, do
//
//     final CategoryModel = CategoryModelFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_modal.freezed.dart';
part 'category_modal.g.dart';

CategoryModel categoryModelFromJson(String str) =>
    CategoryModel.fromJson(json.decode(str));

String categoryModelToJson(CategoryModel data) => json.encode(data.toJson());

@freezed
class CategoryModel with _$CategoryModel {
  const factory CategoryModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON>ey(name: "nameAr") String? nameAr,
    @Json<PERSON><PERSON>(name: "nameEn") String? nameEn,
    @JsonKey(name: "store") String? store,
    @JsonKey(name: "image") String? image,
    @JsonKey(name: "subCategoriesCount") int? subCategoriesCount,
    @J<PERSON><PERSON>ey(name: "subCategories") List<SubcategoryModel>? subCategories,
  }) = _CategoryModel;

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);
}

@freezed
class SubcategoryModel with _$SubcategoryModel {
  const factory SubcategoryModel({
    @JsonKey(name: "subCategoryId") int? subCategoryId,
    @JsonKey(name: "subCategoryNameAr") String? subCategoryNameAr,
    @JsonKey(name: "subCategoryNameEn") String? subCategoryNameEn,
    @JsonKey(name: "isActive") bool? isActive,
    @JsonKey(name: "orderNumber") int? orderNumber,
    @JsonKey(name: "numberOfProducts") int? numberOfProducts,
  }) = _SubcategoryModel;

  factory SubcategoryModel.fromJson(Map<String, dynamic> json) =>
      _$SubcategoryModelFromJson(json);
}
