// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'addition_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdditionModelImpl _$$AdditionModelImplFromJson(Map<String, dynamic> json) =>
    _$AdditionModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$$AdditionModelImplToJson(_$AdditionModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.isActive case final value?) 'isActive': value,
    };
