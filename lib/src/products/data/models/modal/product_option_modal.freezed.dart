// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_option_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductGroupModel _$ProductGroupModelFromJson(Map<String, dynamic> json) {
  return _ProductGroupModel.fromJson(json);
}

/// @nodoc
mixin _$ProductGroupModel {
  @JsonKey(name: "groupId")
  int? get groupId => throw _privateConstructorUsedError;
  @JsonKey(name: "groupName")
  String? get groupName => throw _privateConstructorUsedError;
  @JsonKey(name: "options")
  List<ProductGroupOptionModel>? get options =>
      throw _privateConstructorUsedError;

  /// Serializes this ProductGroupModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductGroupModelCopyWith<ProductGroupModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductGroupModelCopyWith<$Res> {
  factory $ProductGroupModelCopyWith(
          ProductGroupModel value, $Res Function(ProductGroupModel) then) =
      _$ProductGroupModelCopyWithImpl<$Res, ProductGroupModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "groupId") int? groupId,
      @JsonKey(name: "groupName") String? groupName,
      @JsonKey(name: "options") List<ProductGroupOptionModel>? options});
}

/// @nodoc
class _$ProductGroupModelCopyWithImpl<$Res, $Val extends ProductGroupModel>
    implements $ProductGroupModelCopyWith<$Res> {
  _$ProductGroupModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = freezed,
    Object? groupName = freezed,
    Object? options = freezed,
  }) {
    return _then(_value.copyWith(
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      groupName: freezed == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String?,
      options: freezed == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<ProductGroupOptionModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductGroupModelImplCopyWith<$Res>
    implements $ProductGroupModelCopyWith<$Res> {
  factory _$$ProductGroupModelImplCopyWith(_$ProductGroupModelImpl value,
          $Res Function(_$ProductGroupModelImpl) then) =
      __$$ProductGroupModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "groupId") int? groupId,
      @JsonKey(name: "groupName") String? groupName,
      @JsonKey(name: "options") List<ProductGroupOptionModel>? options});
}

/// @nodoc
class __$$ProductGroupModelImplCopyWithImpl<$Res>
    extends _$ProductGroupModelCopyWithImpl<$Res, _$ProductGroupModelImpl>
    implements _$$ProductGroupModelImplCopyWith<$Res> {
  __$$ProductGroupModelImplCopyWithImpl(_$ProductGroupModelImpl _value,
      $Res Function(_$ProductGroupModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = freezed,
    Object? groupName = freezed,
    Object? options = freezed,
  }) {
    return _then(_$ProductGroupModelImpl(
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      groupName: freezed == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String?,
      options: freezed == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<ProductGroupOptionModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductGroupModelImpl implements _ProductGroupModel {
  const _$ProductGroupModelImpl(
      {@JsonKey(name: "groupId") this.groupId,
      @JsonKey(name: "groupName") this.groupName,
      @JsonKey(name: "options") final List<ProductGroupOptionModel>? options})
      : _options = options;

  factory _$ProductGroupModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductGroupModelImplFromJson(json);

  @override
  @JsonKey(name: "groupId")
  final int? groupId;
  @override
  @JsonKey(name: "groupName")
  final String? groupName;
  final List<ProductGroupOptionModel>? _options;
  @override
  @JsonKey(name: "options")
  List<ProductGroupOptionModel>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductGroupModel(groupId: $groupId, groupName: $groupName, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductGroupModelImpl &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, groupId, groupName,
      const DeepCollectionEquality().hash(_options));

  /// Create a copy of ProductGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductGroupModelImplCopyWith<_$ProductGroupModelImpl> get copyWith =>
      __$$ProductGroupModelImplCopyWithImpl<_$ProductGroupModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductGroupModelImplToJson(
      this,
    );
  }
}

abstract class _ProductGroupModel implements ProductGroupModel {
  const factory _ProductGroupModel(
      {@JsonKey(name: "groupId") final int? groupId,
      @JsonKey(name: "groupName") final String? groupName,
      @JsonKey(name: "options")
      final List<ProductGroupOptionModel>? options}) = _$ProductGroupModelImpl;

  factory _ProductGroupModel.fromJson(Map<String, dynamic> json) =
      _$ProductGroupModelImpl.fromJson;

  @override
  @JsonKey(name: "groupId")
  int? get groupId;
  @override
  @JsonKey(name: "groupName")
  String? get groupName;
  @override
  @JsonKey(name: "options")
  List<ProductGroupOptionModel>? get options;

  /// Create a copy of ProductGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductGroupModelImplCopyWith<_$ProductGroupModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductGroupOptionModel _$ProductGroupOptionModelFromJson(
    Map<String, dynamic> json) {
  return _ProductGroupOptionModel.fromJson(json);
}

/// @nodoc
mixin _$ProductGroupOptionModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "value")
  String? get value => throw _privateConstructorUsedError;

  /// Serializes this ProductGroupOptionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductGroupOptionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductGroupOptionModelCopyWith<ProductGroupOptionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductGroupOptionModelCopyWith<$Res> {
  factory $ProductGroupOptionModelCopyWith(ProductGroupOptionModel value,
          $Res Function(ProductGroupOptionModel) then) =
      _$ProductGroupOptionModelCopyWithImpl<$Res, ProductGroupOptionModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "value") String? value});
}

/// @nodoc
class _$ProductGroupOptionModelCopyWithImpl<$Res,
        $Val extends ProductGroupOptionModel>
    implements $ProductGroupOptionModelCopyWith<$Res> {
  _$ProductGroupOptionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductGroupOptionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductGroupOptionModelImplCopyWith<$Res>
    implements $ProductGroupOptionModelCopyWith<$Res> {
  factory _$$ProductGroupOptionModelImplCopyWith(
          _$ProductGroupOptionModelImpl value,
          $Res Function(_$ProductGroupOptionModelImpl) then) =
      __$$ProductGroupOptionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "value") String? value});
}

/// @nodoc
class __$$ProductGroupOptionModelImplCopyWithImpl<$Res>
    extends _$ProductGroupOptionModelCopyWithImpl<$Res,
        _$ProductGroupOptionModelImpl>
    implements _$$ProductGroupOptionModelImplCopyWith<$Res> {
  __$$ProductGroupOptionModelImplCopyWithImpl(
      _$ProductGroupOptionModelImpl _value,
      $Res Function(_$ProductGroupOptionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductGroupOptionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? value = freezed,
  }) {
    return _then(_$ProductGroupOptionModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductGroupOptionModelImpl implements _ProductGroupOptionModel {
  const _$ProductGroupOptionModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "value") this.value});

  factory _$ProductGroupOptionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductGroupOptionModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "value")
  final String? value;

  @override
  String toString() {
    return 'ProductGroupOptionModel(id: $id, name: $name, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductGroupOptionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, value);

  /// Create a copy of ProductGroupOptionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductGroupOptionModelImplCopyWith<_$ProductGroupOptionModelImpl>
      get copyWith => __$$ProductGroupOptionModelImplCopyWithImpl<
          _$ProductGroupOptionModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductGroupOptionModelImplToJson(
      this,
    );
  }
}

abstract class _ProductGroupOptionModel implements ProductGroupOptionModel {
  const factory _ProductGroupOptionModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "value") final String? value}) =
      _$ProductGroupOptionModelImpl;

  factory _ProductGroupOptionModel.fromJson(Map<String, dynamic> json) =
      _$ProductGroupOptionModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "value")
  String? get value;

  /// Create a copy of ProductGroupOptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductGroupOptionModelImplCopyWith<_$ProductGroupOptionModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductAdditionModel _$ProductAdditionModelFromJson(Map<String, dynamic> json) {
  return _ProductAdditionModel.fromJson(json);
}

/// @nodoc
mixin _$ProductAdditionModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "additionId")
  int? get additionId => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "optionName")
  String? get optionName => throw _privateConstructorUsedError;
  @JsonKey(name: "isActive")
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "discount")
  double? get discount => throw _privateConstructorUsedError;
  @JsonKey(name: "nameAr")
  String? get nameAr => throw _privateConstructorUsedError;
  @JsonKey(name: "nameEn")
  String? get nameEn => throw _privateConstructorUsedError;
  @JsonKey(name: "priceAfterDiscount")
  int? get priceAfterDiscount => throw _privateConstructorUsedError;

  /// Serializes this ProductAdditionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductAdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductAdditionModelCopyWith<ProductAdditionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductAdditionModelCopyWith<$Res> {
  factory $ProductAdditionModelCopyWith(ProductAdditionModel value,
          $Res Function(ProductAdditionModel) then) =
      _$ProductAdditionModelCopyWithImpl<$Res, ProductAdditionModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "additionId") int? additionId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "optionName") String? optionName,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "discount") double? discount,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount});
}

/// @nodoc
class _$ProductAdditionModelCopyWithImpl<$Res,
        $Val extends ProductAdditionModel>
    implements $ProductAdditionModelCopyWith<$Res> {
  _$ProductAdditionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductAdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? additionId = freezed,
    Object? name = freezed,
    Object? optionName = freezed,
    Object? isActive = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? priceAfterDiscount = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      additionId: freezed == additionId
          ? _value.additionId
          : additionId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      optionName: freezed == optionName
          ? _value.optionName
          : optionName // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductAdditionModelImplCopyWith<$Res>
    implements $ProductAdditionModelCopyWith<$Res> {
  factory _$$ProductAdditionModelImplCopyWith(_$ProductAdditionModelImpl value,
          $Res Function(_$ProductAdditionModelImpl) then) =
      __$$ProductAdditionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "additionId") int? additionId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "optionName") String? optionName,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "discount") double? discount,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount});
}

/// @nodoc
class __$$ProductAdditionModelImplCopyWithImpl<$Res>
    extends _$ProductAdditionModelCopyWithImpl<$Res, _$ProductAdditionModelImpl>
    implements _$$ProductAdditionModelImplCopyWith<$Res> {
  __$$ProductAdditionModelImplCopyWithImpl(_$ProductAdditionModelImpl _value,
      $Res Function(_$ProductAdditionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductAdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? additionId = freezed,
    Object? name = freezed,
    Object? optionName = freezed,
    Object? isActive = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? priceAfterDiscount = freezed,
  }) {
    return _then(_$ProductAdditionModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      additionId: freezed == additionId
          ? _value.additionId
          : additionId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      optionName: freezed == optionName
          ? _value.optionName
          : optionName // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductAdditionModelImpl implements _ProductAdditionModel {
  const _$ProductAdditionModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "additionId") this.additionId,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "optionName") this.optionName,
      @JsonKey(name: "isActive") this.isActive,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "discount") this.discount,
      @JsonKey(name: "nameAr") this.nameAr,
      @JsonKey(name: "nameEn") this.nameEn,
      @JsonKey(name: "priceAfterDiscount") this.priceAfterDiscount});

  factory _$ProductAdditionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductAdditionModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "additionId")
  final int? additionId;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "optionName")
  final String? optionName;
  @override
  @JsonKey(name: "isActive")
  final bool? isActive;
  @override
  @JsonKey(name: "price")
  final double? price;
  @override
  @JsonKey(name: "discount")
  final double? discount;
  @override
  @JsonKey(name: "nameAr")
  final String? nameAr;
  @override
  @JsonKey(name: "nameEn")
  final String? nameEn;
  @override
  @JsonKey(name: "priceAfterDiscount")
  final int? priceAfterDiscount;

  @override
  String toString() {
    return 'ProductAdditionModel(id: $id, additionId: $additionId, name: $name, optionName: $optionName, isActive: $isActive, price: $price, discount: $discount, nameAr: $nameAr, nameEn: $nameEn, priceAfterDiscount: $priceAfterDiscount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductAdditionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.additionId, additionId) ||
                other.additionId == additionId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.optionName, optionName) ||
                other.optionName == optionName) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.priceAfterDiscount, priceAfterDiscount) ||
                other.priceAfterDiscount == priceAfterDiscount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, additionId, name, optionName,
      isActive, price, discount, nameAr, nameEn, priceAfterDiscount);

  /// Create a copy of ProductAdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductAdditionModelImplCopyWith<_$ProductAdditionModelImpl>
      get copyWith =>
          __$$ProductAdditionModelImplCopyWithImpl<_$ProductAdditionModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductAdditionModelImplToJson(
      this,
    );
  }
}

abstract class _ProductAdditionModel implements ProductAdditionModel {
  const factory _ProductAdditionModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "additionId") final int? additionId,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "optionName") final String? optionName,
          @JsonKey(name: "isActive") final bool? isActive,
          @JsonKey(name: "price") final double? price,
          @JsonKey(name: "discount") final double? discount,
          @JsonKey(name: "nameAr") final String? nameAr,
          @JsonKey(name: "nameEn") final String? nameEn,
          @JsonKey(name: "priceAfterDiscount") final int? priceAfterDiscount}) =
      _$ProductAdditionModelImpl;

  factory _ProductAdditionModel.fromJson(Map<String, dynamic> json) =
      _$ProductAdditionModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "additionId")
  int? get additionId;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "optionName")
  String? get optionName;
  @override
  @JsonKey(name: "isActive")
  bool? get isActive;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "discount")
  double? get discount;
  @override
  @JsonKey(name: "nameAr")
  String? get nameAr;
  @override
  @JsonKey(name: "nameEn")
  String? get nameEn;
  @override
  @JsonKey(name: "priceAfterDiscount")
  int? get priceAfterDiscount;

  /// Create a copy of ProductAdditionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductAdditionModelImplCopyWith<_$ProductAdditionModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
