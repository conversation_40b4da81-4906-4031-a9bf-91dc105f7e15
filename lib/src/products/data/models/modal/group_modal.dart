import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'group_modal.freezed.dart';
part 'group_modal.g.dart';

GroupModel groupModelFromJson(String str) =>
    GroupModel.fromJson(json.decode(str));

String groupModelToJson(GroupModel data) => json.encode(data.toJson());

@freezed
class GroupModel with _$GroupModel {
  const factory GroupModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @Json<PERSON>ey(name: "type") int? type,
    @<PERSON><PERSON><PERSON><PERSON>(name: "options") List<OptionModel>? options,
  }) = _GroupModel;

  factory GroupModel.fromJson(Map<String, dynamic> json) =>
      _$GroupModelFromJson(json);
}

@freezed
class OptionModel with _$OptionModel {
  const factory OptionModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: "id") int? id,
    @<PERSON><PERSON>K<PERSON>(name: "groupId") int? groupId,
    @JsonKey(name: "name") String? name,
    @Json<PERSON><PERSON>(name: "isActive") bool? isActive,
  }) = _OptionModel;

  factory OptionModel.fromJson(Map<String, dynamic> json) =>
      _$OptionModelFromJson(json);
}
