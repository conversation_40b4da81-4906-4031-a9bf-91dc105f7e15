// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductModelImpl _$$ProductModelImplFromJson(Map<String, dynamic> json) =>
    _$ProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String?,
      groups: (json['groups'] as List<dynamic>?)
          ?.map((e) => GroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sliders:
          (json['sliders'] as List<dynamic>?)?.map((e) => e as String).toList(),
      options: (json['options'] as List<dynamic>?)
          ?.map((e) => ProductOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      isAvailable: json['isAvailable'] as bool?,
      isActive: json['isActive'] as bool?,
      discountType: (json['discountType'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      discount: (json['discount'] as num?)?.toInt(),
      orderCount: (json['orderCount'] as num?)?.toInt(),
      quantityInCart: (json['quantityInCart'] as num?)?.toInt(),
      hasOptions: json['hasOptions'] as bool?,
      temporaryDisable: json['temporaryDisable'] as bool?,
      productOptionId: (json['productOptionId'] as num?)?.toInt(),
      isDealOfDay: json['isDealOfDay'] as bool?,
      isFavorite: json['isFavorite'] as bool?,
      createdAt: json['createdAt'] as String?,
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductModelImplToJson(_$ProductModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.description case final value?) 'description': value,
      if (instance.imageUrl case final value?) 'imageUrl': value,
      if (instance.groups case final value?) 'groups': value,
      if (instance.sliders case final value?) 'sliders': value,
      if (instance.options case final value?) 'options': value,
      if (instance.isAvailable case final value?) 'isAvailable': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.discountType case final value?) 'discountType': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.orderCount case final value?) 'orderCount': value,
      if (instance.quantityInCart case final value?) 'quantityInCart': value,
      if (instance.hasOptions case final value?) 'hasOptions': value,
      if (instance.temporaryDisable case final value?)
        'temporaryDisable': value,
      if (instance.productOptionId case final value?) 'productOptionId': value,
      if (instance.isDealOfDay case final value?) 'isDealOfDay': value,
      if (instance.isFavorite case final value?) 'isFavorite': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.priceAfterDiscount case final value?)
        'priceAfterDiscount': value,
    };
