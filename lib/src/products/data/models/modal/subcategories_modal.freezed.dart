// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subcategories_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SubcategoriesModel _$SubcategoriesModelFromJson(Map<String, dynamic> json) {
  return _SubcategoriesModel.fromJson(json);
}

/// @nodoc
mixin _$SubcategoriesModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;

  /// Serializes this SubcategoriesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubcategoriesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubcategoriesModelCopyWith<SubcategoriesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubcategoriesModelCopyWith<$Res> {
  factory $SubcategoriesModelCopyWith(
          SubcategoriesModel value, $Res Function(SubcategoriesModel) then) =
      _$SubcategoriesModelCopyWithImpl<$Res, SubcategoriesModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id, @JsonKey(name: "name") String? name});
}

/// @nodoc
class _$SubcategoriesModelCopyWithImpl<$Res, $Val extends SubcategoriesModel>
    implements $SubcategoriesModelCopyWith<$Res> {
  _$SubcategoriesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubcategoriesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubcategoriesModelImplCopyWith<$Res>
    implements $SubcategoriesModelCopyWith<$Res> {
  factory _$$SubcategoriesModelImplCopyWith(_$SubcategoriesModelImpl value,
          $Res Function(_$SubcategoriesModelImpl) then) =
      __$$SubcategoriesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id, @JsonKey(name: "name") String? name});
}

/// @nodoc
class __$$SubcategoriesModelImplCopyWithImpl<$Res>
    extends _$SubcategoriesModelCopyWithImpl<$Res, _$SubcategoriesModelImpl>
    implements _$$SubcategoriesModelImplCopyWith<$Res> {
  __$$SubcategoriesModelImplCopyWithImpl(_$SubcategoriesModelImpl _value,
      $Res Function(_$SubcategoriesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubcategoriesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_$SubcategoriesModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubcategoriesModelImpl implements _SubcategoriesModel {
  const _$SubcategoriesModelImpl(
      {@JsonKey(name: "id") this.id, @JsonKey(name: "name") this.name});

  factory _$SubcategoriesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubcategoriesModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;

  @override
  String toString() {
    return 'SubcategoriesModel(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubcategoriesModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  /// Create a copy of SubcategoriesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubcategoriesModelImplCopyWith<_$SubcategoriesModelImpl> get copyWith =>
      __$$SubcategoriesModelImplCopyWithImpl<_$SubcategoriesModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubcategoriesModelImplToJson(
      this,
    );
  }
}

abstract class _SubcategoriesModel implements SubcategoriesModel {
  const factory _SubcategoriesModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "name") final String? name}) = _$SubcategoriesModelImpl;

  factory _SubcategoriesModel.fromJson(Map<String, dynamic> json) =
      _$SubcategoriesModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;

  /// Create a copy of SubcategoriesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubcategoriesModelImplCopyWith<_$SubcategoriesModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
