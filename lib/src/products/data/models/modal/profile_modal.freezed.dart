// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProfileModel _$ProfileModelFromJson(Map<String, dynamic> json) {
  return _ProfileModel.fromJson(json);
}

/// @nodoc
mixin _$ProfileModel {
  @JsonKey(name: "id")
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "nameAr")
  String? get nameAr => throw _privateConstructorUsedError;
  @JsonKey(name: "nameEn")
  String? get nameEn => throw _privateConstructorUsedError;
  @JsonKey(name: "phoneNumber")
  String? get phoneNumber => throw _privateConstructorUsedError;
  @JsonKey(name: "isActive")
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: "image")
  String? get image => throw _privateConstructorUsedError;
  @JsonKey(name: "cover")
  String? get cover => throw _privateConstructorUsedError;
  @JsonKey(name: "address")
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: "cityId")
  int? get cityId => throw _privateConstructorUsedError;
  @JsonKey(name: "lat")
  int? get lat => throw _privateConstructorUsedError;
  @JsonKey(name: "lng")
  int? get lng => throw _privateConstructorUsedError;
  @JsonKey(name: "appActivityId")
  int? get appActivityId => throw _privateConstructorUsedError;
  @JsonKey(name: "storeRate")
  String? get storeRate => throw _privateConstructorUsedError;

  /// Serializes this ProfileModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileModelCopyWith<ProfileModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileModelCopyWith<$Res> {
  factory $ProfileModelCopyWith(
          ProfileModel value, $Res Function(ProfileModel) then) =
      _$ProfileModelCopyWithImpl<$Res, ProfileModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "phoneNumber") String? phoneNumber,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "cover") String? cover,
      @JsonKey(name: "address") String? address,
      @JsonKey(name: "cityId") int? cityId,
      @JsonKey(name: "lat") int? lat,
      @JsonKey(name: "lng") int? lng,
      @JsonKey(name: "appActivityId") int? appActivityId,
      @JsonKey(name: "storeRate") String? storeRate});
}

/// @nodoc
class _$ProfileModelCopyWithImpl<$Res, $Val extends ProfileModel>
    implements $ProfileModelCopyWith<$Res> {
  _$ProfileModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? phoneNumber = freezed,
    Object? isActive = freezed,
    Object? image = freezed,
    Object? cover = freezed,
    Object? address = freezed,
    Object? cityId = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? appActivityId = freezed,
    Object? storeRate = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as int?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as int?,
      appActivityId: freezed == appActivityId
          ? _value.appActivityId
          : appActivityId // ignore: cast_nullable_to_non_nullable
              as int?,
      storeRate: freezed == storeRate
          ? _value.storeRate
          : storeRate // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileModelImplCopyWith<$Res>
    implements $ProfileModelCopyWith<$Res> {
  factory _$$ProfileModelImplCopyWith(
          _$ProfileModelImpl value, $Res Function(_$ProfileModelImpl) then) =
      __$$ProfileModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "phoneNumber") String? phoneNumber,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "cover") String? cover,
      @JsonKey(name: "address") String? address,
      @JsonKey(name: "cityId") int? cityId,
      @JsonKey(name: "lat") int? lat,
      @JsonKey(name: "lng") int? lng,
      @JsonKey(name: "appActivityId") int? appActivityId,
      @JsonKey(name: "storeRate") String? storeRate});
}

/// @nodoc
class __$$ProfileModelImplCopyWithImpl<$Res>
    extends _$ProfileModelCopyWithImpl<$Res, _$ProfileModelImpl>
    implements _$$ProfileModelImplCopyWith<$Res> {
  __$$ProfileModelImplCopyWithImpl(
      _$ProfileModelImpl _value, $Res Function(_$ProfileModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? phoneNumber = freezed,
    Object? isActive = freezed,
    Object? image = freezed,
    Object? cover = freezed,
    Object? address = freezed,
    Object? cityId = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? appActivityId = freezed,
    Object? storeRate = freezed,
  }) {
    return _then(_$ProfileModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as int?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as int?,
      appActivityId: freezed == appActivityId
          ? _value.appActivityId
          : appActivityId // ignore: cast_nullable_to_non_nullable
              as int?,
      storeRate: freezed == storeRate
          ? _value.storeRate
          : storeRate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileModelImpl implements _ProfileModel {
  const _$ProfileModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "nameAr") this.nameAr,
      @JsonKey(name: "nameEn") this.nameEn,
      @JsonKey(name: "phoneNumber") this.phoneNumber,
      @JsonKey(name: "isActive") this.isActive,
      @JsonKey(name: "image") this.image,
      @JsonKey(name: "cover") this.cover,
      @JsonKey(name: "address") this.address,
      @JsonKey(name: "cityId") this.cityId,
      @JsonKey(name: "lat") this.lat,
      @JsonKey(name: "lng") this.lng,
      @JsonKey(name: "appActivityId") this.appActivityId,
      @JsonKey(name: "storeRate") this.storeRate});

  factory _$ProfileModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String? id;
  @override
  @JsonKey(name: "nameAr")
  final String? nameAr;
  @override
  @JsonKey(name: "nameEn")
  final String? nameEn;
  @override
  @JsonKey(name: "phoneNumber")
  final String? phoneNumber;
  @override
  @JsonKey(name: "isActive")
  final bool? isActive;
  @override
  @JsonKey(name: "image")
  final String? image;
  @override
  @JsonKey(name: "cover")
  final String? cover;
  @override
  @JsonKey(name: "address")
  final String? address;
  @override
  @JsonKey(name: "cityId")
  final int? cityId;
  @override
  @JsonKey(name: "lat")
  final int? lat;
  @override
  @JsonKey(name: "lng")
  final int? lng;
  @override
  @JsonKey(name: "appActivityId")
  final int? appActivityId;
  @override
  @JsonKey(name: "storeRate")
  final String? storeRate;

  @override
  String toString() {
    return 'ProfileModel(id: $id, nameAr: $nameAr, nameEn: $nameEn, phoneNumber: $phoneNumber, isActive: $isActive, image: $image, cover: $cover, address: $address, cityId: $cityId, lat: $lat, lng: $lng, appActivityId: $appActivityId, storeRate: $storeRate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng) &&
            (identical(other.appActivityId, appActivityId) ||
                other.appActivityId == appActivityId) &&
            (identical(other.storeRate, storeRate) ||
                other.storeRate == storeRate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      nameAr,
      nameEn,
      phoneNumber,
      isActive,
      image,
      cover,
      address,
      cityId,
      lat,
      lng,
      appActivityId,
      storeRate);

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      __$$ProfileModelImplCopyWithImpl<_$ProfileModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileModelImplToJson(
      this,
    );
  }
}

abstract class _ProfileModel implements ProfileModel {
  const factory _ProfileModel(
          {@JsonKey(name: "id") final String? id,
          @JsonKey(name: "nameAr") final String? nameAr,
          @JsonKey(name: "nameEn") final String? nameEn,
          @JsonKey(name: "phoneNumber") final String? phoneNumber,
          @JsonKey(name: "isActive") final bool? isActive,
          @JsonKey(name: "image") final String? image,
          @JsonKey(name: "cover") final String? cover,
          @JsonKey(name: "address") final String? address,
          @JsonKey(name: "cityId") final int? cityId,
          @JsonKey(name: "lat") final int? lat,
          @JsonKey(name: "lng") final int? lng,
          @JsonKey(name: "appActivityId") final int? appActivityId,
          @JsonKey(name: "storeRate") final String? storeRate}) =
      _$ProfileModelImpl;

  factory _ProfileModel.fromJson(Map<String, dynamic> json) =
      _$ProfileModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String? get id;
  @override
  @JsonKey(name: "nameAr")
  String? get nameAr;
  @override
  @JsonKey(name: "nameEn")
  String? get nameEn;
  @override
  @JsonKey(name: "phoneNumber")
  String? get phoneNumber;
  @override
  @JsonKey(name: "isActive")
  bool? get isActive;
  @override
  @JsonKey(name: "image")
  String? get image;
  @override
  @JsonKey(name: "cover")
  String? get cover;
  @override
  @JsonKey(name: "address")
  String? get address;
  @override
  @JsonKey(name: "cityId")
  int? get cityId;
  @override
  @JsonKey(name: "lat")
  int? get lat;
  @override
  @JsonKey(name: "lng")
  int? get lng;
  @override
  @JsonKey(name: "appActivityId")
  int? get appActivityId;
  @override
  @JsonKey(name: "storeRate")
  String? get storeRate;

  /// Create a copy of ProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
