import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_activity_modal.freezed.dart';
part 'app_activity_modal.g.dart';

AppActivityModel appActivityModelFromJson(String str) =>
    AppActivityModel.fromJson(json.decode(str));

String appActivityModelToJson(AppActivityModel data) =>
    json.encode(data.toJson());

@freezed
class AppActivityModel with _$AppActivityModel {
  const factory AppActivityModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON>ey(name: "name") String? name,
  }) = _AppActivityModel;

  factory AppActivityModel.fromJson(Map<String, dynamic> json) =>
      _$AppActivityModelFromJson(json);
}
