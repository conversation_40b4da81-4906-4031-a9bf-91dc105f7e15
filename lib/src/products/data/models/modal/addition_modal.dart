import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'addition_modal.freezed.dart';
part 'addition_modal.g.dart';

AdditionModel additionModelFromJson(String str) =>
    AdditionModel.fromJson(json.decode(str));

String additionModelToJson(AdditionModel data) => json.encode(data.toJson());

@freezed
class AdditionModel with _$AdditionModel {
  const factory AdditionModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON>ey(name: "name") String? name,
    @Json<PERSON>ey(name: "isActive") bool? isActive,
  }) = _AdditionModel;

  factory AdditionModel.fromJson(Map<String, dynamic> json) =>
      _$AdditionModelFromJson(json);
}
