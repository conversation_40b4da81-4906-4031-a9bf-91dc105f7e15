import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'city_modal.freezed.dart';
part 'city_modal.g.dart';

CityModel cityModelFromJson(String str) => CityModel.fromJson(json.decode(str));

String cityModelToJson(CityModel data) => json.encode(data.toJson());

@freezed
class CityModel with _$CityModel {
  const factory CityModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
  }) = _CityModel;

  factory CityModel.fromJson(Map<String, dynamic> json) =>
      _$CityModelFromJson(json);
}
