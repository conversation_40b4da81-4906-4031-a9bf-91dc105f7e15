import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_option_view_modal.g.dart';

@JsonSerializable()
class ProductOptionView {
  @<PERSON><PERSON><PERSON><PERSON>(name: "productId")
  int? productId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "name")
  String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: "optionIds")
  List<int>? optionIds;
  @<PERSON>son<PERSON>ey(name: "quantity")
  int? quantity;
  @<PERSON>sonKey(name: "price")
  double? price;
  @<PERSON><PERSON><PERSON><PERSON>(name: "discount")
  double? discount;
  @<PERSON>son<PERSON><PERSON>(name: "type")
  int? type;
  @<PERSON><PERSON><PERSON><PERSON>(name: "priceAfterDiscount")
  int? priceAfterDiscount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "isActive")
  bool? isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: "image")
  String? image;
  @<PERSON>son<PERSON>ey(name: "discountExpiration")
  DateTime? discountExpiration;

  ProductOptionView({
    this.productId,
    this.name,
    this.optionIds,
    this.quantity,
    this.price,
    this.discount,
    this.type,
    this.priceAfterDiscount,
    this.isActive,
    this.image,
    this.discountExpiration,
  });

  ProductOptionView copyWith({
    String? name,
    List<int>? optionIds,
    int? quantity,
    double? price,
    double? discount,
    int? type,
    int? priceAfterDiscount,
    bool? isActive,
    String? image,
    DateTime? discountExpiration,
  }) {
    return ProductOptionView(
      productId: productId ?? productId,
      name: name ?? this.name,
      optionIds: optionIds ?? this.optionIds,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      discount: discount ?? this.discount,
      type: type ?? this.type,
      priceAfterDiscount: priceAfterDiscount ?? this.priceAfterDiscount,
      isActive: isActive ?? this.isActive,
      image: image ?? this.image,
      discountExpiration: discountExpiration ?? this.discountExpiration,
    );
  }

  factory ProductOptionView.fromJson(Map<String, dynamic> json) =>
      _$ProductOptionViewFromJson(json);

  Map<String, dynamic> toJson() => _$ProductOptionViewToJson(this);
}
