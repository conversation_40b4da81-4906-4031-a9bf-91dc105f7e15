import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_option_modal.freezed.dart';
part 'product_option_modal.g.dart';

@JsonSerializable()
class ProductOption {
  @Json<PERSON>ey(name: "id")
  int? id;
  @J<PERSON><PERSON><PERSON>(name: "price")
  double? price;
  @Json<PERSON>ey(name: "quantity")
  int? quantity;
  @Json<PERSON>ey(name: "discount")
  double? discount;
  @<PERSON>sonKey(name: "discountExpiration")
  DateTime? discountExpiration;
  @<PERSON><PERSON><PERSON><PERSON>(name: "isActive")
  bool? isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: "priceAfterDiscount")
  double? priceAfterDiscount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "discountType")
  int? discountType;
  @<PERSON>son<PERSON><PERSON>(name: "image")
  String? image;
  @Json<PERSON>ey(name: "optionDetails")
  List<ProductGroupModel>? optionDetails;
  @<PERSON><PERSON><PERSON>ey(name: "additions")
  List<ProductAdditionModel>? additions;
  ProductOption({
    this.id,
    this.price,
    this.discount,
    this.isActive,
    this.priceAfterDiscount,
    this.discountType,
    this.image,
    this.optionDetails,
    this.additions,
  });

  factory ProductOption.fromJson(Map<String, dynamic> json) =>
      _$ProductOptionFromJson(json);

  Map<String, dynamic> toJson() => _$ProductOptionToJson(this);
}

@freezed
class ProductGroupModel with _$ProductGroupModel {
  const factory ProductGroupModel({
    @JsonKey(name: "groupId") int? groupId,
    @JsonKey(name: "groupName") String? groupName,
    @JsonKey(name: "options") List<ProductGroupOptionModel>? options,
  }) = _ProductGroupModel;

  factory ProductGroupModel.fromJson(Map<String, dynamic> json) =>
      _$ProductGroupModelFromJson(json);
}

@freezed
class ProductGroupOptionModel with _$ProductGroupOptionModel {
  const factory ProductGroupOptionModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "value") String? value,
  }) = _ProductGroupOptionModel;

  factory ProductGroupOptionModel.fromJson(Map<String, dynamic> json) =>
      _$ProductGroupOptionModelFromJson(json);
}

@freezed
class ProductAdditionModel with _$ProductAdditionModel {
  const factory ProductAdditionModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "additionId") int? additionId,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "optionName") String? optionName,
    @JsonKey(name: "isActive") bool? isActive,
    @JsonKey(name: "price") double? price,
    @JsonKey(name: "discount") double? discount,
    @JsonKey(name: "nameAr") String? nameAr,
    @JsonKey(name: "nameEn") String? nameEn,
    @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount,
  }) = _ProductAdditionModel;

  factory ProductAdditionModel.fromJson(Map<String, dynamic> json) =>
      _$ProductAdditionModelFromJson(json);
}
