// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CategoryModel _$CategoryModelFromJson(Map<String, dynamic> json) {
  return _CategoryModel.fromJson(json);
}

/// @nodoc
mixin _$CategoryModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "nameAr")
  String? get nameAr => throw _privateConstructorUsedError;
  @JsonKey(name: "nameEn")
  String? get nameEn => throw _privateConstructorUsedError;
  @JsonKey(name: "store")
  String? get store => throw _privateConstructorUsedError;
  @JsonKey(name: "image")
  String? get image => throw _privateConstructorUsedError;
  @JsonKey(name: "subCategoriesCount")
  int? get subCategoriesCount => throw _privateConstructorUsedError;
  @JsonKey(name: "subCategories")
  List<SubcategoryModel>? get subCategories =>
      throw _privateConstructorUsedError;

  /// Serializes this CategoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CategoryModelCopyWith<CategoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CategoryModelCopyWith<$Res> {
  factory $CategoryModelCopyWith(
          CategoryModel value, $Res Function(CategoryModel) then) =
      _$CategoryModelCopyWithImpl<$Res, CategoryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "store") String? store,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "subCategoriesCount") int? subCategoriesCount,
      @JsonKey(name: "subCategories") List<SubcategoryModel>? subCategories});
}

/// @nodoc
class _$CategoryModelCopyWithImpl<$Res, $Val extends CategoryModel>
    implements $CategoryModelCopyWith<$Res> {
  _$CategoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? store = freezed,
    Object? image = freezed,
    Object? subCategoriesCount = freezed,
    Object? subCategories = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      subCategoriesCount: freezed == subCategoriesCount
          ? _value.subCategoriesCount
          : subCategoriesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategories: freezed == subCategories
          ? _value.subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<SubcategoryModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CategoryModelImplCopyWith<$Res>
    implements $CategoryModelCopyWith<$Res> {
  factory _$$CategoryModelImplCopyWith(
          _$CategoryModelImpl value, $Res Function(_$CategoryModelImpl) then) =
      __$$CategoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "nameAr") String? nameAr,
      @JsonKey(name: "nameEn") String? nameEn,
      @JsonKey(name: "store") String? store,
      @JsonKey(name: "image") String? image,
      @JsonKey(name: "subCategoriesCount") int? subCategoriesCount,
      @JsonKey(name: "subCategories") List<SubcategoryModel>? subCategories});
}

/// @nodoc
class __$$CategoryModelImplCopyWithImpl<$Res>
    extends _$CategoryModelCopyWithImpl<$Res, _$CategoryModelImpl>
    implements _$$CategoryModelImplCopyWith<$Res> {
  __$$CategoryModelImplCopyWithImpl(
      _$CategoryModelImpl _value, $Res Function(_$CategoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? store = freezed,
    Object? image = freezed,
    Object? subCategoriesCount = freezed,
    Object? subCategories = freezed,
  }) {
    return _then(_$CategoryModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      store: freezed == store
          ? _value.store
          : store // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      subCategoriesCount: freezed == subCategoriesCount
          ? _value.subCategoriesCount
          : subCategoriesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategories: freezed == subCategories
          ? _value._subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<SubcategoryModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CategoryModelImpl implements _CategoryModel {
  const _$CategoryModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "nameAr") this.nameAr,
      @JsonKey(name: "nameEn") this.nameEn,
      @JsonKey(name: "store") this.store,
      @JsonKey(name: "image") this.image,
      @JsonKey(name: "subCategoriesCount") this.subCategoriesCount,
      @JsonKey(name: "subCategories")
      final List<SubcategoryModel>? subCategories})
      : _subCategories = subCategories;

  factory _$CategoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CategoryModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "nameAr")
  final String? nameAr;
  @override
  @JsonKey(name: "nameEn")
  final String? nameEn;
  @override
  @JsonKey(name: "store")
  final String? store;
  @override
  @JsonKey(name: "image")
  final String? image;
  @override
  @JsonKey(name: "subCategoriesCount")
  final int? subCategoriesCount;
  final List<SubcategoryModel>? _subCategories;
  @override
  @JsonKey(name: "subCategories")
  List<SubcategoryModel>? get subCategories {
    final value = _subCategories;
    if (value == null) return null;
    if (_subCategories is EqualUnmodifiableListView) return _subCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, nameAr: $nameAr, nameEn: $nameEn, store: $store, image: $image, subCategoriesCount: $subCategoriesCount, subCategories: $subCategories)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CategoryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.store, store) || other.store == store) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.subCategoriesCount, subCategoriesCount) ||
                other.subCategoriesCount == subCategoriesCount) &&
            const DeepCollectionEquality()
                .equals(other._subCategories, _subCategories));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, nameAr, nameEn, store, image,
      subCategoriesCount, const DeepCollectionEquality().hash(_subCategories));

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CategoryModelImplCopyWith<_$CategoryModelImpl> get copyWith =>
      __$$CategoryModelImplCopyWithImpl<_$CategoryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CategoryModelImplToJson(
      this,
    );
  }
}

abstract class _CategoryModel implements CategoryModel {
  const factory _CategoryModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "nameAr") final String? nameAr,
      @JsonKey(name: "nameEn") final String? nameEn,
      @JsonKey(name: "store") final String? store,
      @JsonKey(name: "image") final String? image,
      @JsonKey(name: "subCategoriesCount") final int? subCategoriesCount,
      @JsonKey(name: "subCategories")
      final List<SubcategoryModel>? subCategories}) = _$CategoryModelImpl;

  factory _CategoryModel.fromJson(Map<String, dynamic> json) =
      _$CategoryModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "nameAr")
  String? get nameAr;
  @override
  @JsonKey(name: "nameEn")
  String? get nameEn;
  @override
  @JsonKey(name: "store")
  String? get store;
  @override
  @JsonKey(name: "image")
  String? get image;
  @override
  @JsonKey(name: "subCategoriesCount")
  int? get subCategoriesCount;
  @override
  @JsonKey(name: "subCategories")
  List<SubcategoryModel>? get subCategories;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CategoryModelImplCopyWith<_$CategoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SubcategoryModel _$SubcategoryModelFromJson(Map<String, dynamic> json) {
  return _SubcategoryModel.fromJson(json);
}

/// @nodoc
mixin _$SubcategoryModel {
  @JsonKey(name: "subCategoryId")
  int? get subCategoryId => throw _privateConstructorUsedError;
  @JsonKey(name: "subCategoryNameAr")
  String? get subCategoryNameAr => throw _privateConstructorUsedError;
  @JsonKey(name: "subCategoryNameEn")
  String? get subCategoryNameEn => throw _privateConstructorUsedError;
  @JsonKey(name: "isActive")
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: "orderNumber")
  int? get orderNumber => throw _privateConstructorUsedError;
  @JsonKey(name: "numberOfProducts")
  int? get numberOfProducts => throw _privateConstructorUsedError;

  /// Serializes this SubcategoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubcategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubcategoryModelCopyWith<SubcategoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubcategoryModelCopyWith<$Res> {
  factory $SubcategoryModelCopyWith(
          SubcategoryModel value, $Res Function(SubcategoryModel) then) =
      _$SubcategoryModelCopyWithImpl<$Res, SubcategoryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "subCategoryId") int? subCategoryId,
      @JsonKey(name: "subCategoryNameAr") String? subCategoryNameAr,
      @JsonKey(name: "subCategoryNameEn") String? subCategoryNameEn,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "orderNumber") int? orderNumber,
      @JsonKey(name: "numberOfProducts") int? numberOfProducts});
}

/// @nodoc
class _$SubcategoryModelCopyWithImpl<$Res, $Val extends SubcategoryModel>
    implements $SubcategoryModelCopyWith<$Res> {
  _$SubcategoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubcategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subCategoryId = freezed,
    Object? subCategoryNameAr = freezed,
    Object? subCategoryNameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
    Object? numberOfProducts = freezed,
  }) {
    return _then(_value.copyWith(
      subCategoryId: freezed == subCategoryId
          ? _value.subCategoryId
          : subCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategoryNameAr: freezed == subCategoryNameAr
          ? _value.subCategoryNameAr
          : subCategoryNameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      subCategoryNameEn: freezed == subCategoryNameEn
          ? _value.subCategoryNameEn
          : subCategoryNameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      numberOfProducts: freezed == numberOfProducts
          ? _value.numberOfProducts
          : numberOfProducts // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubcategoryModelImplCopyWith<$Res>
    implements $SubcategoryModelCopyWith<$Res> {
  factory _$$SubcategoryModelImplCopyWith(_$SubcategoryModelImpl value,
          $Res Function(_$SubcategoryModelImpl) then) =
      __$$SubcategoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "subCategoryId") int? subCategoryId,
      @JsonKey(name: "subCategoryNameAr") String? subCategoryNameAr,
      @JsonKey(name: "subCategoryNameEn") String? subCategoryNameEn,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "orderNumber") int? orderNumber,
      @JsonKey(name: "numberOfProducts") int? numberOfProducts});
}

/// @nodoc
class __$$SubcategoryModelImplCopyWithImpl<$Res>
    extends _$SubcategoryModelCopyWithImpl<$Res, _$SubcategoryModelImpl>
    implements _$$SubcategoryModelImplCopyWith<$Res> {
  __$$SubcategoryModelImplCopyWithImpl(_$SubcategoryModelImpl _value,
      $Res Function(_$SubcategoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubcategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subCategoryId = freezed,
    Object? subCategoryNameAr = freezed,
    Object? subCategoryNameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
    Object? numberOfProducts = freezed,
  }) {
    return _then(_$SubcategoryModelImpl(
      subCategoryId: freezed == subCategoryId
          ? _value.subCategoryId
          : subCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategoryNameAr: freezed == subCategoryNameAr
          ? _value.subCategoryNameAr
          : subCategoryNameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      subCategoryNameEn: freezed == subCategoryNameEn
          ? _value.subCategoryNameEn
          : subCategoryNameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      numberOfProducts: freezed == numberOfProducts
          ? _value.numberOfProducts
          : numberOfProducts // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubcategoryModelImpl implements _SubcategoryModel {
  const _$SubcategoryModelImpl(
      {@JsonKey(name: "subCategoryId") this.subCategoryId,
      @JsonKey(name: "subCategoryNameAr") this.subCategoryNameAr,
      @JsonKey(name: "subCategoryNameEn") this.subCategoryNameEn,
      @JsonKey(name: "isActive") this.isActive,
      @JsonKey(name: "orderNumber") this.orderNumber,
      @JsonKey(name: "numberOfProducts") this.numberOfProducts});

  factory _$SubcategoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubcategoryModelImplFromJson(json);

  @override
  @JsonKey(name: "subCategoryId")
  final int? subCategoryId;
  @override
  @JsonKey(name: "subCategoryNameAr")
  final String? subCategoryNameAr;
  @override
  @JsonKey(name: "subCategoryNameEn")
  final String? subCategoryNameEn;
  @override
  @JsonKey(name: "isActive")
  final bool? isActive;
  @override
  @JsonKey(name: "orderNumber")
  final int? orderNumber;
  @override
  @JsonKey(name: "numberOfProducts")
  final int? numberOfProducts;

  @override
  String toString() {
    return 'SubcategoryModel(subCategoryId: $subCategoryId, subCategoryNameAr: $subCategoryNameAr, subCategoryNameEn: $subCategoryNameEn, isActive: $isActive, orderNumber: $orderNumber, numberOfProducts: $numberOfProducts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubcategoryModelImpl &&
            (identical(other.subCategoryId, subCategoryId) ||
                other.subCategoryId == subCategoryId) &&
            (identical(other.subCategoryNameAr, subCategoryNameAr) ||
                other.subCategoryNameAr == subCategoryNameAr) &&
            (identical(other.subCategoryNameEn, subCategoryNameEn) ||
                other.subCategoryNameEn == subCategoryNameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.numberOfProducts, numberOfProducts) ||
                other.numberOfProducts == numberOfProducts));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, subCategoryId, subCategoryNameAr,
      subCategoryNameEn, isActive, orderNumber, numberOfProducts);

  /// Create a copy of SubcategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubcategoryModelImplCopyWith<_$SubcategoryModelImpl> get copyWith =>
      __$$SubcategoryModelImplCopyWithImpl<_$SubcategoryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubcategoryModelImplToJson(
      this,
    );
  }
}

abstract class _SubcategoryModel implements SubcategoryModel {
  const factory _SubcategoryModel(
          {@JsonKey(name: "subCategoryId") final int? subCategoryId,
          @JsonKey(name: "subCategoryNameAr") final String? subCategoryNameAr,
          @JsonKey(name: "subCategoryNameEn") final String? subCategoryNameEn,
          @JsonKey(name: "isActive") final bool? isActive,
          @JsonKey(name: "orderNumber") final int? orderNumber,
          @JsonKey(name: "numberOfProducts") final int? numberOfProducts}) =
      _$SubcategoryModelImpl;

  factory _SubcategoryModel.fromJson(Map<String, dynamic> json) =
      _$SubcategoryModelImpl.fromJson;

  @override
  @JsonKey(name: "subCategoryId")
  int? get subCategoryId;
  @override
  @JsonKey(name: "subCategoryNameAr")
  String? get subCategoryNameAr;
  @override
  @JsonKey(name: "subCategoryNameEn")
  String? get subCategoryNameEn;
  @override
  @JsonKey(name: "isActive")
  bool? get isActive;
  @override
  @JsonKey(name: "orderNumber")
  int? get orderNumber;
  @override
  @JsonKey(name: "numberOfProducts")
  int? get numberOfProducts;

  /// Create a copy of SubcategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubcategoryModelImplCopyWith<_$SubcategoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
