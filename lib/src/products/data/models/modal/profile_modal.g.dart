// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileModelImpl _$$ProfileModelImplFromJson(Map<String, dynamic> json) =>
    _$ProfileModelImpl(
      id: json['id'] as String?,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      isActive: json['isActive'] as bool?,
      image: json['image'] as String?,
      cover: json['cover'] as String?,
      address: json['address'] as String?,
      cityId: (json['cityId'] as num?)?.toInt(),
      lat: (json['lat'] as num?)?.toInt(),
      lng: (json['lng'] as num?)?.toInt(),
      appActivityId: (json['appActivityId'] as num?)?.toInt(),
      storeRate: json['storeRate'] as String?,
    );

Map<String, dynamic> _$$ProfileModelImplToJson(_$ProfileModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.phoneNumber case final value?) 'phoneNumber': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.image case final value?) 'image': value,
      if (instance.cover case final value?) 'cover': value,
      if (instance.address case final value?) 'address': value,
      if (instance.cityId case final value?) 'cityId': value,
      if (instance.lat case final value?) 'lat': value,
      if (instance.lng case final value?) 'lng': value,
      if (instance.appActivityId case final value?) 'appActivityId': value,
      if (instance.storeRate case final value?) 'storeRate': value,
    };
