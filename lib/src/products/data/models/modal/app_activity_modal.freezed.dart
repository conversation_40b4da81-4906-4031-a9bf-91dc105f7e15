// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_activity_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppActivityModel _$AppActivityModelFromJson(Map<String, dynamic> json) {
  return _AppActivityModel.fromJson(json);
}

/// @nodoc
mixin _$AppActivityModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;

  /// Serializes this AppActivityModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppActivityModelCopyWith<AppActivityModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppActivityModelCopyWith<$Res> {
  factory $AppActivityModelCopyWith(
          AppActivityModel value, $Res Function(AppActivityModel) then) =
      _$AppActivityModelCopyWithImpl<$Res, AppActivityModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id, @JsonKey(name: "name") String? name});
}

/// @nodoc
class _$AppActivityModelCopyWithImpl<$Res, $Val extends AppActivityModel>
    implements $AppActivityModelCopyWith<$Res> {
  _$AppActivityModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppActivityModelImplCopyWith<$Res>
    implements $AppActivityModelCopyWith<$Res> {
  factory _$$AppActivityModelImplCopyWith(_$AppActivityModelImpl value,
          $Res Function(_$AppActivityModelImpl) then) =
      __$$AppActivityModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id, @JsonKey(name: "name") String? name});
}

/// @nodoc
class __$$AppActivityModelImplCopyWithImpl<$Res>
    extends _$AppActivityModelCopyWithImpl<$Res, _$AppActivityModelImpl>
    implements _$$AppActivityModelImplCopyWith<$Res> {
  __$$AppActivityModelImplCopyWithImpl(_$AppActivityModelImpl _value,
      $Res Function(_$AppActivityModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
  }) {
    return _then(_$AppActivityModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppActivityModelImpl implements _AppActivityModel {
  const _$AppActivityModelImpl(
      {@JsonKey(name: "id") this.id, @JsonKey(name: "name") this.name});

  factory _$AppActivityModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppActivityModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;

  @override
  String toString() {
    return 'AppActivityModel(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppActivityModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name);

  /// Create a copy of AppActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppActivityModelImplCopyWith<_$AppActivityModelImpl> get copyWith =>
      __$$AppActivityModelImplCopyWithImpl<_$AppActivityModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppActivityModelImplToJson(
      this,
    );
  }
}

abstract class _AppActivityModel implements AppActivityModel {
  const factory _AppActivityModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "name") final String? name}) = _$AppActivityModelImpl;

  factory _AppActivityModel.fromJson(Map<String, dynamic> json) =
      _$AppActivityModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;

  /// Create a copy of AppActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppActivityModelImplCopyWith<_$AppActivityModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
