// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) {
  return _ProductModel.fromJson(json);
}

/// @nodoc
mixin _$ProductModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "description")
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: "imageUrl")
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "groups")
  List<GroupModel>? get groups => throw _privateConstructorUsedError;
  @JsonKey(name: "sliders")
  List<String>? get sliders => throw _privateConstructorUsedError;
  @JsonKey(name: "options")
  List<ProductOption>? get options => throw _privateConstructorUsedError;
  @JsonKey(name: "isAvailable")
  bool? get isAvailable => throw _privateConstructorUsedError;
  @JsonKey(name: "isActive")
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: "discountType")
  int? get discountType => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  int? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "discount")
  int? get discount => throw _privateConstructorUsedError;
  @JsonKey(name: "orderCount")
  int? get orderCount => throw _privateConstructorUsedError;
  @JsonKey(name: "quantityInCart")
  int? get quantityInCart => throw _privateConstructorUsedError;
  @JsonKey(name: "hasOptions")
  bool? get hasOptions => throw _privateConstructorUsedError;
  @JsonKey(name: "temporaryDisable")
  bool? get temporaryDisable => throw _privateConstructorUsedError;
  @JsonKey(name: "productOptionId")
  int? get productOptionId => throw _privateConstructorUsedError;
  @JsonKey(name: "isDealOfDay")
  bool? get isDealOfDay => throw _privateConstructorUsedError;
  @JsonKey(name: "isFavorite")
  bool? get isFavorite => throw _privateConstructorUsedError;
  @JsonKey(name: "createdAt")
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "priceAfterDiscount")
  int? get priceAfterDiscount => throw _privateConstructorUsedError;

  /// Serializes this ProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductModelCopyWith<ProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductModelCopyWith<$Res> {
  factory $ProductModelCopyWith(
          ProductModel value, $Res Function(ProductModel) then) =
      _$ProductModelCopyWithImpl<$Res, ProductModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "imageUrl") String? imageUrl,
      @JsonKey(name: "groups") List<GroupModel>? groups,
      @JsonKey(name: "sliders") List<String>? sliders,
      @JsonKey(name: "options") List<ProductOption>? options,
      @JsonKey(name: "isAvailable") bool? isAvailable,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "discountType") int? discountType,
      @JsonKey(name: "price") int? price,
      @JsonKey(name: "discount") int? discount,
      @JsonKey(name: "orderCount") int? orderCount,
      @JsonKey(name: "quantityInCart") int? quantityInCart,
      @JsonKey(name: "hasOptions") bool? hasOptions,
      @JsonKey(name: "temporaryDisable") bool? temporaryDisable,
      @JsonKey(name: "productOptionId") int? productOptionId,
      @JsonKey(name: "isDealOfDay") bool? isDealOfDay,
      @JsonKey(name: "isFavorite") bool? isFavorite,
      @JsonKey(name: "createdAt") String? createdAt,
      @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount});
}

/// @nodoc
class _$ProductModelCopyWithImpl<$Res, $Val extends ProductModel>
    implements $ProductModelCopyWith<$Res> {
  _$ProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? imageUrl = freezed,
    Object? groups = freezed,
    Object? sliders = freezed,
    Object? options = freezed,
    Object? isAvailable = freezed,
    Object? isActive = freezed,
    Object? discountType = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? orderCount = freezed,
    Object? quantityInCart = freezed,
    Object? hasOptions = freezed,
    Object? temporaryDisable = freezed,
    Object? productOptionId = freezed,
    Object? isDealOfDay = freezed,
    Object? isFavorite = freezed,
    Object? createdAt = freezed,
    Object? priceAfterDiscount = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      groups: freezed == groups
          ? _value.groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<GroupModel>?,
      sliders: freezed == sliders
          ? _value.sliders
          : sliders // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      options: freezed == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<ProductOption>?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      discountType: freezed == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderCount: freezed == orderCount
          ? _value.orderCount
          : orderCount // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityInCart: freezed == quantityInCart
          ? _value.quantityInCart
          : quantityInCart // ignore: cast_nullable_to_non_nullable
              as int?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      temporaryDisable: freezed == temporaryDisable
          ? _value.temporaryDisable
          : temporaryDisable // ignore: cast_nullable_to_non_nullable
              as bool?,
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      isDealOfDay: freezed == isDealOfDay
          ? _value.isDealOfDay
          : isDealOfDay // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFavorite: freezed == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductModelImplCopyWith<$Res>
    implements $ProductModelCopyWith<$Res> {
  factory _$$ProductModelImplCopyWith(
          _$ProductModelImpl value, $Res Function(_$ProductModelImpl) then) =
      __$$ProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "imageUrl") String? imageUrl,
      @JsonKey(name: "groups") List<GroupModel>? groups,
      @JsonKey(name: "sliders") List<String>? sliders,
      @JsonKey(name: "options") List<ProductOption>? options,
      @JsonKey(name: "isAvailable") bool? isAvailable,
      @JsonKey(name: "isActive") bool? isActive,
      @JsonKey(name: "discountType") int? discountType,
      @JsonKey(name: "price") int? price,
      @JsonKey(name: "discount") int? discount,
      @JsonKey(name: "orderCount") int? orderCount,
      @JsonKey(name: "quantityInCart") int? quantityInCart,
      @JsonKey(name: "hasOptions") bool? hasOptions,
      @JsonKey(name: "temporaryDisable") bool? temporaryDisable,
      @JsonKey(name: "productOptionId") int? productOptionId,
      @JsonKey(name: "isDealOfDay") bool? isDealOfDay,
      @JsonKey(name: "isFavorite") bool? isFavorite,
      @JsonKey(name: "createdAt") String? createdAt,
      @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount});
}

/// @nodoc
class __$$ProductModelImplCopyWithImpl<$Res>
    extends _$ProductModelCopyWithImpl<$Res, _$ProductModelImpl>
    implements _$$ProductModelImplCopyWith<$Res> {
  __$$ProductModelImplCopyWithImpl(
      _$ProductModelImpl _value, $Res Function(_$ProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? imageUrl = freezed,
    Object? groups = freezed,
    Object? sliders = freezed,
    Object? options = freezed,
    Object? isAvailable = freezed,
    Object? isActive = freezed,
    Object? discountType = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? orderCount = freezed,
    Object? quantityInCart = freezed,
    Object? hasOptions = freezed,
    Object? temporaryDisable = freezed,
    Object? productOptionId = freezed,
    Object? isDealOfDay = freezed,
    Object? isFavorite = freezed,
    Object? createdAt = freezed,
    Object? priceAfterDiscount = freezed,
  }) {
    return _then(_$ProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      groups: freezed == groups
          ? _value._groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<GroupModel>?,
      sliders: freezed == sliders
          ? _value._sliders
          : sliders // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      options: freezed == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<ProductOption>?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      discountType: freezed == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderCount: freezed == orderCount
          ? _value.orderCount
          : orderCount // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityInCart: freezed == quantityInCart
          ? _value.quantityInCart
          : quantityInCart // ignore: cast_nullable_to_non_nullable
              as int?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      temporaryDisable: freezed == temporaryDisable
          ? _value.temporaryDisable
          : temporaryDisable // ignore: cast_nullable_to_non_nullable
              as bool?,
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      isDealOfDay: freezed == isDealOfDay
          ? _value.isDealOfDay
          : isDealOfDay // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFavorite: freezed == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      priceAfterDiscount: freezed == priceAfterDiscount
          ? _value.priceAfterDiscount
          : priceAfterDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductModelImpl implements _ProductModel {
  const _$ProductModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "description") this.description,
      @JsonKey(name: "imageUrl") this.imageUrl,
      @JsonKey(name: "groups") final List<GroupModel>? groups,
      @JsonKey(name: "sliders") final List<String>? sliders,
      @JsonKey(name: "options") final List<ProductOption>? options,
      @JsonKey(name: "isAvailable") this.isAvailable,
      @JsonKey(name: "isActive") this.isActive,
      @JsonKey(name: "discountType") this.discountType,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "discount") this.discount,
      @JsonKey(name: "orderCount") this.orderCount,
      @JsonKey(name: "quantityInCart") this.quantityInCart,
      @JsonKey(name: "hasOptions") this.hasOptions,
      @JsonKey(name: "temporaryDisable") this.temporaryDisable,
      @JsonKey(name: "productOptionId") this.productOptionId,
      @JsonKey(name: "isDealOfDay") this.isDealOfDay,
      @JsonKey(name: "isFavorite") this.isFavorite,
      @JsonKey(name: "createdAt") this.createdAt,
      @JsonKey(name: "priceAfterDiscount") this.priceAfterDiscount})
      : _groups = groups,
        _sliders = sliders,
        _options = options;

  factory _$ProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "description")
  final String? description;
  @override
  @JsonKey(name: "imageUrl")
  final String? imageUrl;
  final List<GroupModel>? _groups;
  @override
  @JsonKey(name: "groups")
  List<GroupModel>? get groups {
    final value = _groups;
    if (value == null) return null;
    if (_groups is EqualUnmodifiableListView) return _groups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _sliders;
  @override
  @JsonKey(name: "sliders")
  List<String>? get sliders {
    final value = _sliders;
    if (value == null) return null;
    if (_sliders is EqualUnmodifiableListView) return _sliders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductOption>? _options;
  @override
  @JsonKey(name: "options")
  List<ProductOption>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "isAvailable")
  final bool? isAvailable;
  @override
  @JsonKey(name: "isActive")
  final bool? isActive;
  @override
  @JsonKey(name: "discountType")
  final int? discountType;
  @override
  @JsonKey(name: "price")
  final int? price;
  @override
  @JsonKey(name: "discount")
  final int? discount;
  @override
  @JsonKey(name: "orderCount")
  final int? orderCount;
  @override
  @JsonKey(name: "quantityInCart")
  final int? quantityInCart;
  @override
  @JsonKey(name: "hasOptions")
  final bool? hasOptions;
  @override
  @JsonKey(name: "temporaryDisable")
  final bool? temporaryDisable;
  @override
  @JsonKey(name: "productOptionId")
  final int? productOptionId;
  @override
  @JsonKey(name: "isDealOfDay")
  final bool? isDealOfDay;
  @override
  @JsonKey(name: "isFavorite")
  final bool? isFavorite;
  @override
  @JsonKey(name: "createdAt")
  final String? createdAt;
  @override
  @JsonKey(name: "priceAfterDiscount")
  final int? priceAfterDiscount;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, description: $description, imageUrl: $imageUrl, groups: $groups, sliders: $sliders, options: $options, isAvailable: $isAvailable, isActive: $isActive, discountType: $discountType, price: $price, discount: $discount, orderCount: $orderCount, quantityInCart: $quantityInCart, hasOptions: $hasOptions, temporaryDisable: $temporaryDisable, productOptionId: $productOptionId, isDealOfDay: $isDealOfDay, isFavorite: $isFavorite, createdAt: $createdAt, priceAfterDiscount: $priceAfterDiscount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._groups, _groups) &&
            const DeepCollectionEquality().equals(other._sliders, _sliders) &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.discountType, discountType) ||
                other.discountType == discountType) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.orderCount, orderCount) ||
                other.orderCount == orderCount) &&
            (identical(other.quantityInCart, quantityInCart) ||
                other.quantityInCart == quantityInCart) &&
            (identical(other.hasOptions, hasOptions) ||
                other.hasOptions == hasOptions) &&
            (identical(other.temporaryDisable, temporaryDisable) ||
                other.temporaryDisable == temporaryDisable) &&
            (identical(other.productOptionId, productOptionId) ||
                other.productOptionId == productOptionId) &&
            (identical(other.isDealOfDay, isDealOfDay) ||
                other.isDealOfDay == isDealOfDay) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.priceAfterDiscount, priceAfterDiscount) ||
                other.priceAfterDiscount == priceAfterDiscount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        description,
        imageUrl,
        const DeepCollectionEquality().hash(_groups),
        const DeepCollectionEquality().hash(_sliders),
        const DeepCollectionEquality().hash(_options),
        isAvailable,
        isActive,
        discountType,
        price,
        discount,
        orderCount,
        quantityInCart,
        hasOptions,
        temporaryDisable,
        productOptionId,
        isDealOfDay,
        isFavorite,
        createdAt,
        priceAfterDiscount
      ]);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      __$$ProductModelImplCopyWithImpl<_$ProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductModelImplToJson(
      this,
    );
  }
}

abstract class _ProductModel implements ProductModel {
  const factory _ProductModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "description") final String? description,
          @JsonKey(name: "imageUrl") final String? imageUrl,
          @JsonKey(name: "groups") final List<GroupModel>? groups,
          @JsonKey(name: "sliders") final List<String>? sliders,
          @JsonKey(name: "options") final List<ProductOption>? options,
          @JsonKey(name: "isAvailable") final bool? isAvailable,
          @JsonKey(name: "isActive") final bool? isActive,
          @JsonKey(name: "discountType") final int? discountType,
          @JsonKey(name: "price") final int? price,
          @JsonKey(name: "discount") final int? discount,
          @JsonKey(name: "orderCount") final int? orderCount,
          @JsonKey(name: "quantityInCart") final int? quantityInCart,
          @JsonKey(name: "hasOptions") final bool? hasOptions,
          @JsonKey(name: "temporaryDisable") final bool? temporaryDisable,
          @JsonKey(name: "productOptionId") final int? productOptionId,
          @JsonKey(name: "isDealOfDay") final bool? isDealOfDay,
          @JsonKey(name: "isFavorite") final bool? isFavorite,
          @JsonKey(name: "createdAt") final String? createdAt,
          @JsonKey(name: "priceAfterDiscount") final int? priceAfterDiscount}) =
      _$ProductModelImpl;

  factory _ProductModel.fromJson(Map<String, dynamic> json) =
      _$ProductModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "description")
  String? get description;
  @override
  @JsonKey(name: "imageUrl")
  String? get imageUrl;
  @override
  @JsonKey(name: "groups")
  List<GroupModel>? get groups;
  @override
  @JsonKey(name: "sliders")
  List<String>? get sliders;
  @override
  @JsonKey(name: "options")
  List<ProductOption>? get options;
  @override
  @JsonKey(name: "isAvailable")
  bool? get isAvailable;
  @override
  @JsonKey(name: "isActive")
  bool? get isActive;
  @override
  @JsonKey(name: "discountType")
  int? get discountType;
  @override
  @JsonKey(name: "price")
  int? get price;
  @override
  @JsonKey(name: "discount")
  int? get discount;
  @override
  @JsonKey(name: "orderCount")
  int? get orderCount;
  @override
  @JsonKey(name: "quantityInCart")
  int? get quantityInCart;
  @override
  @JsonKey(name: "hasOptions")
  bool? get hasOptions;
  @override
  @JsonKey(name: "temporaryDisable")
  bool? get temporaryDisable;
  @override
  @JsonKey(name: "productOptionId")
  int? get productOptionId;
  @override
  @JsonKey(name: "isDealOfDay")
  bool? get isDealOfDay;
  @override
  @JsonKey(name: "isFavorite")
  bool? get isFavorite;
  @override
  @JsonKey(name: "createdAt")
  String? get createdAt;
  @override
  @JsonKey(name: "priceAfterDiscount")
  int? get priceAfterDiscount;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
