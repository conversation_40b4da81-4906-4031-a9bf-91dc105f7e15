import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_modal.freezed.dart';
part 'profile_modal.g.dart';

ProfileModel profileModelFromJson(String str) =>
    ProfileModel.fromJson(json.decode(str));

String profileModelToJson(ProfileModel data) => json.encode(data.toJson());

@freezed
class ProfileModel with _$ProfileModel {
  const factory ProfileModel({
    @Json<PERSON><PERSON>(name: "id") String? id,
    @Json<PERSON>ey(name: "nameAr") String? nameAr,
    @Json<PERSON>ey(name: "nameEn") String? nameEn,
    @<PERSON><PERSON><PERSON><PERSON>(name: "phoneNumber") String? phoneNumber,
    @<PERSON><PERSON><PERSON><PERSON>(name: "isActive") bool? isActive,
    @Json<PERSON><PERSON>(name: "image") String? image,
    @Json<PERSON>ey(name: "cover") String? cover,
    @Json<PERSON>ey(name: "address") String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: "cityId") int? cityId,
    @<PERSON><PERSON><PERSON><PERSON>(name: "lat") int? lat,
    @<PERSON><PERSON><PERSON><PERSON>(name: "lng") int? lng,
    @<PERSON><PERSON><PERSON><PERSON>(name: "appActivityId") int? appActivityId,
    @JsonKey(name: "storeRate") String? storeRate,
  }) = _ProfileModel;

  factory ProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileModelFromJson(json);
}
