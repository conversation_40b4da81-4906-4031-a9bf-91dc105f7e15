import 'dart:convert';

import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_modal.freezed.dart';
part 'product_modal.g.dart';

ProductModel productModelFromJson(String str) =>
    ProductModel.fromJson(json.decode(str));

String productModelToJson(ProductModel data) => json.encode(data.toJson());

@freezed
class ProductModel with _$ProductModel {
  const factory ProductModel({
    @JsonKey(name: "id") int? id,
    @Json<PERSON>ey(name: "name") String? name,
    @JsonKey(name: "description") String? description,
    @JsonKey(name: "imageUrl") String? imageUrl,
    @JsonKey(name: "groups") List<GroupModel>? groups,
    @JsonKey(name: "sliders") List<String>? sliders,
    @JsonKey(name: "options") List<ProductOption>? options,
    @JsonKey(name: "isAvailable") bool? isAvailable,
    @JsonKey(name: "isActive") bool? isActive,
    @JsonKey(name: "discountType") int? discountType,
    @JsonKey(name: "price") int? price,
    @JsonKey(name: "discount") int? discount,
    @JsonKey(name: "orderCount") int? orderCount,
    @JsonKey(name: "quantityInCart") int? quantityInCart,
    @JsonKey(name: "hasOptions") bool? hasOptions,
    @JsonKey(name: "temporaryDisable") bool? temporaryDisable,
    @JsonKey(name: "productOptionId") int? productOptionId,
    @JsonKey(name: "isDealOfDay") bool? isDealOfDay,
    @JsonKey(name: "isFavorite") bool? isFavorite,
    @JsonKey(name: "createdAt") String? createdAt,
    @JsonKey(name: "priceAfterDiscount") int? priceAfterDiscount,
  }) = _ProductModel;

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
}
