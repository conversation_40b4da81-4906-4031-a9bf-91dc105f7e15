// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_option_modal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductOption _$ProductOptionFromJson(Map<String, dynamic> json) =>
    ProductOption(
      id: (json['id'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool?,
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toDouble(),
      discountType: (json['discountType'] as num?)?.toInt(),
      image: json['image'] as String?,
      optionDetails: (json['optionDetails'] as List<dynamic>?)
          ?.map((e) => ProductGroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      additions: (json['additions'] as List<dynamic>?)
          ?.map((e) => ProductAdditionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..quantity = (json['quantity'] as num?)?.toInt()
      ..discountExpiration = json['discountExpiration'] == null
          ? null
          : DateTime.parse(json['discountExpiration'] as String);

Map<String, dynamic> _$ProductOptionToJson(ProductOption instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.price case final value?) 'price': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.discountExpiration?.toIso8601String() case final value?)
        'discountExpiration': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.priceAfterDiscount case final value?)
        'priceAfterDiscount': value,
      if (instance.discountType case final value?) 'discountType': value,
      if (instance.image case final value?) 'image': value,
      if (instance.optionDetails case final value?) 'optionDetails': value,
      if (instance.additions case final value?) 'additions': value,
    };

_$ProductGroupModelImpl _$$ProductGroupModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductGroupModelImpl(
      groupId: (json['groupId'] as num?)?.toInt(),
      groupName: json['groupName'] as String?,
      options: (json['options'] as List<dynamic>?)
          ?.map((e) =>
              ProductGroupOptionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProductGroupModelImplToJson(
        _$ProductGroupModelImpl instance) =>
    <String, dynamic>{
      if (instance.groupId case final value?) 'groupId': value,
      if (instance.groupName case final value?) 'groupName': value,
      if (instance.options case final value?) 'options': value,
    };

_$ProductGroupOptionModelImpl _$$ProductGroupOptionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductGroupOptionModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$$ProductGroupOptionModelImplToJson(
        _$ProductGroupOptionModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.value case final value?) 'value': value,
    };

_$ProductAdditionModelImpl _$$ProductAdditionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductAdditionModelImpl(
      id: (json['id'] as num?)?.toInt(),
      additionId: (json['additionId'] as num?)?.toInt(),
      name: json['name'] as String?,
      optionName: json['optionName'] as String?,
      isActive: json['isActive'] as bool?,
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductAdditionModelImplToJson(
        _$ProductAdditionModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.additionId case final value?) 'additionId': value,
      if (instance.name case final value?) 'name': value,
      if (instance.optionName case final value?) 'optionName': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.priceAfterDiscount case final value?)
        'priceAfterDiscount': value,
    };
