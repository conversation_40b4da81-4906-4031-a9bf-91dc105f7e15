import 'package:alsarea_store/src/products/data/models/category_wrapper.dart';
import 'package:alsarea_store/src/products/data/models/modal/addition_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/app_activity_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/city_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_view_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/profile_modal.dart';
import 'package:alsarea_store/src/products/data/models/modal/subcategories_modal.dart';

class CategoryOperations {
  final dataWrapper = CategoryDataWrapper();
  final subDataWrapper = SubcategoryDataWrapper();
  final productsDataWrapper = ProductsDataWrapper();
  final productDataWrapper = ProductDataWrapper();
  final productOptionDataWrapper = ProductOptionDataWrapper();
  final productAdditionDataWrapper = ProductAdditionDataWrapper();
  final optionsDataWrapper = OptionDataWrapper();
  final profileDataWrapper = ProfileDataWrapper();
  final groupDataWrapper = GroupDataWrapper();
  final additionDataWrapper = AdditionDataWrapper();

//Get profile
  ProfileModel profile = const ProfileModel();
  List<CityModel> cities = [];
  List<AppActivityModel> appActivities = [];
  int ordersCount = 0;

//Get Categories
  int currentPage = 1;
  int totalPages = 1;
  int? skip;
  List<CategoryModel> categories = [];

  //Get Category & Add Category & Edit Category
  int? id;

  CategoryModel category = const CategoryModel();

  String? nameAr;
  String? nameEn;
  String? image;
  bool? isActive = true;
  int? orderNumber = 1;

  //Get SubCategories

  List<SubcategoryModel> subCategories = [];

  //Get SubCategory & Add SubCategory & Edit SubCategory
  int? subId;

  SubcategoryModel subcategory = const SubcategoryModel();

  String? nameArSubcategory;
  String? nameEnSubcategory;
  bool? isActiveSubcategory = true;
  int? orderNumberSubcategory = 1;
  int? numberOfProducts;

  //Get SubCategoriesList
  List<SubcategoriesModel> subCategoriesList = [];

  //Get Additions

  List<AdditionModel> additionsList = [];
  List<GroupModel> groupsList = [];

  //Get Products
  int productsCurrentPage = 1;
  int productsTotalPages = 1;
  int? productsSkip;
  List<ProductModel> products = [];
  ProductModel? product;
  List<ProductOption> productOptions = [];
  List<ProductAdditionModel> productAdditions = [];

  List<List<int>> selectedOptionsPerGroup = [];
  List<List<int>> generatedProductsOptionsIds = [];
  List<ProductOptionView> productOptionsView = [];

  void updateGroupWithOption(GroupModel group, OptionModel option) {
    final groupIndex = groupsList.indexWhere((g) => g.id == group.id);
    if (groupIndex != -1) {
      final updatedOptions =
          List<OptionModel>.from(groupsList[groupIndex].options ?? []);
      updatedOptions.add(option);
      groupsList[groupIndex] =
          groupsList[groupIndex].copyWith(options: updatedOptions);
    }
  }
}
