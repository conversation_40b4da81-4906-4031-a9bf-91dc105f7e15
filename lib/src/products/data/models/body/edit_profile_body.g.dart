// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_profile_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditProfileBodyImpl _$$EditProfileBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$EditProfileBodyImpl(
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool?,
      cityId: (json['cityId'] as num?)?.toInt(),
      appActivityId: (json['appActivityId'] as num?)?.toInt(),
      lat: (json['lat'] as num?)?.toInt(),
      lng: (json['lng'] as num?)?.toInt(),
      logo: json['logo'] as String?,
      cover: json['cover'] as String?,
    );

Map<String, dynamic> _$$EditProfileBodyImplToJson(
        _$EditProfileBodyImpl instance) =>
    <String, dynamic>{
      if (instance.phone case final value?) 'phone': value,
      if (instance.address case final value?) 'address': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.cityId case final value?) 'cityId': value,
      if (instance.appActivityId case final value?) 'appActivityId': value,
      if (instance.lat case final value?) 'lat': value,
      if (instance.lng case final value?) 'lng': value,
      if (instance.logo case final value?) 'logo': value,
      if (instance.cover case final value?) 'cover': value,
    };
