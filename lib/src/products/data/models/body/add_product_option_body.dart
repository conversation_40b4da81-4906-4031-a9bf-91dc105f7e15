import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_product_option_body.freezed.dart';
part 'add_product_option_body.g.dart';

@freezed
class AddProductOptionBody with _$AddProductOptionBody {
  const factory AddProductOptionBody({
    int? productId,
    List<int>? optionIds,
    int? quantity,
    double? price,
    int? discount,
    int? type,
    bool? isActive,
    String? image,
    String? discountExpiration,
  }) = _AddProductOptionBody;

  factory AddProductOptionBody.fromJson(Map<String, dynamic> json) =>
      _$AddProductOptionBodyFromJson(json);
}
