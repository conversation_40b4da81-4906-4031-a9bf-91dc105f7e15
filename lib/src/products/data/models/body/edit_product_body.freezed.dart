// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_product_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditProductBody _$EditProductBodyFromJson(Map<String, dynamic> json) {
  return _EditProductBody.fromJson(json);
}

/// @nodoc
mixin _$EditProductBody {
  int? get productOptionId => throw _privateConstructorUsedError;
  bool? get hasOptions => throw _privateConstructorUsedError;
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  String? get descriptionAr => throw _privateConstructorUsedError;
  String? get descriptionEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  bool? get isAvailable => throw _privateConstructorUsedError;
  DateTime? get discountExpiration => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  double? get discount => throw _privateConstructorUsedError;

  /// Serializes this EditProductBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditProductBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditProductBodyCopyWith<EditProductBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditProductBodyCopyWith<$Res> {
  factory $EditProductBodyCopyWith(
          EditProductBody value, $Res Function(EditProductBody) then) =
      _$EditProductBodyCopyWithImpl<$Res, EditProductBody>;
  @useResult
  $Res call(
      {int? productOptionId,
      bool? hasOptions,
      String? nameAr,
      String? nameEn,
      String? descriptionAr,
      String? descriptionEn,
      bool? isActive,
      bool? isAvailable,
      DateTime? discountExpiration,
      double? price,
      double? discount});
}

/// @nodoc
class _$EditProductBodyCopyWithImpl<$Res, $Val extends EditProductBody>
    implements $EditProductBodyCopyWith<$Res> {
  _$EditProductBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditProductBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOptionId = freezed,
    Object? hasOptions = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionEn = freezed,
    Object? isActive = freezed,
    Object? isAvailable = freezed,
    Object? discountExpiration = freezed,
    Object? price = freezed,
    Object? discount = freezed,
  }) {
    return _then(_value.copyWith(
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _value.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionEn: freezed == descriptionEn
          ? _value.descriptionEn
          : descriptionEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      discountExpiration: freezed == discountExpiration
          ? _value.discountExpiration
          : discountExpiration // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditProductBodyImplCopyWith<$Res>
    implements $EditProductBodyCopyWith<$Res> {
  factory _$$EditProductBodyImplCopyWith(_$EditProductBodyImpl value,
          $Res Function(_$EditProductBodyImpl) then) =
      __$$EditProductBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? productOptionId,
      bool? hasOptions,
      String? nameAr,
      String? nameEn,
      String? descriptionAr,
      String? descriptionEn,
      bool? isActive,
      bool? isAvailable,
      DateTime? discountExpiration,
      double? price,
      double? discount});
}

/// @nodoc
class __$$EditProductBodyImplCopyWithImpl<$Res>
    extends _$EditProductBodyCopyWithImpl<$Res, _$EditProductBodyImpl>
    implements _$$EditProductBodyImplCopyWith<$Res> {
  __$$EditProductBodyImplCopyWithImpl(
      _$EditProductBodyImpl _value, $Res Function(_$EditProductBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditProductBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productOptionId = freezed,
    Object? hasOptions = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionEn = freezed,
    Object? isActive = freezed,
    Object? isAvailable = freezed,
    Object? discountExpiration = freezed,
    Object? price = freezed,
    Object? discount = freezed,
  }) {
    return _then(_$EditProductBodyImpl(
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _value.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionEn: freezed == descriptionEn
          ? _value.descriptionEn
          : descriptionEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      discountExpiration: freezed == discountExpiration
          ? _value.discountExpiration
          : discountExpiration // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditProductBodyImpl implements _EditProductBody {
  const _$EditProductBodyImpl(
      {this.productOptionId,
      this.hasOptions,
      this.nameAr,
      this.nameEn,
      this.descriptionAr,
      this.descriptionEn,
      this.isActive,
      this.isAvailable,
      this.discountExpiration,
      this.price,
      this.discount});

  factory _$EditProductBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditProductBodyImplFromJson(json);

  @override
  final int? productOptionId;
  @override
  final bool? hasOptions;
  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final String? descriptionAr;
  @override
  final String? descriptionEn;
  @override
  final bool? isActive;
  @override
  final bool? isAvailable;
  @override
  final DateTime? discountExpiration;
  @override
  final double? price;
  @override
  final double? discount;

  @override
  String toString() {
    return 'EditProductBody(productOptionId: $productOptionId, hasOptions: $hasOptions, nameAr: $nameAr, nameEn: $nameEn, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, isActive: $isActive, isAvailable: $isAvailable, discountExpiration: $discountExpiration, price: $price, discount: $discount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditProductBodyImpl &&
            (identical(other.productOptionId, productOptionId) ||
                other.productOptionId == productOptionId) &&
            (identical(other.hasOptions, hasOptions) ||
                other.hasOptions == hasOptions) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.descriptionAr, descriptionAr) ||
                other.descriptionAr == descriptionAr) &&
            (identical(other.descriptionEn, descriptionEn) ||
                other.descriptionEn == descriptionEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.discountExpiration, discountExpiration) ||
                other.discountExpiration == discountExpiration) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discount, discount) ||
                other.discount == discount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productOptionId,
      hasOptions,
      nameAr,
      nameEn,
      descriptionAr,
      descriptionEn,
      isActive,
      isAvailable,
      discountExpiration,
      price,
      discount);

  /// Create a copy of EditProductBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditProductBodyImplCopyWith<_$EditProductBodyImpl> get copyWith =>
      __$$EditProductBodyImplCopyWithImpl<_$EditProductBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditProductBodyImplToJson(
      this,
    );
  }
}

abstract class _EditProductBody implements EditProductBody {
  const factory _EditProductBody(
      {final int? productOptionId,
      final bool? hasOptions,
      final String? nameAr,
      final String? nameEn,
      final String? descriptionAr,
      final String? descriptionEn,
      final bool? isActive,
      final bool? isAvailable,
      final DateTime? discountExpiration,
      final double? price,
      final double? discount}) = _$EditProductBodyImpl;

  factory _EditProductBody.fromJson(Map<String, dynamic> json) =
      _$EditProductBodyImpl.fromJson;

  @override
  int? get productOptionId;
  @override
  bool? get hasOptions;
  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  String? get descriptionAr;
  @override
  String? get descriptionEn;
  @override
  bool? get isActive;
  @override
  bool? get isAvailable;
  @override
  DateTime? get discountExpiration;
  @override
  double? get price;
  @override
  double? get discount;

  /// Create a copy of EditProductBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditProductBodyImplCopyWith<_$EditProductBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
