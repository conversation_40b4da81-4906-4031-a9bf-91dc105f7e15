import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_option_body.freezed.dart';
part 'create_option_body.g.dart';

@freezed
class CreateOptionBody with _$CreateOptionBody {
  const factory CreateOptionBody({
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? groupId,
    int? id,
  }) = _CreateOptionBody;

  factory CreateOptionBody.fromJson(Map<String, dynamic> json) =>
      _$CreateOptionBodyFromJson(json);
}
