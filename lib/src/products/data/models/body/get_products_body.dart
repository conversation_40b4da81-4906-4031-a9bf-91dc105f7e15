import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_products_body.freezed.dart';
part 'get_products_body.g.dart';

@freezed
class GetProductsBody with _$GetProductsBody {
  const factory GetProductsBody({
    String? search,
    String? sales,
    String? byDate,
    String? discount,
    int? categoryId,
    int? subCategoryId,
  }) = _GetProductsBody;

  factory GetProductsBody.fromJson(Map<String, dynamic> json) =>
      _$GetProductsBodyFromJson(json);
}
