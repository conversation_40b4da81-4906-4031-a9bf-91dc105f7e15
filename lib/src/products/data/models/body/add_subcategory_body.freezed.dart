// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_subcategory_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddSubcategoryBody _$AddSubcategoryBodyFromJson(Map<String, dynamic> json) {
  return _AddSubcategoryBody.fromJson(json);
}

/// @nodoc
mixin _$AddSubcategoryBody {
  int? get parentId => throw _privateConstructorUsedError;
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get orderNumber => throw _privateConstructorUsedError;

  /// Serializes this AddSubcategoryBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddSubcategoryBodyCopyWith<AddSubcategoryBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddSubcategoryBodyCopyWith<$Res> {
  factory $AddSubcategoryBodyCopyWith(
          AddSubcategoryBody value, $Res Function(AddSubcategoryBody) then) =
      _$AddSubcategoryBodyCopyWithImpl<$Res, AddSubcategoryBody>;
  @useResult
  $Res call(
      {int? parentId,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class _$AddSubcategoryBodyCopyWithImpl<$Res, $Val extends AddSubcategoryBody>
    implements $AddSubcategoryBodyCopyWith<$Res> {
  _$AddSubcategoryBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? parentId = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_value.copyWith(
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddSubcategoryBodyImplCopyWith<$Res>
    implements $AddSubcategoryBodyCopyWith<$Res> {
  factory _$$AddSubcategoryBodyImplCopyWith(_$AddSubcategoryBodyImpl value,
          $Res Function(_$AddSubcategoryBodyImpl) then) =
      __$$AddSubcategoryBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? parentId,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class __$$AddSubcategoryBodyImplCopyWithImpl<$Res>
    extends _$AddSubcategoryBodyCopyWithImpl<$Res, _$AddSubcategoryBodyImpl>
    implements _$$AddSubcategoryBodyImplCopyWith<$Res> {
  __$$AddSubcategoryBodyImplCopyWithImpl(_$AddSubcategoryBodyImpl _value,
      $Res Function(_$AddSubcategoryBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? parentId = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_$AddSubcategoryBodyImpl(
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddSubcategoryBodyImpl implements _AddSubcategoryBody {
  const _$AddSubcategoryBodyImpl(
      {this.parentId,
      this.nameAr,
      this.nameEn,
      this.isActive,
      this.orderNumber});

  factory _$AddSubcategoryBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddSubcategoryBodyImplFromJson(json);

  @override
  final int? parentId;
  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? orderNumber;

  @override
  String toString() {
    return 'AddSubcategoryBody(parentId: $parentId, nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, orderNumber: $orderNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddSubcategoryBodyImpl &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, parentId, nameAr, nameEn, isActive, orderNumber);

  /// Create a copy of AddSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddSubcategoryBodyImplCopyWith<_$AddSubcategoryBodyImpl> get copyWith =>
      __$$AddSubcategoryBodyImplCopyWithImpl<_$AddSubcategoryBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddSubcategoryBodyImplToJson(
      this,
    );
  }
}

abstract class _AddSubcategoryBody implements AddSubcategoryBody {
  const factory _AddSubcategoryBody(
      {final int? parentId,
      final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? orderNumber}) = _$AddSubcategoryBodyImpl;

  factory _AddSubcategoryBody.fromJson(Map<String, dynamic> json) =
      _$AddSubcategoryBodyImpl.fromJson;

  @override
  int? get parentId;
  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get orderNumber;

  /// Create a copy of AddSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddSubcategoryBodyImplCopyWith<_$AddSubcategoryBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
