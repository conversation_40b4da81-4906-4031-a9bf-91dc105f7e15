// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'categories_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CategoriesBody _$CategoriesBodyFromJson(Map<String, dynamic> json) {
  return _CategoriesBody.fromJson(json);
}

/// @nodoc
mixin _$CategoriesBody {
  int? get skip => throw _privateConstructorUsedError;
  int? get take => throw _privateConstructorUsedError;

  /// Serializes this CategoriesBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CategoriesBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CategoriesBodyCopyWith<CategoriesBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CategoriesBodyCopyWith<$Res> {
  factory $CategoriesBodyCopyWith(
          CategoriesBody value, $Res Function(CategoriesBody) then) =
      _$CategoriesBodyCopyWithImpl<$Res, CategoriesBody>;
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class _$CategoriesBodyCopyWithImpl<$Res, $Val extends CategoriesBody>
    implements $CategoriesBodyCopyWith<$Res> {
  _$CategoriesBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CategoriesBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_value.copyWith(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CategoriesBodyImplCopyWith<$Res>
    implements $CategoriesBodyCopyWith<$Res> {
  factory _$$CategoriesBodyImplCopyWith(_$CategoriesBodyImpl value,
          $Res Function(_$CategoriesBodyImpl) then) =
      __$$CategoriesBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class __$$CategoriesBodyImplCopyWithImpl<$Res>
    extends _$CategoriesBodyCopyWithImpl<$Res, _$CategoriesBodyImpl>
    implements _$$CategoriesBodyImplCopyWith<$Res> {
  __$$CategoriesBodyImplCopyWithImpl(
      _$CategoriesBodyImpl _value, $Res Function(_$CategoriesBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of CategoriesBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_$CategoriesBodyImpl(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CategoriesBodyImpl implements _CategoriesBody {
  const _$CategoriesBodyImpl({this.skip, this.take});

  factory _$CategoriesBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CategoriesBodyImplFromJson(json);

  @override
  final int? skip;
  @override
  final int? take;

  @override
  String toString() {
    return 'CategoriesBody(skip: $skip, take: $take)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CategoriesBodyImpl &&
            (identical(other.skip, skip) || other.skip == skip) &&
            (identical(other.take, take) || other.take == take));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, skip, take);

  /// Create a copy of CategoriesBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CategoriesBodyImplCopyWith<_$CategoriesBodyImpl> get copyWith =>
      __$$CategoriesBodyImplCopyWithImpl<_$CategoriesBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CategoriesBodyImplToJson(
      this,
    );
  }
}

abstract class _CategoriesBody implements CategoriesBody {
  const factory _CategoriesBody({final int? skip, final int? take}) =
      _$CategoriesBodyImpl;

  factory _CategoriesBody.fromJson(Map<String, dynamic> json) =
      _$CategoriesBodyImpl.fromJson;

  @override
  int? get skip;
  @override
  int? get take;

  /// Create a copy of CategoriesBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CategoriesBodyImplCopyWith<_$CategoriesBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
