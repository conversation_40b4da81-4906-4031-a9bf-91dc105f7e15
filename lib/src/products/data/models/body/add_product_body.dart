import 'package:alsarea_store/src/products/data/models/modal/product_option_view_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_product_body.freezed.dart';
part 'add_product_body.g.dart';

@freezed
class AddProductBody with _$AddProductBody {
  const factory AddProductBody({
    String? nameAr,
    String? nameEn,
    String? descriptionAr,
    String? descriptionEn,
    String? image,
    bool? isActive,
    bool? hasOptions,
    List<int>? categoriesId,
    List<String>? covers,
    bool? isForPoints,
    bool? hasOffer,
    bool? isAvailable,
    bool? showOnMainPage,
    List<ProductOptionView>? productOptions,
  }) = _AddProductBody;

  factory AddProductBody.fromJson(Map<String, dynamic> json) =>
      _$AddProductBodyFromJson(json);
}
