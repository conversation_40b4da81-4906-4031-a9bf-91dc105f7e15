// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_group_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditGroupBodyImpl _$$EditGroupBodyImplFromJson(Map<String, dynamic> json) =>
    _$EditGroupBodyImpl(
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool?,
      groupType: (json['groupType'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$EditGroupBodyImplToJson(_$EditGroupBodyImpl instance) =>
    <String, dynamic>{
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.groupType case final value?) 'groupType': value,
    };
