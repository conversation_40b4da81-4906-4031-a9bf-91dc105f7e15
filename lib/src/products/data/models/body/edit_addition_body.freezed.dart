// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_addition_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditAdditionBody _$EditAdditionBodyFromJson(Map<String, dynamic> json) {
  return _EditAdditionBody.fromJson(json);
}

/// @nodoc
mixin _$EditAdditionBody {
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get defaultPrice => throw _privateConstructorUsedError;

  /// Serializes this EditAdditionBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditAdditionBodyCopyWith<EditAdditionBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditAdditionBodyCopyWith<$Res> {
  factory $EditAdditionBodyCopyWith(
          EditAdditionBody value, $Res Function(EditAdditionBody) then) =
      _$EditAdditionBodyCopyWithImpl<$Res, EditAdditionBody>;
  @useResult
  $Res call(
      {String? nameAr, String? nameEn, bool? isActive, int? defaultPrice});
}

/// @nodoc
class _$EditAdditionBodyCopyWithImpl<$Res, $Val extends EditAdditionBody>
    implements $EditAdditionBodyCopyWith<$Res> {
  _$EditAdditionBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? defaultPrice = freezed,
  }) {
    return _then(_value.copyWith(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      defaultPrice: freezed == defaultPrice
          ? _value.defaultPrice
          : defaultPrice // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditAdditionBodyImplCopyWith<$Res>
    implements $EditAdditionBodyCopyWith<$Res> {
  factory _$$EditAdditionBodyImplCopyWith(_$EditAdditionBodyImpl value,
          $Res Function(_$EditAdditionBodyImpl) then) =
      __$$EditAdditionBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nameAr, String? nameEn, bool? isActive, int? defaultPrice});
}

/// @nodoc
class __$$EditAdditionBodyImplCopyWithImpl<$Res>
    extends _$EditAdditionBodyCopyWithImpl<$Res, _$EditAdditionBodyImpl>
    implements _$$EditAdditionBodyImplCopyWith<$Res> {
  __$$EditAdditionBodyImplCopyWithImpl(_$EditAdditionBodyImpl _value,
      $Res Function(_$EditAdditionBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? defaultPrice = freezed,
  }) {
    return _then(_$EditAdditionBodyImpl(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      defaultPrice: freezed == defaultPrice
          ? _value.defaultPrice
          : defaultPrice // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditAdditionBodyImpl implements _EditAdditionBody {
  const _$EditAdditionBodyImpl(
      {this.nameAr, this.nameEn, this.isActive, this.defaultPrice});

  factory _$EditAdditionBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditAdditionBodyImplFromJson(json);

  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? defaultPrice;

  @override
  String toString() {
    return 'EditAdditionBody(nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, defaultPrice: $defaultPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditAdditionBodyImpl &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.defaultPrice, defaultPrice) ||
                other.defaultPrice == defaultPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, nameAr, nameEn, isActive, defaultPrice);

  /// Create a copy of EditAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditAdditionBodyImplCopyWith<_$EditAdditionBodyImpl> get copyWith =>
      __$$EditAdditionBodyImplCopyWithImpl<_$EditAdditionBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditAdditionBodyImplToJson(
      this,
    );
  }
}

abstract class _EditAdditionBody implements EditAdditionBody {
  const factory _EditAdditionBody(
      {final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? defaultPrice}) = _$EditAdditionBodyImpl;

  factory _EditAdditionBody.fromJson(Map<String, dynamic> json) =
      _$EditAdditionBodyImpl.fromJson;

  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get defaultPrice;

  /// Create a copy of EditAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditAdditionBodyImplCopyWith<_$EditAdditionBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
