// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_profile_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditProfileBody _$EditProfileBodyFromJson(Map<String, dynamic> json) {
  return _EditProfileBody.fromJson(json);
}

/// @nodoc
mixin _$EditProfileBody {
  String? get phone => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get cityId => throw _privateConstructorUsedError;
  int? get appActivityId => throw _privateConstructorUsedError;
  int? get lat => throw _privateConstructorUsedError;
  int? get lng => throw _privateConstructorUsedError;
  String? get logo => throw _privateConstructorUsedError;
  String? get cover => throw _privateConstructorUsedError;

  /// Serializes this EditProfileBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditProfileBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditProfileBodyCopyWith<EditProfileBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditProfileBodyCopyWith<$Res> {
  factory $EditProfileBodyCopyWith(
          EditProfileBody value, $Res Function(EditProfileBody) then) =
      _$EditProfileBodyCopyWithImpl<$Res, EditProfileBody>;
  @useResult
  $Res call(
      {String? phone,
      String? address,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? cityId,
      int? appActivityId,
      int? lat,
      int? lng,
      String? logo,
      String? cover});
}

/// @nodoc
class _$EditProfileBodyCopyWithImpl<$Res, $Val extends EditProfileBody>
    implements $EditProfileBodyCopyWith<$Res> {
  _$EditProfileBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditProfileBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = freezed,
    Object? address = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? cityId = freezed,
    Object? appActivityId = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? logo = freezed,
    Object? cover = freezed,
  }) {
    return _then(_value.copyWith(
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      appActivityId: freezed == appActivityId
          ? _value.appActivityId
          : appActivityId // ignore: cast_nullable_to_non_nullable
              as int?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as int?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as int?,
      logo: freezed == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditProfileBodyImplCopyWith<$Res>
    implements $EditProfileBodyCopyWith<$Res> {
  factory _$$EditProfileBodyImplCopyWith(_$EditProfileBodyImpl value,
          $Res Function(_$EditProfileBodyImpl) then) =
      __$$EditProfileBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? phone,
      String? address,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? cityId,
      int? appActivityId,
      int? lat,
      int? lng,
      String? logo,
      String? cover});
}

/// @nodoc
class __$$EditProfileBodyImplCopyWithImpl<$Res>
    extends _$EditProfileBodyCopyWithImpl<$Res, _$EditProfileBodyImpl>
    implements _$$EditProfileBodyImplCopyWith<$Res> {
  __$$EditProfileBodyImplCopyWithImpl(
      _$EditProfileBodyImpl _value, $Res Function(_$EditProfileBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditProfileBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = freezed,
    Object? address = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? cityId = freezed,
    Object? appActivityId = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? logo = freezed,
    Object? cover = freezed,
  }) {
    return _then(_$EditProfileBodyImpl(
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      appActivityId: freezed == appActivityId
          ? _value.appActivityId
          : appActivityId // ignore: cast_nullable_to_non_nullable
              as int?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as int?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as int?,
      logo: freezed == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditProfileBodyImpl implements _EditProfileBody {
  const _$EditProfileBodyImpl(
      {this.phone,
      this.address,
      this.nameAr,
      this.nameEn,
      this.isActive,
      this.cityId,
      this.appActivityId,
      this.lat,
      this.lng,
      this.logo,
      this.cover});

  factory _$EditProfileBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditProfileBodyImplFromJson(json);

  @override
  final String? phone;
  @override
  final String? address;
  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? cityId;
  @override
  final int? appActivityId;
  @override
  final int? lat;
  @override
  final int? lng;
  @override
  final String? logo;
  @override
  final String? cover;

  @override
  String toString() {
    return 'EditProfileBody(phone: $phone, address: $address, nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, cityId: $cityId, appActivityId: $appActivityId, lat: $lat, lng: $lng, logo: $logo, cover: $cover)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditProfileBodyImpl &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.appActivityId, appActivityId) ||
                other.appActivityId == appActivityId) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.cover, cover) || other.cover == cover));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phone, address, nameAr, nameEn,
      isActive, cityId, appActivityId, lat, lng, logo, cover);

  /// Create a copy of EditProfileBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditProfileBodyImplCopyWith<_$EditProfileBodyImpl> get copyWith =>
      __$$EditProfileBodyImplCopyWithImpl<_$EditProfileBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditProfileBodyImplToJson(
      this,
    );
  }
}

abstract class _EditProfileBody implements EditProfileBody {
  const factory _EditProfileBody(
      {final String? phone,
      final String? address,
      final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? cityId,
      final int? appActivityId,
      final int? lat,
      final int? lng,
      final String? logo,
      final String? cover}) = _$EditProfileBodyImpl;

  factory _EditProfileBody.fromJson(Map<String, dynamic> json) =
      _$EditProfileBodyImpl.fromJson;

  @override
  String? get phone;
  @override
  String? get address;
  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get cityId;
  @override
  int? get appActivityId;
  @override
  int? get lat;
  @override
  int? get lng;
  @override
  String? get logo;
  @override
  String? get cover;

  /// Create a copy of EditProfileBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditProfileBodyImplCopyWith<_$EditProfileBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
