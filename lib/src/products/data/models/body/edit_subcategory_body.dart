import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_subcategory_body.freezed.dart';
part 'edit_subcategory_body.g.dart';

@freezed
class EditSubcategoryBody with _$EditSubcategoryBody {
  const factory EditSubcategoryBody({
    int? id,
    int? parentId,
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? orderNumber,
  }) = _EditSubcategoryBody;

  factory EditSubcategoryBody.fromJson(Map<String, dynamic> json) =>
      _$EditSubcategoryBodyFromJson(json);
}
