// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_category_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddCategoryBody _$AddCategoryBodyFromJson(Map<String, dynamic> json) {
  return _AddCategoryBody.fromJson(json);
}

/// @nodoc
mixin _$AddCategoryBody {
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get orderNumber => throw _privateConstructorUsedError;

  /// Serializes this AddCategoryBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddCategoryBodyCopyWith<AddCategoryBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddCategoryBodyCopyWith<$Res> {
  factory $AddCategoryBodyCopyWith(
          AddCategoryBody value, $Res Function(AddCategoryBody) then) =
      _$AddCategoryBodyCopyWithImpl<$Res, AddCategoryBody>;
  @useResult
  $Res call(
      {String? nameAr,
      String? nameEn,
      String? image,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class _$AddCategoryBodyCopyWithImpl<$Res, $Val extends AddCategoryBody>
    implements $AddCategoryBodyCopyWith<$Res> {
  _$AddCategoryBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_value.copyWith(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddCategoryBodyImplCopyWith<$Res>
    implements $AddCategoryBodyCopyWith<$Res> {
  factory _$$AddCategoryBodyImplCopyWith(_$AddCategoryBodyImpl value,
          $Res Function(_$AddCategoryBodyImpl) then) =
      __$$AddCategoryBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nameAr,
      String? nameEn,
      String? image,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class __$$AddCategoryBodyImplCopyWithImpl<$Res>
    extends _$AddCategoryBodyCopyWithImpl<$Res, _$AddCategoryBodyImpl>
    implements _$$AddCategoryBodyImplCopyWith<$Res> {
  __$$AddCategoryBodyImplCopyWithImpl(
      _$AddCategoryBodyImpl _value, $Res Function(_$AddCategoryBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_$AddCategoryBodyImpl(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddCategoryBodyImpl implements _AddCategoryBody {
  const _$AddCategoryBodyImpl(
      {this.nameAr, this.nameEn, this.image, this.isActive, this.orderNumber});

  factory _$AddCategoryBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddCategoryBodyImplFromJson(json);

  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final String? image;
  @override
  final bool? isActive;
  @override
  final int? orderNumber;

  @override
  String toString() {
    return 'AddCategoryBody(nameAr: $nameAr, nameEn: $nameEn, image: $image, isActive: $isActive, orderNumber: $orderNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddCategoryBodyImpl &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, nameAr, nameEn, image, isActive, orderNumber);

  /// Create a copy of AddCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddCategoryBodyImplCopyWith<_$AddCategoryBodyImpl> get copyWith =>
      __$$AddCategoryBodyImplCopyWithImpl<_$AddCategoryBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddCategoryBodyImplToJson(
      this,
    );
  }
}

abstract class _AddCategoryBody implements AddCategoryBody {
  const factory _AddCategoryBody(
      {final String? nameAr,
      final String? nameEn,
      final String? image,
      final bool? isActive,
      final int? orderNumber}) = _$AddCategoryBodyImpl;

  factory _AddCategoryBody.fromJson(Map<String, dynamic> json) =
      _$AddCategoryBodyImpl.fromJson;

  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  String? get image;
  @override
  bool? get isActive;
  @override
  int? get orderNumber;

  /// Create a copy of AddCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddCategoryBodyImplCopyWith<_$AddCategoryBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
