// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_product_option_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddProductOptionBody _$AddProductOptionBodyFromJson(Map<String, dynamic> json) {
  return _AddProductOptionBody.fromJson(json);
}

/// @nodoc
mixin _$AddProductOptionBody {
  int? get productId => throw _privateConstructorUsedError;
  List<int>? get optionIds => throw _privateConstructorUsedError;
  int? get quantity => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  int? get discount => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get discountExpiration => throw _privateConstructorUsedError;

  /// Serializes this AddProductOptionBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddProductOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddProductOptionBodyCopyWith<AddProductOptionBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddProductOptionBodyCopyWith<$Res> {
  factory $AddProductOptionBodyCopyWith(AddProductOptionBody value,
          $Res Function(AddProductOptionBody) then) =
      _$AddProductOptionBodyCopyWithImpl<$Res, AddProductOptionBody>;
  @useResult
  $Res call(
      {int? productId,
      List<int>? optionIds,
      int? quantity,
      double? price,
      int? discount,
      int? type,
      bool? isActive,
      String? image,
      String? discountExpiration});
}

/// @nodoc
class _$AddProductOptionBodyCopyWithImpl<$Res,
        $Val extends AddProductOptionBody>
    implements $AddProductOptionBodyCopyWith<$Res> {
  _$AddProductOptionBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddProductOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? optionIds = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? type = freezed,
    Object? isActive = freezed,
    Object? image = freezed,
    Object? discountExpiration = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      optionIds: freezed == optionIds
          ? _value.optionIds
          : optionIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      discountExpiration: freezed == discountExpiration
          ? _value.discountExpiration
          : discountExpiration // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddProductOptionBodyImplCopyWith<$Res>
    implements $AddProductOptionBodyCopyWith<$Res> {
  factory _$$AddProductOptionBodyImplCopyWith(_$AddProductOptionBodyImpl value,
          $Res Function(_$AddProductOptionBodyImpl) then) =
      __$$AddProductOptionBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? productId,
      List<int>? optionIds,
      int? quantity,
      double? price,
      int? discount,
      int? type,
      bool? isActive,
      String? image,
      String? discountExpiration});
}

/// @nodoc
class __$$AddProductOptionBodyImplCopyWithImpl<$Res>
    extends _$AddProductOptionBodyCopyWithImpl<$Res, _$AddProductOptionBodyImpl>
    implements _$$AddProductOptionBodyImplCopyWith<$Res> {
  __$$AddProductOptionBodyImplCopyWithImpl(_$AddProductOptionBodyImpl _value,
      $Res Function(_$AddProductOptionBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddProductOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? optionIds = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? discount = freezed,
    Object? type = freezed,
    Object? isActive = freezed,
    Object? image = freezed,
    Object? discountExpiration = freezed,
  }) {
    return _then(_$AddProductOptionBodyImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      optionIds: freezed == optionIds
          ? _value._optionIds
          : optionIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      discountExpiration: freezed == discountExpiration
          ? _value.discountExpiration
          : discountExpiration // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddProductOptionBodyImpl implements _AddProductOptionBody {
  const _$AddProductOptionBodyImpl(
      {this.productId,
      final List<int>? optionIds,
      this.quantity,
      this.price,
      this.discount,
      this.type,
      this.isActive,
      this.image,
      this.discountExpiration})
      : _optionIds = optionIds;

  factory _$AddProductOptionBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddProductOptionBodyImplFromJson(json);

  @override
  final int? productId;
  final List<int>? _optionIds;
  @override
  List<int>? get optionIds {
    final value = _optionIds;
    if (value == null) return null;
    if (_optionIds is EqualUnmodifiableListView) return _optionIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? quantity;
  @override
  final double? price;
  @override
  final int? discount;
  @override
  final int? type;
  @override
  final bool? isActive;
  @override
  final String? image;
  @override
  final String? discountExpiration;

  @override
  String toString() {
    return 'AddProductOptionBody(productId: $productId, optionIds: $optionIds, quantity: $quantity, price: $price, discount: $discount, type: $type, isActive: $isActive, image: $image, discountExpiration: $discountExpiration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddProductOptionBodyImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            const DeepCollectionEquality()
                .equals(other._optionIds, _optionIds) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.discountExpiration, discountExpiration) ||
                other.discountExpiration == discountExpiration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      const DeepCollectionEquality().hash(_optionIds),
      quantity,
      price,
      discount,
      type,
      isActive,
      image,
      discountExpiration);

  /// Create a copy of AddProductOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddProductOptionBodyImplCopyWith<_$AddProductOptionBodyImpl>
      get copyWith =>
          __$$AddProductOptionBodyImplCopyWithImpl<_$AddProductOptionBodyImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddProductOptionBodyImplToJson(
      this,
    );
  }
}

abstract class _AddProductOptionBody implements AddProductOptionBody {
  const factory _AddProductOptionBody(
      {final int? productId,
      final List<int>? optionIds,
      final int? quantity,
      final double? price,
      final int? discount,
      final int? type,
      final bool? isActive,
      final String? image,
      final String? discountExpiration}) = _$AddProductOptionBodyImpl;

  factory _AddProductOptionBody.fromJson(Map<String, dynamic> json) =
      _$AddProductOptionBodyImpl.fromJson;

  @override
  int? get productId;
  @override
  List<int>? get optionIds;
  @override
  int? get quantity;
  @override
  double? get price;
  @override
  int? get discount;
  @override
  int? get type;
  @override
  bool? get isActive;
  @override
  String? get image;
  @override
  String? get discountExpiration;

  /// Create a copy of AddProductOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddProductOptionBodyImplCopyWith<_$AddProductOptionBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
