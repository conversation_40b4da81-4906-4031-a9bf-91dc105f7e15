// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_category_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditCategoryBody _$EditCategoryBodyFromJson(Map<String, dynamic> json) {
  return _EditCategoryBody.fromJson(json);
}

/// @nodoc
mixin _$EditCategoryBody {
  int? get id => throw _privateConstructorUsedError;
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get orderNumber => throw _privateConstructorUsedError;

  /// Serializes this EditCategoryBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditCategoryBodyCopyWith<EditCategoryBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditCategoryBodyCopyWith<$Res> {
  factory $EditCategoryBodyCopyWith(
          EditCategoryBody value, $Res Function(EditCategoryBody) then) =
      _$EditCategoryBodyCopyWithImpl<$Res, EditCategoryBody>;
  @useResult
  $Res call(
      {int? id,
      String? nameAr,
      String? nameEn,
      String? image,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class _$EditCategoryBodyCopyWithImpl<$Res, $Val extends EditCategoryBody>
    implements $EditCategoryBodyCopyWith<$Res> {
  _$EditCategoryBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditCategoryBodyImplCopyWith<$Res>
    implements $EditCategoryBodyCopyWith<$Res> {
  factory _$$EditCategoryBodyImplCopyWith(_$EditCategoryBodyImpl value,
          $Res Function(_$EditCategoryBodyImpl) then) =
      __$$EditCategoryBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? nameAr,
      String? nameEn,
      String? image,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class __$$EditCategoryBodyImplCopyWithImpl<$Res>
    extends _$EditCategoryBodyCopyWithImpl<$Res, _$EditCategoryBodyImpl>
    implements _$$EditCategoryBodyImplCopyWith<$Res> {
  __$$EditCategoryBodyImplCopyWithImpl(_$EditCategoryBodyImpl _value,
      $Res Function(_$EditCategoryBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_$EditCategoryBodyImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditCategoryBodyImpl implements _EditCategoryBody {
  const _$EditCategoryBodyImpl(
      {this.id,
      this.nameAr,
      this.nameEn,
      this.image,
      this.isActive,
      this.orderNumber});

  factory _$EditCategoryBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditCategoryBodyImplFromJson(json);

  @override
  final int? id;
  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final String? image;
  @override
  final bool? isActive;
  @override
  final int? orderNumber;

  @override
  String toString() {
    return 'EditCategoryBody(id: $id, nameAr: $nameAr, nameEn: $nameEn, image: $image, isActive: $isActive, orderNumber: $orderNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditCategoryBodyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, nameAr, nameEn, image, isActive, orderNumber);

  /// Create a copy of EditCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditCategoryBodyImplCopyWith<_$EditCategoryBodyImpl> get copyWith =>
      __$$EditCategoryBodyImplCopyWithImpl<_$EditCategoryBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditCategoryBodyImplToJson(
      this,
    );
  }
}

abstract class _EditCategoryBody implements EditCategoryBody {
  const factory _EditCategoryBody(
      {final int? id,
      final String? nameAr,
      final String? nameEn,
      final String? image,
      final bool? isActive,
      final int? orderNumber}) = _$EditCategoryBodyImpl;

  factory _EditCategoryBody.fromJson(Map<String, dynamic> json) =
      _$EditCategoryBodyImpl.fromJson;

  @override
  int? get id;
  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  String? get image;
  @override
  bool? get isActive;
  @override
  int? get orderNumber;

  /// Create a copy of EditCategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditCategoryBodyImplCopyWith<_$EditCategoryBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
