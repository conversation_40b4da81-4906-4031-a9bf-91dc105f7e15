// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_subcategory_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditSubcategoryBodyImpl _$$EditSubcategoryBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$EditSubcategoryBodyImpl(
      id: (json['id'] as num?)?.toInt(),
      parentId: (json['parentId'] as num?)?.toInt(),
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      isActive: json['isActive'] as bool?,
      orderNumber: (json['orderNumber'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$EditSubcategoryBodyImplToJson(
        _$EditSubcategoryBodyImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.parentId case final value?) 'parentId': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.orderNumber case final value?) 'orderNumber': value,
    };
