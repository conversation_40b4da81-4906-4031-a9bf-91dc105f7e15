// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_products_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetProductsBody _$GetProductsBodyFromJson(Map<String, dynamic> json) {
  return _GetProductsBody.fromJson(json);
}

/// @nodoc
mixin _$GetProductsBody {
  String? get search => throw _privateConstructorUsedError;
  String? get sales => throw _privateConstructorUsedError;
  String? get byDate => throw _privateConstructorUsedError;
  String? get discount => throw _privateConstructorUsedError;
  int? get categoryId => throw _privateConstructorUsedError;
  int? get subCategoryId => throw _privateConstructorUsedError;

  /// Serializes this GetProductsBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetProductsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetProductsBodyCopyWith<GetProductsBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetProductsBodyCopyWith<$Res> {
  factory $GetProductsBodyCopyWith(
          GetProductsBody value, $Res Function(GetProductsBody) then) =
      _$GetProductsBodyCopyWithImpl<$Res, GetProductsBody>;
  @useResult
  $Res call(
      {String? search,
      String? sales,
      String? byDate,
      String? discount,
      int? categoryId,
      int? subCategoryId});
}

/// @nodoc
class _$GetProductsBodyCopyWithImpl<$Res, $Val extends GetProductsBody>
    implements $GetProductsBodyCopyWith<$Res> {
  _$GetProductsBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetProductsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? search = freezed,
    Object? sales = freezed,
    Object? byDate = freezed,
    Object? discount = freezed,
    Object? categoryId = freezed,
    Object? subCategoryId = freezed,
  }) {
    return _then(_value.copyWith(
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      sales: freezed == sales
          ? _value.sales
          : sales // ignore: cast_nullable_to_non_nullable
              as String?,
      byDate: freezed == byDate
          ? _value.byDate
          : byDate // ignore: cast_nullable_to_non_nullable
              as String?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategoryId: freezed == subCategoryId
          ? _value.subCategoryId
          : subCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetProductsBodyImplCopyWith<$Res>
    implements $GetProductsBodyCopyWith<$Res> {
  factory _$$GetProductsBodyImplCopyWith(_$GetProductsBodyImpl value,
          $Res Function(_$GetProductsBodyImpl) then) =
      __$$GetProductsBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? search,
      String? sales,
      String? byDate,
      String? discount,
      int? categoryId,
      int? subCategoryId});
}

/// @nodoc
class __$$GetProductsBodyImplCopyWithImpl<$Res>
    extends _$GetProductsBodyCopyWithImpl<$Res, _$GetProductsBodyImpl>
    implements _$$GetProductsBodyImplCopyWith<$Res> {
  __$$GetProductsBodyImplCopyWithImpl(
      _$GetProductsBodyImpl _value, $Res Function(_$GetProductsBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetProductsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? search = freezed,
    Object? sales = freezed,
    Object? byDate = freezed,
    Object? discount = freezed,
    Object? categoryId = freezed,
    Object? subCategoryId = freezed,
  }) {
    return _then(_$GetProductsBodyImpl(
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      sales: freezed == sales
          ? _value.sales
          : sales // ignore: cast_nullable_to_non_nullable
              as String?,
      byDate: freezed == byDate
          ? _value.byDate
          : byDate // ignore: cast_nullable_to_non_nullable
              as String?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      subCategoryId: freezed == subCategoryId
          ? _value.subCategoryId
          : subCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetProductsBodyImpl implements _GetProductsBody {
  const _$GetProductsBodyImpl(
      {this.search,
      this.sales,
      this.byDate,
      this.discount,
      this.categoryId,
      this.subCategoryId});

  factory _$GetProductsBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetProductsBodyImplFromJson(json);

  @override
  final String? search;
  @override
  final String? sales;
  @override
  final String? byDate;
  @override
  final String? discount;
  @override
  final int? categoryId;
  @override
  final int? subCategoryId;

  @override
  String toString() {
    return 'GetProductsBody(search: $search, sales: $sales, byDate: $byDate, discount: $discount, categoryId: $categoryId, subCategoryId: $subCategoryId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetProductsBodyImpl &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.sales, sales) || other.sales == sales) &&
            (identical(other.byDate, byDate) || other.byDate == byDate) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.subCategoryId, subCategoryId) ||
                other.subCategoryId == subCategoryId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, search, sales, byDate, discount, categoryId, subCategoryId);

  /// Create a copy of GetProductsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetProductsBodyImplCopyWith<_$GetProductsBodyImpl> get copyWith =>
      __$$GetProductsBodyImplCopyWithImpl<_$GetProductsBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetProductsBodyImplToJson(
      this,
    );
  }
}

abstract class _GetProductsBody implements GetProductsBody {
  const factory _GetProductsBody(
      {final String? search,
      final String? sales,
      final String? byDate,
      final String? discount,
      final int? categoryId,
      final int? subCategoryId}) = _$GetProductsBodyImpl;

  factory _GetProductsBody.fromJson(Map<String, dynamic> json) =
      _$GetProductsBodyImpl.fromJson;

  @override
  String? get search;
  @override
  String? get sales;
  @override
  String? get byDate;
  @override
  String? get discount;
  @override
  int? get categoryId;
  @override
  int? get subCategoryId;

  /// Create a copy of GetProductsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetProductsBodyImplCopyWith<_$GetProductsBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
