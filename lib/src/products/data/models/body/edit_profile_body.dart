import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_profile_body.freezed.dart';
part 'edit_profile_body.g.dart';

@freezed
class EditProfileBody with _$EditProfileBody {
  const factory EditProfileBody({
    String? phone,
    String? address,
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? cityId,
    int? appActivityId,
    int? lat,
    int? lng,
    String? logo,
    String? cover,
  }) = _EditProfileBody;

  factory EditProfileBody.fromJson(Map<String, dynamic> json) =>
      _$EditProfileBodyFromJson(json);
}
