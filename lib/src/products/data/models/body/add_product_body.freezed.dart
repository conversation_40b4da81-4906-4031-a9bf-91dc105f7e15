// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_product_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddProductBody _$AddProductBodyFromJson(Map<String, dynamic> json) {
  return _AddProductBody.fromJson(json);
}

/// @nodoc
mixin _$AddProductBody {
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  String? get descriptionAr => throw _privateConstructorUsedError;
  String? get descriptionEn => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  bool? get hasOptions => throw _privateConstructorUsedError;
  List<int>? get categoriesId => throw _privateConstructorUsedError;
  List<String>? get covers => throw _privateConstructorUsedError;
  bool? get isForPoints => throw _privateConstructorUsedError;
  bool? get hasOffer => throw _privateConstructorUsedError;
  bool? get isAvailable => throw _privateConstructorUsedError;
  bool? get showOnMainPage => throw _privateConstructorUsedError;
  List<ProductOptionView>? get productOptions =>
      throw _privateConstructorUsedError;

  /// Serializes this AddProductBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddProductBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddProductBodyCopyWith<AddProductBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddProductBodyCopyWith<$Res> {
  factory $AddProductBodyCopyWith(
          AddProductBody value, $Res Function(AddProductBody) then) =
      _$AddProductBodyCopyWithImpl<$Res, AddProductBody>;
  @useResult
  $Res call(
      {String? nameAr,
      String? nameEn,
      String? descriptionAr,
      String? descriptionEn,
      String? image,
      bool? isActive,
      bool? hasOptions,
      List<int>? categoriesId,
      List<String>? covers,
      bool? isForPoints,
      bool? hasOffer,
      bool? isAvailable,
      bool? showOnMainPage,
      List<ProductOptionView>? productOptions});
}

/// @nodoc
class _$AddProductBodyCopyWithImpl<$Res, $Val extends AddProductBody>
    implements $AddProductBodyCopyWith<$Res> {
  _$AddProductBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddProductBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? hasOptions = freezed,
    Object? categoriesId = freezed,
    Object? covers = freezed,
    Object? isForPoints = freezed,
    Object? hasOffer = freezed,
    Object? isAvailable = freezed,
    Object? showOnMainPage = freezed,
    Object? productOptions = freezed,
  }) {
    return _then(_value.copyWith(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _value.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionEn: freezed == descriptionEn
          ? _value.descriptionEn
          : descriptionEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      categoriesId: freezed == categoriesId
          ? _value.categoriesId
          : categoriesId // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      covers: freezed == covers
          ? _value.covers
          : covers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isForPoints: freezed == isForPoints
          ? _value.isForPoints
          : isForPoints // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOffer: freezed == hasOffer
          ? _value.hasOffer
          : hasOffer // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      showOnMainPage: freezed == showOnMainPage
          ? _value.showOnMainPage
          : showOnMainPage // ignore: cast_nullable_to_non_nullable
              as bool?,
      productOptions: freezed == productOptions
          ? _value.productOptions
          : productOptions // ignore: cast_nullable_to_non_nullable
              as List<ProductOptionView>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddProductBodyImplCopyWith<$Res>
    implements $AddProductBodyCopyWith<$Res> {
  factory _$$AddProductBodyImplCopyWith(_$AddProductBodyImpl value,
          $Res Function(_$AddProductBodyImpl) then) =
      __$$AddProductBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nameAr,
      String? nameEn,
      String? descriptionAr,
      String? descriptionEn,
      String? image,
      bool? isActive,
      bool? hasOptions,
      List<int>? categoriesId,
      List<String>? covers,
      bool? isForPoints,
      bool? hasOffer,
      bool? isAvailable,
      bool? showOnMainPage,
      List<ProductOptionView>? productOptions});
}

/// @nodoc
class __$$AddProductBodyImplCopyWithImpl<$Res>
    extends _$AddProductBodyCopyWithImpl<$Res, _$AddProductBodyImpl>
    implements _$$AddProductBodyImplCopyWith<$Res> {
  __$$AddProductBodyImplCopyWithImpl(
      _$AddProductBodyImpl _value, $Res Function(_$AddProductBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddProductBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionEn = freezed,
    Object? image = freezed,
    Object? isActive = freezed,
    Object? hasOptions = freezed,
    Object? categoriesId = freezed,
    Object? covers = freezed,
    Object? isForPoints = freezed,
    Object? hasOffer = freezed,
    Object? isAvailable = freezed,
    Object? showOnMainPage = freezed,
    Object? productOptions = freezed,
  }) {
    return _then(_$AddProductBodyImpl(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _value.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionEn: freezed == descriptionEn
          ? _value.descriptionEn
          : descriptionEn // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOptions: freezed == hasOptions
          ? _value.hasOptions
          : hasOptions // ignore: cast_nullable_to_non_nullable
              as bool?,
      categoriesId: freezed == categoriesId
          ? _value._categoriesId
          : categoriesId // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      covers: freezed == covers
          ? _value._covers
          : covers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isForPoints: freezed == isForPoints
          ? _value.isForPoints
          : isForPoints // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasOffer: freezed == hasOffer
          ? _value.hasOffer
          : hasOffer // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      showOnMainPage: freezed == showOnMainPage
          ? _value.showOnMainPage
          : showOnMainPage // ignore: cast_nullable_to_non_nullable
              as bool?,
      productOptions: freezed == productOptions
          ? _value._productOptions
          : productOptions // ignore: cast_nullable_to_non_nullable
              as List<ProductOptionView>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddProductBodyImpl implements _AddProductBody {
  const _$AddProductBodyImpl(
      {this.nameAr,
      this.nameEn,
      this.descriptionAr,
      this.descriptionEn,
      this.image,
      this.isActive,
      this.hasOptions,
      final List<int>? categoriesId,
      final List<String>? covers,
      this.isForPoints,
      this.hasOffer,
      this.isAvailable,
      this.showOnMainPage,
      final List<ProductOptionView>? productOptions})
      : _categoriesId = categoriesId,
        _covers = covers,
        _productOptions = productOptions;

  factory _$AddProductBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddProductBodyImplFromJson(json);

  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final String? descriptionAr;
  @override
  final String? descriptionEn;
  @override
  final String? image;
  @override
  final bool? isActive;
  @override
  final bool? hasOptions;
  final List<int>? _categoriesId;
  @override
  List<int>? get categoriesId {
    final value = _categoriesId;
    if (value == null) return null;
    if (_categoriesId is EqualUnmodifiableListView) return _categoriesId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _covers;
  @override
  List<String>? get covers {
    final value = _covers;
    if (value == null) return null;
    if (_covers is EqualUnmodifiableListView) return _covers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isForPoints;
  @override
  final bool? hasOffer;
  @override
  final bool? isAvailable;
  @override
  final bool? showOnMainPage;
  final List<ProductOptionView>? _productOptions;
  @override
  List<ProductOptionView>? get productOptions {
    final value = _productOptions;
    if (value == null) return null;
    if (_productOptions is EqualUnmodifiableListView) return _productOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AddProductBody(nameAr: $nameAr, nameEn: $nameEn, descriptionAr: $descriptionAr, descriptionEn: $descriptionEn, image: $image, isActive: $isActive, hasOptions: $hasOptions, categoriesId: $categoriesId, covers: $covers, isForPoints: $isForPoints, hasOffer: $hasOffer, isAvailable: $isAvailable, showOnMainPage: $showOnMainPage, productOptions: $productOptions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddProductBodyImpl &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.descriptionAr, descriptionAr) ||
                other.descriptionAr == descriptionAr) &&
            (identical(other.descriptionEn, descriptionEn) ||
                other.descriptionEn == descriptionEn) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.hasOptions, hasOptions) ||
                other.hasOptions == hasOptions) &&
            const DeepCollectionEquality()
                .equals(other._categoriesId, _categoriesId) &&
            const DeepCollectionEquality().equals(other._covers, _covers) &&
            (identical(other.isForPoints, isForPoints) ||
                other.isForPoints == isForPoints) &&
            (identical(other.hasOffer, hasOffer) ||
                other.hasOffer == hasOffer) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.showOnMainPage, showOnMainPage) ||
                other.showOnMainPage == showOnMainPage) &&
            const DeepCollectionEquality()
                .equals(other._productOptions, _productOptions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nameAr,
      nameEn,
      descriptionAr,
      descriptionEn,
      image,
      isActive,
      hasOptions,
      const DeepCollectionEquality().hash(_categoriesId),
      const DeepCollectionEquality().hash(_covers),
      isForPoints,
      hasOffer,
      isAvailable,
      showOnMainPage,
      const DeepCollectionEquality().hash(_productOptions));

  /// Create a copy of AddProductBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddProductBodyImplCopyWith<_$AddProductBodyImpl> get copyWith =>
      __$$AddProductBodyImplCopyWithImpl<_$AddProductBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddProductBodyImplToJson(
      this,
    );
  }
}

abstract class _AddProductBody implements AddProductBody {
  const factory _AddProductBody(
      {final String? nameAr,
      final String? nameEn,
      final String? descriptionAr,
      final String? descriptionEn,
      final String? image,
      final bool? isActive,
      final bool? hasOptions,
      final List<int>? categoriesId,
      final List<String>? covers,
      final bool? isForPoints,
      final bool? hasOffer,
      final bool? isAvailable,
      final bool? showOnMainPage,
      final List<ProductOptionView>? productOptions}) = _$AddProductBodyImpl;

  factory _AddProductBody.fromJson(Map<String, dynamic> json) =
      _$AddProductBodyImpl.fromJson;

  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  String? get descriptionAr;
  @override
  String? get descriptionEn;
  @override
  String? get image;
  @override
  bool? get isActive;
  @override
  bool? get hasOptions;
  @override
  List<int>? get categoriesId;
  @override
  List<String>? get covers;
  @override
  bool? get isForPoints;
  @override
  bool? get hasOffer;
  @override
  bool? get isAvailable;
  @override
  bool? get showOnMainPage;
  @override
  List<ProductOptionView>? get productOptions;

  /// Create a copy of AddProductBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddProductBodyImplCopyWith<_$AddProductBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
