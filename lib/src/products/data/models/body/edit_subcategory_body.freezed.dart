// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_subcategory_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditSubcategoryBody _$EditSubcategoryBodyFromJson(Map<String, dynamic> json) {
  return _EditSubcategoryBody.fromJson(json);
}

/// @nodoc
mixin _$EditSubcategoryBody {
  int? get id => throw _privateConstructorUsedError;
  int? get parentId => throw _privateConstructorUsedError;
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get orderNumber => throw _privateConstructorUsedError;

  /// Serializes this EditSubcategoryBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditSubcategoryBodyCopyWith<EditSubcategoryBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditSubcategoryBodyCopyWith<$Res> {
  factory $EditSubcategoryBodyCopyWith(
          EditSubcategoryBody value, $Res Function(EditSubcategoryBody) then) =
      _$EditSubcategoryBodyCopyWithImpl<$Res, EditSubcategoryBody>;
  @useResult
  $Res call(
      {int? id,
      int? parentId,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class _$EditSubcategoryBodyCopyWithImpl<$Res, $Val extends EditSubcategoryBody>
    implements $EditSubcategoryBodyCopyWith<$Res> {
  _$EditSubcategoryBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? parentId = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditSubcategoryBodyImplCopyWith<$Res>
    implements $EditSubcategoryBodyCopyWith<$Res> {
  factory _$$EditSubcategoryBodyImplCopyWith(_$EditSubcategoryBodyImpl value,
          $Res Function(_$EditSubcategoryBodyImpl) then) =
      __$$EditSubcategoryBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? parentId,
      String? nameAr,
      String? nameEn,
      bool? isActive,
      int? orderNumber});
}

/// @nodoc
class __$$EditSubcategoryBodyImplCopyWithImpl<$Res>
    extends _$EditSubcategoryBodyCopyWithImpl<$Res, _$EditSubcategoryBodyImpl>
    implements _$$EditSubcategoryBodyImplCopyWith<$Res> {
  __$$EditSubcategoryBodyImplCopyWithImpl(_$EditSubcategoryBodyImpl _value,
      $Res Function(_$EditSubcategoryBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? parentId = freezed,
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? orderNumber = freezed,
  }) {
    return _then(_$EditSubcategoryBodyImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNumber: freezed == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditSubcategoryBodyImpl implements _EditSubcategoryBody {
  const _$EditSubcategoryBodyImpl(
      {this.id,
      this.parentId,
      this.nameAr,
      this.nameEn,
      this.isActive,
      this.orderNumber});

  factory _$EditSubcategoryBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditSubcategoryBodyImplFromJson(json);

  @override
  final int? id;
  @override
  final int? parentId;
  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? orderNumber;

  @override
  String toString() {
    return 'EditSubcategoryBody(id: $id, parentId: $parentId, nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, orderNumber: $orderNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditSubcategoryBodyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, parentId, nameAr, nameEn, isActive, orderNumber);

  /// Create a copy of EditSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditSubcategoryBodyImplCopyWith<_$EditSubcategoryBodyImpl> get copyWith =>
      __$$EditSubcategoryBodyImplCopyWithImpl<_$EditSubcategoryBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditSubcategoryBodyImplToJson(
      this,
    );
  }
}

abstract class _EditSubcategoryBody implements EditSubcategoryBody {
  const factory _EditSubcategoryBody(
      {final int? id,
      final int? parentId,
      final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? orderNumber}) = _$EditSubcategoryBodyImpl;

  factory _EditSubcategoryBody.fromJson(Map<String, dynamic> json) =
      _$EditSubcategoryBodyImpl.fromJson;

  @override
  int? get id;
  @override
  int? get parentId;
  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get orderNumber;

  /// Create a copy of EditSubcategoryBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditSubcategoryBodyImplCopyWith<_$EditSubcategoryBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
