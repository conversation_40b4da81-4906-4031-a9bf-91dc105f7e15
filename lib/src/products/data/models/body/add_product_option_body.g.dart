// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_product_option_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AddProductOptionBodyImpl _$$AddProductOptionBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$AddProductOptionBodyImpl(
      productId: (json['productId'] as num?)?.toInt(),
      optionIds: (json['optionIds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      quantity: (json['quantity'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
      isActive: json['isActive'] as bool?,
      image: json['image'] as String?,
      discountExpiration: json['discountExpiration'] as String?,
    );

Map<String, dynamic> _$$AddProductOptionBodyImplToJson(
        _$AddProductOptionBodyImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.optionIds case final value?) 'optionIds': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.type case final value?) 'type': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.image case final value?) 'image': value,
      if (instance.discountExpiration case final value?)
        'discountExpiration': value,
    };
