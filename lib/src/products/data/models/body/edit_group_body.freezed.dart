// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_group_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditGroupBody _$EditGroupBodyFromJson(Map<String, dynamic> json) {
  return _EditGroupBody.fromJson(json);
}

/// @nodoc
mixin _$EditGroupBody {
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get groupType => throw _privateConstructorUsedError;

  /// Serializes this EditGroupBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditGroupBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditGroupBodyCopyWith<EditGroupBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditGroupBodyCopyWith<$Res> {
  factory $EditGroupBodyCopyWith(
          EditGroupBody value, $Res Function(EditGroupBody) then) =
      _$EditGroupBodyCopyWithImpl<$Res, EditGroupBody>;
  @useResult
  $Res call({String? nameAr, String? nameEn, bool? isActive, int? groupType});
}

/// @nodoc
class _$EditGroupBodyCopyWithImpl<$Res, $Val extends EditGroupBody>
    implements $EditGroupBodyCopyWith<$Res> {
  _$EditGroupBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditGroupBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? groupType = freezed,
  }) {
    return _then(_value.copyWith(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      groupType: freezed == groupType
          ? _value.groupType
          : groupType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditGroupBodyImplCopyWith<$Res>
    implements $EditGroupBodyCopyWith<$Res> {
  factory _$$EditGroupBodyImplCopyWith(
          _$EditGroupBodyImpl value, $Res Function(_$EditGroupBodyImpl) then) =
      __$$EditGroupBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? nameAr, String? nameEn, bool? isActive, int? groupType});
}

/// @nodoc
class __$$EditGroupBodyImplCopyWithImpl<$Res>
    extends _$EditGroupBodyCopyWithImpl<$Res, _$EditGroupBodyImpl>
    implements _$$EditGroupBodyImplCopyWith<$Res> {
  __$$EditGroupBodyImplCopyWithImpl(
      _$EditGroupBodyImpl _value, $Res Function(_$EditGroupBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditGroupBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? groupType = freezed,
  }) {
    return _then(_$EditGroupBodyImpl(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      groupType: freezed == groupType
          ? _value.groupType
          : groupType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditGroupBodyImpl implements _EditGroupBody {
  const _$EditGroupBodyImpl(
      {this.nameAr, this.nameEn, this.isActive, this.groupType});

  factory _$EditGroupBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditGroupBodyImplFromJson(json);

  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? groupType;

  @override
  String toString() {
    return 'EditGroupBody(nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, groupType: $groupType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditGroupBodyImpl &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.groupType, groupType) ||
                other.groupType == groupType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, nameAr, nameEn, isActive, groupType);

  /// Create a copy of EditGroupBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditGroupBodyImplCopyWith<_$EditGroupBodyImpl> get copyWith =>
      __$$EditGroupBodyImplCopyWithImpl<_$EditGroupBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditGroupBodyImplToJson(
      this,
    );
  }
}

abstract class _EditGroupBody implements EditGroupBody {
  const factory _EditGroupBody(
      {final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? groupType}) = _$EditGroupBodyImpl;

  factory _EditGroupBody.fromJson(Map<String, dynamic> json) =
      _$EditGroupBodyImpl.fromJson;

  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get groupType;

  /// Create a copy of EditGroupBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditGroupBodyImplCopyWith<_$EditGroupBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
