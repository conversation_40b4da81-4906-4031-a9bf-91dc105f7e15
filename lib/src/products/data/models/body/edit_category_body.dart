import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_category_body.freezed.dart';
part 'edit_category_body.g.dart';

@freezed
class EditCategoryBody with _$EditCategoryBody {
  const factory EditCategoryBody({
    int? id,
    String? nameAr,
    String? nameEn,
    String? image,
    bool? isActive,
    int? orderNumber,
  }) = _EditCategoryBody;

  factory EditCategoryBody.fromJson(Map<String, dynamic> json) =>
      _$EditCategoryBodyFromJson(json);
}
