// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_groups_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetGroupsBody _$GetGroupsBodyFromJson(Map<String, dynamic> json) {
  return _GetGroupsBody.fromJson(json);
}

/// @nodoc
mixin _$GetGroupsBody {
  int? get skip => throw _privateConstructorUsedError;
  int? get take => throw _privateConstructorUsedError;

  /// Serializes this GetGroupsBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetGroupsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetGroupsBodyCopyWith<GetGroupsBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetGroupsBodyCopyWith<$Res> {
  factory $GetGroupsBodyCopyWith(
          GetGroupsBody value, $Res Function(GetGroupsBody) then) =
      _$GetGroupsBodyCopyWithImpl<$Res, GetGroupsBody>;
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class _$GetGroupsBodyCopyWithImpl<$Res, $Val extends GetGroupsBody>
    implements $GetGroupsBodyCopyWith<$Res> {
  _$GetGroupsBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetGroupsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_value.copyWith(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetGroupsBodyImplCopyWith<$Res>
    implements $GetGroupsBodyCopyWith<$Res> {
  factory _$$GetGroupsBodyImplCopyWith(
          _$GetGroupsBodyImpl value, $Res Function(_$GetGroupsBodyImpl) then) =
      __$$GetGroupsBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class __$$GetGroupsBodyImplCopyWithImpl<$Res>
    extends _$GetGroupsBodyCopyWithImpl<$Res, _$GetGroupsBodyImpl>
    implements _$$GetGroupsBodyImplCopyWith<$Res> {
  __$$GetGroupsBodyImplCopyWithImpl(
      _$GetGroupsBodyImpl _value, $Res Function(_$GetGroupsBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetGroupsBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_$GetGroupsBodyImpl(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetGroupsBodyImpl implements _GetGroupsBody {
  const _$GetGroupsBodyImpl({this.skip, this.take});

  factory _$GetGroupsBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetGroupsBodyImplFromJson(json);

  @override
  final int? skip;
  @override
  final int? take;

  @override
  String toString() {
    return 'GetGroupsBody(skip: $skip, take: $take)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetGroupsBodyImpl &&
            (identical(other.skip, skip) || other.skip == skip) &&
            (identical(other.take, take) || other.take == take));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, skip, take);

  /// Create a copy of GetGroupsBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetGroupsBodyImplCopyWith<_$GetGroupsBodyImpl> get copyWith =>
      __$$GetGroupsBodyImplCopyWithImpl<_$GetGroupsBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetGroupsBodyImplToJson(
      this,
    );
  }
}

abstract class _GetGroupsBody implements GetGroupsBody {
  const factory _GetGroupsBody({final int? skip, final int? take}) =
      _$GetGroupsBodyImpl;

  factory _GetGroupsBody.fromJson(Map<String, dynamic> json) =
      _$GetGroupsBodyImpl.fromJson;

  @override
  int? get skip;
  @override
  int? get take;

  /// Create a copy of GetGroupsBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetGroupsBodyImplCopyWith<_$GetGroupsBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
