import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_category_body.freezed.dart';
part 'add_category_body.g.dart';

@freezed
class AddCategoryBody with _$AddCategoryBody {
  const factory AddCategoryBody({
    String? nameAr,
    String? nameEn,
    String? image,
    bool? isActive,
    int? orderNumber,
  }) = _AddCategoryBody;

  factory AddCategoryBody.fromJson(Map<String, dynamic> json) =>
      _$AddCategoryBodyFromJson(json);
}
