import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_addition_body.freezed.dart';
part 'edit_addition_body.g.dart';

@freezed
class EditAdditionBody with _$EditAdditionBody {
  const factory EditAdditionBody({
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? defaultPrice,
  }) = _EditAdditionBody;

  factory EditAdditionBody.fromJson(Map<String, dynamic> json) =>
      _$EditAdditionBodyFromJson(json);
}
