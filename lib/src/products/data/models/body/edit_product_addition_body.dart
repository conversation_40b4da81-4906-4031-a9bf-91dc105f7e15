import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_product_addition_body.freezed.dart';
part 'edit_product_addition_body.g.dart';

@freezed
class EditProductAdditionBody with _$EditProductAdditionBody {
  const factory EditProductAdditionBody({
    int? id,
    int? additionId,
    int? productOptionId,
    double? price,
    bool? isActive,
  }) = _EditProductAdditionBody;

  factory EditProductAdditionBody.fromJson(Map<String, dynamic> json) =>
      _$EditProductAdditionBodyFromJson(json);
}
