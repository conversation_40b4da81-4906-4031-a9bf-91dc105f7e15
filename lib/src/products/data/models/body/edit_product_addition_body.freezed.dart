// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_product_addition_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EditProductAdditionBody _$EditProductAdditionBodyFromJson(
    Map<String, dynamic> json) {
  return _EditProductAdditionBody.fromJson(json);
}

/// @nodoc
mixin _$EditProductAdditionBody {
  int? get id => throw _privateConstructorUsedError;
  int? get additionId => throw _privateConstructorUsedError;
  int? get productOptionId => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;

  /// Serializes this EditProductAdditionBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditProductAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditProductAdditionBodyCopyWith<EditProductAdditionBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditProductAdditionBodyCopyWith<$Res> {
  factory $EditProductAdditionBodyCopyWith(EditProductAdditionBody value,
          $Res Function(EditProductAdditionBody) then) =
      _$EditProductAdditionBodyCopyWithImpl<$Res, EditProductAdditionBody>;
  @useResult
  $Res call(
      {int? id,
      int? additionId,
      int? productOptionId,
      double? price,
      bool? isActive});
}

/// @nodoc
class _$EditProductAdditionBodyCopyWithImpl<$Res,
        $Val extends EditProductAdditionBody>
    implements $EditProductAdditionBodyCopyWith<$Res> {
  _$EditProductAdditionBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditProductAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? additionId = freezed,
    Object? productOptionId = freezed,
    Object? price = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      additionId: freezed == additionId
          ? _value.additionId
          : additionId // ignore: cast_nullable_to_non_nullable
              as int?,
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditProductAdditionBodyImplCopyWith<$Res>
    implements $EditProductAdditionBodyCopyWith<$Res> {
  factory _$$EditProductAdditionBodyImplCopyWith(
          _$EditProductAdditionBodyImpl value,
          $Res Function(_$EditProductAdditionBodyImpl) then) =
      __$$EditProductAdditionBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? additionId,
      int? productOptionId,
      double? price,
      bool? isActive});
}

/// @nodoc
class __$$EditProductAdditionBodyImplCopyWithImpl<$Res>
    extends _$EditProductAdditionBodyCopyWithImpl<$Res,
        _$EditProductAdditionBodyImpl>
    implements _$$EditProductAdditionBodyImplCopyWith<$Res> {
  __$$EditProductAdditionBodyImplCopyWithImpl(
      _$EditProductAdditionBodyImpl _value,
      $Res Function(_$EditProductAdditionBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditProductAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? additionId = freezed,
    Object? productOptionId = freezed,
    Object? price = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_$EditProductAdditionBodyImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      additionId: freezed == additionId
          ? _value.additionId
          : additionId // ignore: cast_nullable_to_non_nullable
              as int?,
      productOptionId: freezed == productOptionId
          ? _value.productOptionId
          : productOptionId // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EditProductAdditionBodyImpl implements _EditProductAdditionBody {
  const _$EditProductAdditionBodyImpl(
      {this.id,
      this.additionId,
      this.productOptionId,
      this.price,
      this.isActive});

  factory _$EditProductAdditionBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditProductAdditionBodyImplFromJson(json);

  @override
  final int? id;
  @override
  final int? additionId;
  @override
  final int? productOptionId;
  @override
  final double? price;
  @override
  final bool? isActive;

  @override
  String toString() {
    return 'EditProductAdditionBody(id: $id, additionId: $additionId, productOptionId: $productOptionId, price: $price, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditProductAdditionBodyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.additionId, additionId) ||
                other.additionId == additionId) &&
            (identical(other.productOptionId, productOptionId) ||
                other.productOptionId == productOptionId) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, additionId, productOptionId, price, isActive);

  /// Create a copy of EditProductAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditProductAdditionBodyImplCopyWith<_$EditProductAdditionBodyImpl>
      get copyWith => __$$EditProductAdditionBodyImplCopyWithImpl<
          _$EditProductAdditionBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditProductAdditionBodyImplToJson(
      this,
    );
  }
}

abstract class _EditProductAdditionBody implements EditProductAdditionBody {
  const factory _EditProductAdditionBody(
      {final int? id,
      final int? additionId,
      final int? productOptionId,
      final double? price,
      final bool? isActive}) = _$EditProductAdditionBodyImpl;

  factory _EditProductAdditionBody.fromJson(Map<String, dynamic> json) =
      _$EditProductAdditionBodyImpl.fromJson;

  @override
  int? get id;
  @override
  int? get additionId;
  @override
  int? get productOptionId;
  @override
  double? get price;
  @override
  bool? get isActive;

  /// Create a copy of EditProductAdditionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditProductAdditionBodyImplCopyWith<_$EditProductAdditionBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
