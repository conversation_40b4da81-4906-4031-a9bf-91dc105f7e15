// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_product_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditProductBodyImpl _$$EditProductBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$EditProductBodyImpl(
      productOptionId: (json['productOptionId'] as num?)?.toInt(),
      hasOptions: json['hasOptions'] as bool?,
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      descriptionEn: json['descriptionEn'] as String?,
      isActive: json['isActive'] as bool?,
      isAvailable: json['isAvailable'] as bool?,
      discountExpiration: json['discountExpiration'] == null
          ? null
          : DateTime.parse(json['discountExpiration'] as String),
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$EditProductBodyImplToJson(
        _$EditProductBodyImpl instance) =>
    <String, dynamic>{
      if (instance.productOptionId case final value?) 'productOptionId': value,
      if (instance.hasOptions case final value?) 'hasOptions': value,
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.descriptionAr case final value?) 'descriptionAr': value,
      if (instance.descriptionEn case final value?) 'descriptionEn': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.isAvailable case final value?) 'isAvailable': value,
      if (instance.discountExpiration?.toIso8601String() case final value?)
        'discountExpiration': value,
      if (instance.price case final value?) 'price': value,
      if (instance.discount case final value?) 'discount': value,
    };
