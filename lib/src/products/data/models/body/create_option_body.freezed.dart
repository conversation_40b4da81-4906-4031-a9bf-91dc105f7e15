// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_option_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateOptionBody _$CreateOptionBodyFromJson(Map<String, dynamic> json) {
  return _CreateOptionBody.fromJson(json);
}

/// @nodoc
mixin _$CreateOptionBody {
  String? get nameAr => throw _privateConstructorUsedError;
  String? get nameEn => throw _privateConstructorUsedError;
  bool? get isActive => throw _privateConstructorUsedError;
  int? get groupId => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;

  /// Serializes this CreateOptionBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateOptionBodyCopyWith<CreateOptionBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOptionBodyCopyWith<$Res> {
  factory $CreateOptionBodyCopyWith(
          CreateOptionBody value, $Res Function(CreateOptionBody) then) =
      _$CreateOptionBodyCopyWithImpl<$Res, CreateOptionBody>;
  @useResult
  $Res call(
      {String? nameAr, String? nameEn, bool? isActive, int? groupId, int? id});
}

/// @nodoc
class _$CreateOptionBodyCopyWithImpl<$Res, $Val extends CreateOptionBody>
    implements $CreateOptionBodyCopyWith<$Res> {
  _$CreateOptionBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? groupId = freezed,
    Object? id = freezed,
  }) {
    return _then(_value.copyWith(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateOptionBodyImplCopyWith<$Res>
    implements $CreateOptionBodyCopyWith<$Res> {
  factory _$$CreateOptionBodyImplCopyWith(_$CreateOptionBodyImpl value,
          $Res Function(_$CreateOptionBodyImpl) then) =
      __$$CreateOptionBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nameAr, String? nameEn, bool? isActive, int? groupId, int? id});
}

/// @nodoc
class __$$CreateOptionBodyImplCopyWithImpl<$Res>
    extends _$CreateOptionBodyCopyWithImpl<$Res, _$CreateOptionBodyImpl>
    implements _$$CreateOptionBodyImplCopyWith<$Res> {
  __$$CreateOptionBodyImplCopyWithImpl(_$CreateOptionBodyImpl _value,
      $Res Function(_$CreateOptionBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameAr = freezed,
    Object? nameEn = freezed,
    Object? isActive = freezed,
    Object? groupId = freezed,
    Object? id = freezed,
  }) {
    return _then(_$CreateOptionBodyImpl(
      nameAr: freezed == nameAr
          ? _value.nameAr
          : nameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      nameEn: freezed == nameEn
          ? _value.nameEn
          : nameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOptionBodyImpl implements _CreateOptionBody {
  const _$CreateOptionBodyImpl(
      {this.nameAr, this.nameEn, this.isActive, this.groupId, this.id});

  factory _$CreateOptionBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOptionBodyImplFromJson(json);

  @override
  final String? nameAr;
  @override
  final String? nameEn;
  @override
  final bool? isActive;
  @override
  final int? groupId;
  @override
  final int? id;

  @override
  String toString() {
    return 'CreateOptionBody(nameAr: $nameAr, nameEn: $nameEn, isActive: $isActive, groupId: $groupId, id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOptionBodyImpl &&
            (identical(other.nameAr, nameAr) || other.nameAr == nameAr) &&
            (identical(other.nameEn, nameEn) || other.nameEn == nameEn) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, nameAr, nameEn, isActive, groupId, id);

  /// Create a copy of CreateOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOptionBodyImplCopyWith<_$CreateOptionBodyImpl> get copyWith =>
      __$$CreateOptionBodyImplCopyWithImpl<_$CreateOptionBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOptionBodyImplToJson(
      this,
    );
  }
}

abstract class _CreateOptionBody implements CreateOptionBody {
  const factory _CreateOptionBody(
      {final String? nameAr,
      final String? nameEn,
      final bool? isActive,
      final int? groupId,
      final int? id}) = _$CreateOptionBodyImpl;

  factory _CreateOptionBody.fromJson(Map<String, dynamic> json) =
      _$CreateOptionBodyImpl.fromJson;

  @override
  String? get nameAr;
  @override
  String? get nameEn;
  @override
  bool? get isActive;
  @override
  int? get groupId;
  @override
  int? get id;

  /// Create a copy of CreateOptionBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateOptionBodyImplCopyWith<_$CreateOptionBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
