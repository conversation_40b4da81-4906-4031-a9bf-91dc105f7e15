// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_product_addition_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EditProductAdditionBodyImpl _$$EditProductAdditionBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$EditProductAdditionBodyImpl(
      id: (json['id'] as num?)?.toInt(),
      additionId: (json['additionId'] as num?)?.toInt(),
      productOptionId: (json['productOptionId'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$$EditProductAdditionBodyImplToJson(
        _$EditProductAdditionBodyImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.additionId case final value?) 'additionId': value,
      if (instance.productOptionId case final value?) 'productOptionId': value,
      if (instance.price case final value?) 'price': value,
      if (instance.isActive case final value?) 'isActive': value,
    };
