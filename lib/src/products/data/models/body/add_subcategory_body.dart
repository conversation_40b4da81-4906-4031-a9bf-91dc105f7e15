import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_subcategory_body.freezed.dart';
part 'add_subcategory_body.g.dart';

@freezed
class AddSubcategoryBody with _$AddSubcategoryBody {
  const factory AddSubcategoryBody({
    int? parentId,
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? orderNumber,
  }) = _AddSubcategoryBody;

  factory AddSubcategoryBody.fromJson(Map<String, dynamic> json) =>
      _$AddSubcategoryBodyFromJson(json);
}
