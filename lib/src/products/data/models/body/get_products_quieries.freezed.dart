// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_products_quieries.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetProductsQuieries _$GetProductsQuieriesFromJson(Map<String, dynamic> json) {
  return _GetProductsQuieries.fromJson(json);
}

/// @nodoc
mixin _$GetProductsQuieries {
  int? get skip => throw _privateConstructorUsedError;
  int? get take => throw _privateConstructorUsedError;

  /// Serializes this GetProductsQuieries to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetProductsQuieries
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetProductsQuieriesCopyWith<GetProductsQuieries> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetProductsQuieriesCopyWith<$Res> {
  factory $GetProductsQuieriesCopyWith(
          GetProductsQuieries value, $Res Function(GetProductsQuieries) then) =
      _$GetProductsQuieriesCopyWithImpl<$Res, GetProductsQuieries>;
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class _$GetProductsQuieriesCopyWithImpl<$Res, $Val extends GetProductsQuieries>
    implements $GetProductsQuieriesCopyWith<$Res> {
  _$GetProductsQuieriesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetProductsQuieries
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_value.copyWith(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetProductsQuieriesImplCopyWith<$Res>
    implements $GetProductsQuieriesCopyWith<$Res> {
  factory _$$GetProductsQuieriesImplCopyWith(_$GetProductsQuieriesImpl value,
          $Res Function(_$GetProductsQuieriesImpl) then) =
      __$$GetProductsQuieriesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? skip, int? take});
}

/// @nodoc
class __$$GetProductsQuieriesImplCopyWithImpl<$Res>
    extends _$GetProductsQuieriesCopyWithImpl<$Res, _$GetProductsQuieriesImpl>
    implements _$$GetProductsQuieriesImplCopyWith<$Res> {
  __$$GetProductsQuieriesImplCopyWithImpl(_$GetProductsQuieriesImpl _value,
      $Res Function(_$GetProductsQuieriesImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetProductsQuieries
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skip = freezed,
    Object? take = freezed,
  }) {
    return _then(_$GetProductsQuieriesImpl(
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetProductsQuieriesImpl implements _GetProductsQuieries {
  const _$GetProductsQuieriesImpl({this.skip, this.take});

  factory _$GetProductsQuieriesImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetProductsQuieriesImplFromJson(json);

  @override
  final int? skip;
  @override
  final int? take;

  @override
  String toString() {
    return 'GetProductsQuieries(skip: $skip, take: $take)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetProductsQuieriesImpl &&
            (identical(other.skip, skip) || other.skip == skip) &&
            (identical(other.take, take) || other.take == take));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, skip, take);

  /// Create a copy of GetProductsQuieries
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetProductsQuieriesImplCopyWith<_$GetProductsQuieriesImpl> get copyWith =>
      __$$GetProductsQuieriesImplCopyWithImpl<_$GetProductsQuieriesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetProductsQuieriesImplToJson(
      this,
    );
  }
}

abstract class _GetProductsQuieries implements GetProductsQuieries {
  const factory _GetProductsQuieries({final int? skip, final int? take}) =
      _$GetProductsQuieriesImpl;

  factory _GetProductsQuieries.fromJson(Map<String, dynamic> json) =
      _$GetProductsQuieriesImpl.fromJson;

  @override
  int? get skip;
  @override
  int? get take;

  /// Create a copy of GetProductsQuieries
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetProductsQuieriesImplCopyWith<_$GetProductsQuieriesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
