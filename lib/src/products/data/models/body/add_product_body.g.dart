// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_product_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AddProductBodyImpl _$$AddProductBodyImplFromJson(Map<String, dynamic> json) =>
    _$AddProductBodyImpl(
      nameAr: json['nameAr'] as String?,
      nameEn: json['nameEn'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      descriptionEn: json['descriptionEn'] as String?,
      image: json['image'] as String?,
      isActive: json['isActive'] as bool?,
      hasOptions: json['hasOptions'] as bool?,
      categoriesId: (json['categoriesId'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      covers:
          (json['covers'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isForPoints: json['isForPoints'] as bool?,
      hasOffer: json['hasOffer'] as bool?,
      isAvailable: json['isAvailable'] as bool?,
      showOnMainPage: json['showOnMainPage'] as bool?,
      productOptions: (json['productOptions'] as List<dynamic>?)
          ?.map((e) => ProductOptionView.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AddProductBodyImplToJson(
        _$AddProductBodyImpl instance) =>
    <String, dynamic>{
      if (instance.nameAr case final value?) 'nameAr': value,
      if (instance.nameEn case final value?) 'nameEn': value,
      if (instance.descriptionAr case final value?) 'descriptionAr': value,
      if (instance.descriptionEn case final value?) 'descriptionEn': value,
      if (instance.image case final value?) 'image': value,
      if (instance.isActive case final value?) 'isActive': value,
      if (instance.hasOptions case final value?) 'hasOptions': value,
      if (instance.categoriesId case final value?) 'categoriesId': value,
      if (instance.covers case final value?) 'covers': value,
      if (instance.isForPoints case final value?) 'isForPoints': value,
      if (instance.hasOffer case final value?) 'hasOffer': value,
      if (instance.isAvailable case final value?) 'isAvailable': value,
      if (instance.showOnMainPage case final value?) 'showOnMainPage': value,
      if (instance.productOptions case final value?) 'productOptions': value,
    };
