import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_products_quieries.freezed.dart';
part 'get_products_quieries.g.dart';

@freezed
class GetProductsQuieries with _$GetProductsQuieries {
  const factory GetProductsQuieries({
    int? skip,
    int? take,
  }) = _GetProductsQuieries;

  factory GetProductsQuieries.fromJson(Map<String, dynamic> json) =>
      _$GetProductsQuieriesFromJson(json);
}
