import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_group_body.freezed.dart';
part 'edit_group_body.g.dart';

@freezed
class EditGroupBody with _$EditGroupBody {
  const factory EditGroupBody({
    String? nameAr,
    String? nameEn,
    bool? isActive,
    int? groupType,
  }) = _EditGroupBody;

  factory EditGroupBody.fromJson(Map<String, dynamic> json) =>
      _$EditGroupBodyFromJson(json);
}
