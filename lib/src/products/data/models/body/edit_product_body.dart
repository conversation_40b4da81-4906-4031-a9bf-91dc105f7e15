import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_product_body.freezed.dart';
part 'edit_product_body.g.dart';

@freezed
class EditProductBody with _$EditProductBody {
  const factory EditProductBody({
    int? productOptionId,
    bool? hasOptions,
    String? nameAr,
    String? nameEn,
    String? descriptionAr,
    String? descriptionEn,
    bool? isActive,
    bool? isAvailable,
    DateTime? discountExpiration,
    double? price,
    double? discount,
  }) = _EditProductBody;

  factory EditProductBody.fromJson(Map<String, dynamic> json) =>
      _$EditProductBodyFromJson(json);
}
