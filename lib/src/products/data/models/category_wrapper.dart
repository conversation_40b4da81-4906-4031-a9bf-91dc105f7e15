import 'dart:convert';

import 'package:alsarea_store/src/products/data/models/modal/product_option_view_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_wrapper.g.dart';

// final oprations = CategoryOperations();

// Categories

CategoryDataWrapper categoryDataWrapperFromJson(String str) =>
    CategoryDataWrapper.fromJson(json.decode(str));

String categoryDataWrapperToJson(CategoryDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class CategoryDataWrapper {
  @J<PERSON><PERSON>ey(name: "skip")
  int? skip;
  @Json<PERSON>ey(name: "take")
  int? take;
  @Json<PERSON>ey(name: "id")
  int? id;
  @Json<PERSON>ey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @Json<PERSON>ey(name: "image")
  String? image;
  @Json<PERSON>ey(name: "isActive")
  bool? isActive;
  @Json<PERSON>ey(name: "orderNumber")
  int? orderNumber;

  CategoryDataWrapper({
    this.skip,
    this.take = 10,
    this.id,
    this.nameAr,
    this.nameEn,
    this.image,
    this.isActive = true,
    this.orderNumber = 1,
  });

  factory CategoryDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$CategoryDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryDataWrapperToJson(this);
}

//Subcategories

SubcategoryDataWrapper subcategoryDataWrapperFromJson(String str) =>
    SubcategoryDataWrapper.fromJson(json.decode(str));

String subcategoryDataWrapperToJson(SubcategoryDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class SubcategoryDataWrapper {
  @JsonKey(name: "id")
  int? subId;
  @JsonKey(name: "parentId")
  int? id;
  @JsonKey(name: "nameAr")
  String? nameArSubcategory;
  @JsonKey(name: "nameEn")
  String? nameEnSubcategory;
  @JsonKey(name: "isActive")
  bool? isActiveSubcategory;
  @JsonKey(name: "orderNumber")
  int? orderNumberSubcategory;
  @JsonKey(name: "numberOfProducts")
  int? numberOfProducts;

  SubcategoryDataWrapper({
    this.subId,
    this.id,
    this.nameArSubcategory,
    this.nameEnSubcategory,
    this.isActiveSubcategory = true,
    this.orderNumberSubcategory = 1,
    this.numberOfProducts,
  });

  factory SubcategoryDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$SubcategoryDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$SubcategoryDataWrapperToJson(this);
}

// Products

ProductsDataWrapper productsDataWrapperFromJson(String str) =>
    ProductsDataWrapper.fromJson(json.decode(str));

String productsDataWrapperToJson(ProductsDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ProductsDataWrapper {
  @JsonKey(name: "skip")
  int? skip;
  @JsonKey(name: "take")
  int? take;
  @JsonKey(name: "search")
  String? search;
  @JsonKey(name: "sales")
  String? sales;
  @JsonKey(name: "byDate")
  String? byDate;
  @JsonKey(name: "discount")
  String? discount;
  @JsonKey(name: "categoryId")
  int? categoryId;
  @JsonKey(name: "subCategoryId")
  int? subCategoryId;

  ProductsDataWrapper({
    this.skip,
    this.take = 10,
    this.search = '',
    this.sales = '',
    this.byDate = '',
    this.discount = '',
    this.categoryId,
    this.subCategoryId,
  });

  factory ProductsDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ProductsDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ProductsDataWrapperToJson(this);
}

// Product

ProductDataWrapper productDataWrapperFromJson(String str) =>
    ProductDataWrapper.fromJson(json.decode(str));

String productDataWrapperToJson(ProductDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ProductDataWrapper {
  @JsonKey(name: "id")
  int? id;
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "descriptionAr")
  String? descriptionAr;
  @JsonKey(name: "descriptionEn")
  String? descriptionEn;
  @JsonKey(name: "image")
  String? image;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "hasOptions")
  bool? hasOptions;
  @JsonKey(name: "categoriesId")
  List<int>? categoriesId;
  @JsonKey(name: "covers")
  List<String>? covers;
  @JsonKey(name: "isForPoints")
  bool? isForPoints;
  @JsonKey(name: "hasOffer")
  bool? hasOffer;
  @JsonKey(name: "isAvailable")
  bool? isAvailable;
  @JsonKey(name: "showOnMainPage")
  bool? showOnMainPage;
  @JsonKey(name: "productOptions")
  List<ProductOptionView>? productOptions;
  @JsonKey(name: "defaultPrice")
  double? defaultPrice;
  @JsonKey(name: "discountType")
  int? discountType;
  @JsonKey(name: "discount")
  double? discount;
  @JsonKey(name: "discountExpiration")
  DateTime? discountExpiration;

  void clear() {
    id = null;
    nameAr = null;
    nameEn = null;
    descriptionAr = null;
    descriptionEn = null;
    image = null;
    isActive = true;
    hasOptions = true;
    categoriesId = null;
    covers = null;
    isForPoints = false;
    hasOffer = true;
    isAvailable = true;
    showOnMainPage = true;
    productOptions = null;
    defaultPrice = null;
    discountType = null;
    discount = null;
    discountExpiration = null;
  }

  ProductDataWrapper({
    this.id,
    this.nameAr,
    this.nameEn,
    this.descriptionAr,
    this.descriptionEn,
    this.image,
    this.isActive = true,
    this.hasOptions = true,
    this.categoriesId,
    this.covers,
    this.isForPoints = false,
    this.hasOffer = true,
    this.isAvailable = true,
    this.showOnMainPage = true,
    this.productOptions,
    this.defaultPrice,
    this.discountType,
    this.discount,
    this.discountExpiration,
  });
  factory ProductDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ProductDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ProductDataWrapperToJson(this);
}

// Product Option
ProductOptionDataWrapper productOptionDataWrapperFromJson(String str) =>
    ProductOptionDataWrapper.fromJson(json.decode(str));

String productOptionDataWrapperToJson(ProductOptionDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ProductOptionDataWrapper {
  @JsonKey(name: "productOptionId")
  int? productOptionId;
  @JsonKey(name: "productId")
  int? productId;
  @JsonKey(name: "optionIds")
  List<int>? optionIds;
  @JsonKey(name: "hasOptions")
  bool? hasOptions;
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "descriptionAr")
  String? descriptionAr;
  @JsonKey(name: "descriptionEn")
  String? descriptionEn;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "isAvailable")
  bool? isAvailable;
  @JsonKey(name: "discountExpiration")
  DateTime? discountExpiration;
  @JsonKey(name: "price")
  double? price;
  @JsonKey(name: "discount")
  double? discount;
  @JsonKey(name: "type")
  int? type;
  @JsonKey(name: "quantity")
  int? quantity;
  @JsonKey(name: "image")
  String? image;

  ProductOptionDataWrapper({
    this.productOptionId,
    this.productId,
    this.optionIds,
    this.hasOptions,
    this.nameAr,
    this.nameEn,
    this.descriptionAr,
    this.descriptionEn,
    this.isActive = true,
    this.isAvailable = true,
    this.discountExpiration,
    this.price,
    this.discount,
    this.type,
    this.quantity,
    this.image,
  });
  factory ProductOptionDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ProductOptionDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ProductOptionDataWrapperToJson(this);
}

// Product Addition
ProductAdditionDataWrapper productAdditionDataWrapperFromJson(String str) =>
    ProductAdditionDataWrapper.fromJson(json.decode(str));

String productAdditionDataWrapperToJson(ProductAdditionDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ProductAdditionDataWrapper {
  @JsonKey(name: "id")
  int? id;
  @JsonKey(name: "additionId")
  int? additionId;
  @JsonKey(name: "productOptionId")
  int? productOptionId;
  @JsonKey(name: "price")
  double? price;
  @JsonKey(name: "isActive")
  bool? isActive;

  ProductAdditionDataWrapper({
    this.id = 0,
    this.additionId,
    this.productOptionId,
    this.price,
    this.isActive,
  });
  factory ProductAdditionDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ProductAdditionDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ProductAdditionDataWrapperToJson(this);
}

// Product Addition
OptionDataWrapper optionDataWrapperFromJson(String str) =>
    OptionDataWrapper.fromJson(json.decode(str));

String optionDataWrapperToJson(OptionDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class OptionDataWrapper {
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "groupId")
  int? groupId;
  @JsonKey(name: "id")
  int? id;

  OptionDataWrapper({
    this.nameAr,
    this.nameEn,
    this.isActive = true,
    this.groupId,
    this.id,
  });
  factory OptionDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$OptionDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$OptionDataWrapperToJson(this);
}

// Profile
ProfileDataWrapper profileDataWrapperFromJson(String str) =>
    ProfileDataWrapper.fromJson(json.decode(str));

String profileDataWrapperToJson(ProfileDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ProfileDataWrapper {
  @JsonKey(name: "id")
  String? id;
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "phone")
  String? phone;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "logo")
  String? logo;
  @JsonKey(name: "cover")
  String? cover;
  @JsonKey(name: "address")
  String? address;
  @JsonKey(name: "cityId")
  int? cityId;
  @JsonKey(name: "lat")
  int? lat;
  @JsonKey(name: "lng")
  int? lng;
  @JsonKey(name: "appActivityId")
  int? appActivityId;
  @JsonKey(name: "storeRate")
  String? storeRate;

  ProfileDataWrapper({
    this.id,
    this.nameAr,
    this.nameEn,
    this.phone,
    this.logo,
    this.isActive,
    this.cover,
    this.address,
    this.cityId,
    this.lat,
    this.lng,
    this.appActivityId,
    this.storeRate,
  });
  factory ProfileDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ProfileDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ProfileDataWrapperToJson(this);
}

// Group
GroupDataWrapper groupDataWrapperFromJson(String str) =>
    GroupDataWrapper.fromJson(json.decode(str));

String groupDataWrapperToJson(GroupDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class GroupDataWrapper {
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "groupType")
  int? groupType;

  GroupDataWrapper({
    this.nameAr,
    this.nameEn,
    this.isActive = true,
    this.groupType = 0,
  });
  factory GroupDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$GroupDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$GroupDataWrapperToJson(this);
}

// Group
AdditionDataWrapper additionDataWrapperFromJson(String str) =>
    AdditionDataWrapper.fromJson(json.decode(str));

String additionDataWrapperToJson(AdditionDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class AdditionDataWrapper {
  @JsonKey(name: "nameAr")
  String? nameAr;
  @JsonKey(name: "nameEn")
  String? nameEn;
  @JsonKey(name: "isActive")
  bool? isActive;
  @JsonKey(name: "defaultPrice")
  int? defaultPrice;

  AdditionDataWrapper({
    this.nameAr,
    this.nameEn,
    this.isActive = true,
    this.defaultPrice = 0,
  });
  factory AdditionDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$AdditionDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$AdditionDataWrapperToJson(this);
}
