import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/subcategories_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'subcategories_list_response.g.dart';

@JsonSerializable()
class SubcategoriesListResponse extends BaseResponse {
  final List<SubcategoriesModel>? data;

  SubcategoriesListResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory SubcategoriesListResponse.fromJson(Map<String, dynamic> json) =>
      _$SubcategoriesListResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$SubcategoriesListResponseToJson(this);
}
