import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/city_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_cities_response.g.dart';

@JsonSerializable()
class GetCitiesResponse extends BaseResponse {
  final List<CityModel>? data;

  GetCitiesResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetCitiesResponse.fromJson(Map<String, dynamic> json) =>
      _$GetCitiesResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetCitiesResponseToJson(this);
}
