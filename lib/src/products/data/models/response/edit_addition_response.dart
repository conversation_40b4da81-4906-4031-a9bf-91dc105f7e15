import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/addition_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_addition_response.g.dart';

@JsonSerializable()
class EditAdditionResponse extends BaseResponse {
  final AdditionModel? data;

  EditAdditionResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory EditAdditionResponse.fromJson(Map<String, dynamic> json) =>
      _$EditAdditionResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EditAdditionResponseToJson(this);
}
