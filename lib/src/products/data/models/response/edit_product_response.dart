import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_product_response.g.dart';

@JsonSerializable()
class EditProductResponse extends BaseResponse {
  final ProductModel? data;

  EditProductResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory EditProductResponse.fromJson(Map<String, dynamic> json) =>
      _$EditProductResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EditProductResponseToJson(this);
}
