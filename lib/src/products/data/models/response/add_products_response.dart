import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_products_response.g.dart';

@JsonSerializable()
class AddProductResponse extends BaseResponse {
  final ProductModel? data;

  AddProductResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory AddProductResponse.fromJson(Map<String, dynamic> json) =>
      _$AddProductResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AddProductResponseToJson(this);
}
