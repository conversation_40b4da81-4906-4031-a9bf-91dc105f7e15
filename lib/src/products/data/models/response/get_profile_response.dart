import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/profile_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_profile_response.g.dart';

@JsonSerializable()
class GetProfileResponse extends BaseResponse {
  final ProfileModel? data;

  GetProfileResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$GetProfileResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetProfileResponseToJson(this);
}
