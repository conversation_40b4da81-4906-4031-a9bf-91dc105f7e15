import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_option_response.g.dart';

@JsonSerializable()
class CreateOptionResponse extends BaseResponse {
  final OptionModel? data;

  CreateOptionResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory CreateOptionResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateOptionResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CreateOptionResponseToJson(this);
}
