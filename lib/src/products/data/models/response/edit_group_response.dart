import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_group_response.g.dart';

@JsonSerializable()
class EditGroupResponse extends BaseResponse {
  final GroupModel? data;

  EditGroupResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory EditGroupResponse.fromJson(Map<String, dynamic> json) =>
      _$EditGroupResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EditGroupResponseToJson(this);
}
