import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/app_activity_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_app_activites_response.g.dart';

@JsonSerializable()
class GetAppActivitesResponse extends BaseResponse {
  final List<AppActivityModel>? data;

  GetAppActivitesResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetAppActivitesResponse.fromJson(Map<String, dynamic> json) =>
      _$GetAppActivitesResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetAppActivitesResponseToJson(this);
}
