import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_products_response.g.dart';

@JsonSerializable()
class GetProductsResponse extends BaseResponse {
  final List<ProductModel>? data;

  GetProductsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetProductsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetProductsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetProductsResponseToJson(this);
}
