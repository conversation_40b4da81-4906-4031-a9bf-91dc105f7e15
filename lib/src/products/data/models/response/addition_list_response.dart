import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/addition_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'addition_list_response.g.dart';

@JsonSerializable()
class AdditionListResponse extends BaseResponse {
  final List<AdditionModel>? data;

  AdditionListResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory AdditionListResponse.fromJson(Map<String, dynamic> json) =>
      _$AdditionListResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AdditionListResponseToJson(this);
}
