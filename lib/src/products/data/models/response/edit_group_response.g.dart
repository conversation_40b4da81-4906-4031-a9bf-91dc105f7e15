// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_group_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EditGroupResponse _$EditGroupResponseFromJson(Map<String, dynamic> json) =>
    EditGroupResponse(
      data: json['data'] == null
          ? null
          : GroupModel.fromJson(json['data'] as Map<String, dynamic>),
      errors: json['errors'],
      message: json['message'],
      status: (json['status'] as num?)?.toInt(),
      statusName: json['statusName'] as String?,
      debug: json['debug'],
      pages: json['pages'] == null
          ? null
          : PagesModel.fromJson(json['pages'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EditGroupResponseToJson(EditGroupResponse instance) =>
    <String, dynamic>{
      if (instance.status case final value?) 'status': value,
      if (instance.statusName case final value?) 'statusName': value,
      if (instance.message case final value?) 'message': value,
      if (instance.pages case final value?) 'pages': value,
      if (instance.debug case final value?) 'debug': value,
      if (instance.errors case final value?) 'errors': value,
      if (instance.data case final value?) 'data': value,
    };
