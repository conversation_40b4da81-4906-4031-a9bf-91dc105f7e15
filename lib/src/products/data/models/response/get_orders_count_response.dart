import 'package:alsarea_store/core/api/base_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_orders_count_response.g.dart';

@JsonSerializable()
class GetOrdersCountResponse extends BaseResponse {
  final int? data;

  GetOrdersCountResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetOrdersCountResponse.fromJson(Map<String, dynamic> json) =>
      _$GetOrdersCountResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetOrdersCountResponseToJson(this);
}
