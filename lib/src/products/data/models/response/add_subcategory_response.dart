import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_subcategory_response.g.dart';

@JsonSerializable()
class AddSubcategoryResponse extends BaseResponse {
  final SubcategoryModel? data;

  AddSubcategoryResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory AddSubcategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$AddSubcategoryResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AddSubcategoryResponseToJson(this);
}
