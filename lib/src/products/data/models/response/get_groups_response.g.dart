// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_groups_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetGroupsResponse _$GetGroupsResponseFromJson(Map<String, dynamic> json) =>
    GetGroupsResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => GroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      errors: json['errors'],
      message: json['message'],
      status: (json['status'] as num?)?.toInt(),
      statusName: json['statusName'] as String?,
      debug: json['debug'],
      pages: json['pages'] == null
          ? null
          : PagesModel.fromJson(json['pages'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetGroupsResponseToJson(GetGroupsResponse instance) =>
    <String, dynamic>{
      if (instance.status case final value?) 'status': value,
      if (instance.statusName case final value?) 'statusName': value,
      if (instance.message case final value?) 'message': value,
      if (instance.pages case final value?) 'pages': value,
      if (instance.debug case final value?) 'debug': value,
      if (instance.errors case final value?) 'errors': value,
      if (instance.data case final value?) 'data': value,
    };
