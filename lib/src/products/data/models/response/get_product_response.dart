import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_product_response.g.dart';

@JsonSerializable()
class GetProductResponse extends BaseResponse {
  final ProductModel? data;

  GetProductResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetProductResponse.fromJson(Map<String, dynamic> json) =>
      _$GetProductResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetProductResponseToJson(this);
}
