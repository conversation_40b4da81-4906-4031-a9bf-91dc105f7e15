import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_categories_response.g.dart';

@JsonSerializable()
class GetCategoriesResponse extends BaseResponse {
  final List<CategoryModel>? data;

  GetCategoriesResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetCategoriesResponse.fromJson(Map<String, dynamic> json) =>
      _$GetCategoriesResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetCategoriesResponseToJson(this);
}
