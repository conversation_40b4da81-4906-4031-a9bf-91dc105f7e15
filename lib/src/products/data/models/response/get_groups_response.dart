import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/group_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_groups_response.g.dart';

@JsonSerializable()
class GetGroupsResponse extends BaseResponse {
  final List<GroupModel>? data;

  GetGroupsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetGroupsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetGroupsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetGroupsResponseToJson(this);
}
