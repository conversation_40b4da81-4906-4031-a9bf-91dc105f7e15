import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/product_option_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_product_addition_response.g.dart';

@JsonSerializable()
class EditProductAdditionResponse extends BaseResponse {
  final ProductAdditionModel? data;

  EditProductAdditionResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory EditProductAdditionResponse.fromJson(Map<String, dynamic> json) =>
      _$EditProductAdditionResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EditProductAdditionResponseToJson(this);
}
