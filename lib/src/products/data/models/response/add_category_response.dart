import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/products/data/models/modal/category_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_category_response.g.dart';

@JsonSerializable()
class AddCategoryResponse extends BaseResponse {
  final CategoryModel? data;

  AddCategoryResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory AddCategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$AddCategoryResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AddCategoryResponseToJson(this);
}
