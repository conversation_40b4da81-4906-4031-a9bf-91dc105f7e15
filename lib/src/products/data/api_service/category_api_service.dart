import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/products/data/models/body/add_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_product_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/add_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/categories_body.dart';
import 'package:alsarea_store/src/products/data/models/body/create_option_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_category_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_group_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_addition_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_product_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_profile_body.dart';
import 'package:alsarea_store/src/products/data/models/body/edit_subcategory_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_groups_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_body.dart';
import 'package:alsarea_store/src/products/data/models/body/get_products_quieries.dart';
import 'package:alsarea_store/src/products/data/models/response/add_category_response.dart';
import 'package:alsarea_store/src/products/data/models/response/add_products_response.dart';
import 'package:alsarea_store/src/products/data/models/response/add_subcategory_response.dart';
import 'package:alsarea_store/src/products/data/models/response/addition_list_response.dart';
import 'package:alsarea_store/src/products/data/models/response/create_option_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_addition_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_group_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_product_addition_response.dart';
import 'package:alsarea_store/src/products/data/models/response/edit_product_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_app_activites_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_categories_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_cities_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_groups_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_orders_count_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_product_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_products_response.dart';
import 'package:alsarea_store/src/products/data/models/response/get_profile_response.dart';
import 'package:alsarea_store/src/products/data/models/response/subcategories_list_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'category_api_service.g.dart';

@RestApi()
abstract class CategoryApiService {
  factory CategoryApiService(
    Dio dio, {
    String baseUrl,
  }) = _CategoryApiService;

  @GET(EndPoints.categories)
  Future<GetCategoriesResponse> getCategories(@Queries() CategoriesBody body);

  @GET(EndPoints.categoryDetails)
  Future<AddCategoryResponse> getCategory(@Query('categoryId') int id);

  @POST(EndPoints.createCategory)
  Future<AddCategoryResponse> addCategory(@Body() AddCategoryBody body);

  @PUT(EndPoints.editCategory)
  Future<AddCategoryResponse> editCategory(@Body() EditCategoryBody body);

  @DELETE(EndPoints.deleteCategory)
  Future<BaseResponse> deleteCategory(@Query('categoryId') int id);

  @POST(EndPoints.createSubcategory)
  Future<AddSubcategoryResponse> addSubcategory(
      @Body() AddSubcategoryBody body);

  @PUT(EndPoints.editSubcategory)
  Future<AddSubcategoryResponse> editSubcategory(
      @Body() EditSubcategoryBody body);

  @DELETE(EndPoints.deleteSubcategory)
  Future<BaseResponse> deleteSubcategory(@Query('subcategoryId') int id);

  @GET(EndPoints.subcategoriesList)
  Future<SubcategoriesListResponse> subcategoriesList();

  // @GET(EndPoints.trademarksList)
  // Future<TrademarksListResponse> subcategoriesList();

  @GET(EndPoints.additionList)
  Future<AdditionListResponse> additionList();

  @POST(EndPoints.createAddition)
  Future<EditAdditionResponse> createAddition(@Body() EditAdditionBody body);

  @PUT(EndPoints.editAddition)
  Future<EditAdditionResponse> editAddition(
      @Body() EditAdditionBody body, @Query('additionId') int id);

  @DELETE(EndPoints.deleteAddition)
  Future<BaseResponse> deleteAddition(@Query('additionId') int id);

  @POST(EndPoints.products)
  Future<GetProductsResponse> getProducts(
      @Body() GetProductsBody body, @Queries() GetProductsQuieries queries);

  @GET('${EndPoints.singleProduct}/{id}')
  Future<GetProductResponse> getProduct(@Path('id') int id);

  @GET(EndPoints.getGroups)
  Future<GetGroupsResponse> getGroups(@Queries() GetGroupsBody body);

  @POST(EndPoints.createGroup)
  Future<EditGroupResponse> createGroup(@Body() EditGroupBody body);

  @PUT(EndPoints.editGroup)
  Future<EditGroupResponse> editGroup(
      @Body() EditGroupBody body, @Query('groupId') int id);

  @DELETE(EndPoints.deleteGroup)
  Future<BaseResponse> deleteGroup(@Query('groupId') int id);

  @POST(EndPoints.createProduct)
  Future<AddProductResponse> createProduct(@Body() AddProductBody body);

  @PUT(EndPoints.editProduct)
  Future<EditProductResponse> editProduct(@Body() EditProductBody body);

  @POST(EndPoints.createProductOption)
  Future<GetProductResponse> createProductOption(
      @Body() AddProductOptionBody body);

  @PUT(EndPoints.editProductAddition)
  Future<EditProductAdditionResponse> editProductAddition(
      @Body() EditProductAdditionBody body);

  @POST(EndPoints.createProductAddition)
  Future<EditProductAdditionResponse> createProductAddition(
      @Body() EditProductAdditionBody body);

  @POST(EndPoints.createOption)
  Future<CreateOptionResponse> createOption(@Body() CreateOptionBody body);

  @PUT(EndPoints.editOption)
  Future<CreateOptionResponse> editOption(@Body() CreateOptionBody body);

  @DELETE(EndPoints.deleteOption)
  Future<BaseResponse> deleteOption(@Query('optionId') int id);

  @GET(EndPoints.cities)
  Future<GetCitiesResponse> getCities();

  @GET(EndPoints.appActivities)
  Future<GetAppActivitesResponse> getAppActivites();

  @GET(EndPoints.getProfile)
  Future<GetProfileResponse> getProfile();

  @POST(EndPoints.editProfile)
  Future<GetProfileResponse> editProfile(@Body() EditProfileBody body);

  @GET(EndPoints.storeOrdersCount)
  Future<GetOrdersCountResponse> storeOrdersCount();
}
