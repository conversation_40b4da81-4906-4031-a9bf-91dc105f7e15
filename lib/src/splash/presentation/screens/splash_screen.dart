import 'dart:developer';

import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/auth/data/models/auth_operations.dart';
import 'package:alsarea_store/src/splash/presentation/cubit/splash_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class SplashScreen extends StatelessWidget implements AutoRouteWrapper {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<SplashCubit, SplashState>(
        listener: (context, state) {
          state.whenOrNull(
            success: () async {
              if (AuthOperations.isUserLoggedIn()) {
                final String? token =
                    await SharedPreferencesHelper.getSecuredString('token');
                log(token!);
                if (!context.mounted) return;
                context.router.replace(const BottomNavBarRoute());
              } else {
                context.router.replace(const LoginRoute());
              }
            },
            failure: (message) => UiHelper.onFailure(context, message),
          );
        },
        child: Assets.images.splash.image(
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<SplashCubit>()..getSplash(),
        child: this,
      );
}
