import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/src/more/presentation/screens/more_view.dart';
import 'package:alsarea_store/src/orders/presentation/cubit/orders_cubit.dart';
import 'package:alsarea_store/src/orders/presentation/screens/orders_view.dart';
import 'package:alsarea_store/src/products/presentation/cubit/category_cubit.dart';
import 'package:alsarea_store/src/products/presentation/screens/categories_view.dart';
import 'package:alsarea_store/src/products/presentation/screens/home_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bottom_nav_bar_cubit.freezed.dart';
part 'bottom_nav_bar_state.dart';

class BottomNavBarCubit extends Cubit<BottomNavBarState> {
  BottomNavBarCubit() : super(const BottomNavBarState.initial());

  final List<Widget> bodies = [
    BlocProvider.value(
      value: injector<CategoryCubit>(),
      child: const HomeView(),
    ),
    BlocProvider.value(
      value: injector<OrdersCubit>(),
      child: const OrdersView(),
    ),
    BlocProvider.value(
      value: injector<CategoryCubit>(),
      child: const CategoriesView(),
    ),
    const MoreView(),
  ];

  void changeIndex(int index) {
    emit(BottomNavBarState.changeIndex(index));
  }
}
