import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/cubit/bottom_nav_bar_cubit.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/widgets/default_bottom_nav_bar.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class BottomNavBarScreen extends StatefulWidget {
  const BottomNavBarScreen({super.key});

  @override
  State<BottomNavBarScreen> createState() => _BottomNavBarScreenState();
}

class _BottomNavBarScreenState extends State<BottomNavBarScreen> {
  @override
  void dispose() {
    injector<BottomNavBarCubit>().changeIndex(0);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
        builder: (context, state) {
          final index = state.maybeMap(
            orElse: () => 0,
            changeIndex: (indexState) => indexState.index,
          );
          return injector<BottomNavBarCubit>().bodies[index];
        },
      ),
      bottomNavigationBar: const DefaultBottomNavBar(),
    );
  }
}
