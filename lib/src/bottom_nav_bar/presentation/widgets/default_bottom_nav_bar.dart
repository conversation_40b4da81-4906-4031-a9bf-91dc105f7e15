import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/bottom_nav_bar/presentation/cubit/bottom_nav_bar_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DefaultBottomNavBar extends StatelessWidget {
  const DefaultBottomNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
      builder: (context, state) {
        final index = state.maybeWhen(
          changeIndex: (index) => index,
          orElse: () => 0,
        );
        return BottomNavigationBar(
          currentIndex: index,
          backgroundColor: AppColors.primary,
          type: BottomNavigationBarType.fixed,
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
          selectedItemColor: Colors.white,
          onTap: (index) {
            if (context.router.current.name != 'BottomNavBarRoute') {
              context.router.popUntilRoot();
            }
            context.read<BottomNavBarCubit>().changeIndex(index);
          },
          items: <BottomNavigationBarItem>[
            _buildBottomNavigationBarItem(
              label: context.l10n.home,
              icon: Assets.icons.home.svg(
                colorFilter: index == 0
                    ? const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      )
                    : null,
              ),
            ),
            _buildBottomNavigationBarItem(
              label: context.l10n.orders,
              icon: Assets.icons.orders.svg(
                colorFilter: index == 1
                    ? const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      )
                    : null,
              ),
            ),
            _buildBottomNavigationBarItem(
              label: context.l10n.products,
              icon: Assets.icons.products.svg(
                colorFilter: index == 2
                    ? const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      )
                    : null,
              ),
            ),
            _buildBottomNavigationBarItem(
              label: context.l10n.myAccount,
              icon: Assets.icons.person.svg(
                colorFilter: index == 3
                    ? const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      )
                    : null,
              ),
            ),
          ],
        );
      },
    );
  }

  BottomNavigationBarItem _buildBottomNavigationBarItem({
    required String label,
    required Widget icon,
  }) {
    return BottomNavigationBarItem(
      icon: Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: icon,
      ),
      label: label,
    );
  }
}
