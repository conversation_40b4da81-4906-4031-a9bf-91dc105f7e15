import 'package:alsarea_store/src/terms/data/terms_repo/terms_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'terms_cubit.freezed.dart';
part 'terms_state.dart';

class TermsCubit extends Cubit<TermsState> {
  final TermsRepo _termsRepo;
  TermsCubit(this._termsRepo) : super(const TermsState.initial());

  String? termsText;

  Future<void> getTerms() async {
    emit(const TermsState.loading());
    final response = await _termsRepo.getTerms();
    response.when(
      success: (result) {
        termsText = result.data?.termsAndConditions;
        emit(const TermsState.loaded());
      },
      failure: (message) => emit(
        TermsState.error(message),
      ),
    );
  }
}
