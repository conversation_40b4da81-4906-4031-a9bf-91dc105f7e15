import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/terms/presentation/screens/cubit/terms_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

@RoutePage()
class TermsScreen extends StatelessWidget implements AutoRouteWrapper {
  const TermsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.termsAndConditions),
      ),
      body: BlocBuilder<TermsCubit, TermsState>(
        builder: (context, state) {
          return state.maybeWhen(
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: () => SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: HtmlWidget(
                injector<TermsCubit>().termsText ?? '',
              ),
            ),
            error: (message) => Center(
              child: Text(message, style: context.textTheme.bodyLarge),
            ),
            orElse: () => const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
      value: injector<TermsCubit>(),
      child: this,
    );
  }
}
