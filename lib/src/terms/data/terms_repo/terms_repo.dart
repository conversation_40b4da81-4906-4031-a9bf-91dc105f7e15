import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/terms/data/models/terms_response.dart';
import 'package:alsarea_store/src/terms/data/terms_api_service/terms_api_service.dart';

class TermsRepo {
  final TermsApiService _termsApiService;

  TermsRepo(this._termsApiService);

  Future<Result<TermsResponse>> getTerms() async => errorHandlerAsync(() async {
        final response = await _termsApiService.getTerms();
        return response;
      });
}
