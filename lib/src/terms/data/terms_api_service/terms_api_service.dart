import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/terms/data/models/terms_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'terms_api_service.g.dart';

@RestApi()
abstract class TermsApiService {
  factory TermsApiService(
    Dio dio, {
    String baseUrl,
  }) = _TermsApiService;

  @GET(EndPoints.terms)
  Future<TermsResponse> getTerms();
}
