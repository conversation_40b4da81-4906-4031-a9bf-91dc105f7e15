//get orders response
import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/terms/data/models/terms_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'terms_response.g.dart';

@JsonSerializable()
class TermsResponse extends BaseResponse {
  final TermsModel? data;

  TermsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory TermsResponse.fromJson(Map<String, dynamic> json) =>
      _$TermsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$TermsResponseToJson(this);
}
