import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/helpers/shared_preferences_helper.dart';
import 'package:alsarea_store/core/helpers/ui_helper.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:alsarea_store/src/more/presentation/widgets/more_titled_section.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

class MoreView extends StatefulWidget {
  const MoreView({super.key});

  @override
  State<MoreView> createState() => _MoreViewState();
}

class _MoreViewState extends State<MoreView> {
  String? token = '';
  @override
  void initState() {
    super.initState();
    getToken();
  }

  void getToken() async {
    final String? token =
        await SharedPreferencesHelper.getSecuredString('token');
    setState(() {
      this.token = token;
    });
  }

  void checkNav(action) async {
    if (token == '') {
      context.router.push(const LoginRoute());
    } else {
      action;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(
          context.l10n.myAccount,
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          spacing: 24,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MoreTitledSection(
              title: context.l10n.personalInformation,
              child: MoreItem(
                icon: Assets.icons.userIcon.svg(),
                title: context.l10n.personalInformation,
                onTap: () =>
                    checkNav(context.router.push(const StoreDataRoute())),
              ),
            ),
            MoreTitledSection(
              title: context.l10n.choicesAndReports,
              child: Column(
                children: [
                  MoreItem(
                    icon: Assets.icons.ordersIcon.svg(),
                    title: context.l10n.choices,
                    onTap: () =>
                        checkNav(context.router.push(const ChoicesRoute())),
                  ),
                  MoreItem(
                    icon: Assets.icons.ordersIcon.svg(),
                    title: context.l10n.salesReport,
                    onTap: () =>
                        checkNav(context.router.push(const SalesReportRoute())),
                  ),
                ],
              ),
            ),
            // MoreTitledSection(
            //   title: context.l10n.appSettings,
            //   child: MoreItem(
            //     icon: Assets.icons.languageIcon.svg(),
            //     title: context.l10n.language,
            //     onTap: () => UiHelper.showCustomDialog<bool>(
            //       context: context,
            //       dialog: const ChangeLanguageDialog(),
            //     ),
            //   ),
            // ),
            MoreTitledSection(
              title: context.l10n.termsAndConditions,
              child: Column(
                children: [
                  MoreItem(
                    icon: Assets.icons.callusIcon.svg(),
                    title: context.l10n.complaintsSuggestionsAndCommunication,
                    onTap: () =>
                        checkNav(context.router.push(const ComplaintsRoute())),
                  ),
                  MoreItem(
                    icon: Assets.icons.termsIcon.svg(),
                    title: context.l10n.termsAndConditions,
                    onTap: () =>
                        checkNav(context.router.push(const TermsRoute())),
                  ),
                ],
              ),
            ),
            MoreTitledSection(
              title: context.l10n.accountDetails,
              child: Column(
                children: [
                  token == ''
                      ? const SizedBox()
                      : MoreItem(
                          icon: Assets.icons.deleteUserIcon.svg(),
                          title: context.l10n.deleteAccount,
                          onTap: () => {
                                UiHelper.showCustomDialogV1(
                                    context: context,
                                    onYesPressed: () {
                                      injector<AuthCubit>().logout();
                                      context.router
                                          .replaceAll(const [LoginRoute()]);
                                    },
                                    title: context.l10n.areYouSure)
                              }),
                  MoreItem(
                      icon: Assets.icons.logoutIcon.svg(),
                      title: token == ''
                          ? context.l10n.login
                          : context.l10n.logout,
                      onTap: () => checkNav(UiHelper.showCustomDialogV1(
                          context: context,
                          onYesPressed: () {
                            injector<AuthCubit>().logout();
                            context.router.replaceAll(const [LoginRoute()]);
                          },
                          title: context.l10n.areYouSure))),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MoreItem extends StatelessWidget {
  const MoreItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
  });

  final Widget icon;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
      onTap: onTap,
      leading: icon,
      title: Text(
        title,
        style: TextStyles.body16,
      ),
      trailing: Assets.icons.arrowIcon.svg(),
    );
  }
}
