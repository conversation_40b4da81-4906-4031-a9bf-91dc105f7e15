import 'package:alsarea_store/config/router/app_router.dart';
import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/localization/localization_cubit.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChangeLanguageDialog extends StatelessWidget {
  const ChangeLanguageDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 16),
          Assets.icons.languageIcon.svg(width: 96, height: 96),
          Text(
            context.l10n.changeLanguage,
            style: TextStyles.headline24,
          ),
          const SizedBox(height: 24),
          CustomElevatedButton(
            onPressed: () {
              context.read<LocalizationCubit>().toArabic();
              context.router.pushAndPopUntil(
                const SplashRoute(),
                predicate: (_) => false,
              );
            },
            backgroundColor: context.currentLocaleCode == 'ar'
                ? AppColors.primary
                : AppColors.lighterGrey,
            foregroundColor: context.currentLocaleCode == 'ar'
                ? Colors.white
                : AppColors.black,
            child: const Text('العربية'),
          ),
          const SizedBox(height: 8),
          CustomElevatedButton(
            onPressed: () {
              context.read<LocalizationCubit>().toEnglish();
              context.router.pushAndPopUntil(
                const SplashRoute(),
                predicate: (_) => false,
              );
            },
            backgroundColor: context.currentLocaleCode == 'en'
                ? AppColors.primary
                : AppColors.lighterGrey,
            foregroundColor: context.currentLocaleCode == 'en'
                ? Colors.white
                : AppColors.black,
            child: const Text('English'),
          ),
        ],
      ),
    );
  }
}
