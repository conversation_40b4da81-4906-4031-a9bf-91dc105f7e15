import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';

class MoreTitledSection extends StatelessWidget {
  const MoreTitledSection({
    super.key,
    required this.title,
    required this.child,
  });
  final String title;
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 4,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.body12.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: AppColors.primary.withValues(alpha: 0.03),
          ),
          child: child,
        ),
      ],
    );
  }
}
