import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:flutter/material.dart';

class DeleteAccountDialog extends StatelessWidget {
  const DeleteAccountDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog.adaptive(
      title: Text(
        context.l10n.deleteAccount,
        style: TextStyles.title20.copyWith(
          fontWeight: FontWeight.w700,
        ),
      ),
      content: Text(
        context.l10n.deleteAccountMsg,
        style: TextStyles.body14,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: Text(context.l10n.cancel),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, true),
          child: Text(
            context.l10n.delete,
            style: const TextStyle(color: AppColors.red),
          ),
        ),
      ],
    );
  }
}
