import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_elevated_button.dart';
import 'package:alsarea_store/core/widgets/custom/custom_text_field.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/complaints/presentation/cubit/settings_cubit.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ComplaintsScreen extends StatefulWidget implements AutoRouteWrapper {
  const ComplaintsScreen({super.key});

  @override
  State<ComplaintsScreen> createState() => _ComplaintsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<SettingsCubit>(),
        child: this,
      );
}

class _ComplaintsScreenState extends State<ComplaintsScreen> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    injector<SettingsCubit>().settings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(
          context.l10n.complaintsSuggestionsAndCommunication,
        ),
      ),
      body: BlocBuilder<SettingsCubit, SettingsState>(
        buildWhen: (previous, current) => current.maybeWhen(
          settingsSuccess: () => true,
          settingsFailure: (_) => true,
          settingsLoading: () => true,
          orElse: () => false,
        ),
        builder: (context, state) {
          return state.maybeWhen(
            settingsLoading: () =>
                const Center(child: CircularProgressIndicator()),
            settingsFailure: (message) => Center(
              child: Text(message, style: const TextStyle(color: Colors.red)),
            ),
            settingsSuccess: () {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      spacing: 16,
                      children: [
                        Assets.icons.complaints.svg(),
                        Expanded(
                          child: CustomElevatedButton(
                            onPressed: openWhatsApp,
                            child: Row(
                              spacing: 8,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Assets.icons.whatsapp.svg(),
                                Text(context.l10n.whatsapp),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    Text(
                      context.l10n.tellUsYourComplaintOrSuggestion,
                      style: TextStyles.body14.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Form(
                      key: _formKey,
                      child: CustomTextField(
                        hintText: context.l10n.tellUsYourComplaintOrSuggestion,
                        maxLines: 7,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return context.l10n.pleaseEnterTheStoreName;
                          }
                          return null;
                        },
                        onChanged: (value) {
                          injector<SettingsCubit>()
                              .oprations
                              .complaintsDataWrapper
                              .comment = value;
                        },
                      ),
                    ),
                    const SizedBox(height: 32),
                    CustomElevatedButton(
                      onPressed: _onSave,
                      child: Text(context.l10n.send),
                    ),
                  ],
                ),
              );
            },
            orElse: () => const SizedBox(),
          );
        },
      ),
    );
  }

  Future<void> openWhatsApp() async {
    final phone = injector<SettingsCubit>().oprations.settings.phone;
    final url = Uri.parse("https://wa.me/2$phone");

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch WhatsApp';
    }
  }

  void _onSave() {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    log(injector<SettingsCubit>()
        .oprations
        .complaintsDataWrapper
        .comment
        .toString());
    injector<SettingsCubit>().createComplaint();
  }
}
