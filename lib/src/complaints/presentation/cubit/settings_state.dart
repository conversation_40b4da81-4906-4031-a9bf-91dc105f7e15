part of 'settings_cubit.dart';

@unfreezed
class SettingsState with _$SettingsState {
  factory SettingsState.initial() = _Initial;
  // settings
  factory SettingsState.settingsLoading() = _SettingsLoading;
  factory SettingsState.settingsSuccess() = _SettingsSuccess;
  factory SettingsState.settingsFailure(String message) = _SettingsFailure;

  // complaint
  factory SettingsState.createComplaintLoading() = _CreateComplaintLoading;
  factory SettingsState.createComplaintSuccess() = _CreateComplaintSuccess;
  factory SettingsState.createComplaintFailure(String message) =
      _CreateComplaintFailure;
}
