import 'package:alsarea_store/src/complaints/data/models/create_complaint_body.dart';
import 'package:alsarea_store/src/complaints/data/models/settings_modal.dart';
import 'package:alsarea_store/src/complaints/data/repo/settings_repo.dart';
import 'package:alsarea_store/src/complaints/data/settings_operations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_cubit.freezed.dart';
part 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  final SettingsRepo _repo;
  SettingsCubit(this._repo) : super(SettingsState.initial());

  final oprations = SettingsOperations();
  final List<GlobalKey<FormState>> productFormKey =
      List.generate(3, (_) => GlobalKey<FormState>());

  Future<void> settings() async {
    emit(SettingsState.settingsLoading());
    final result = await _repo.settings();
    result.when(
        success: (result) => {
              oprations.settings = result.data ?? const SettingsModel(),
              emit(SettingsState.settingsSuccess()),
            },
        failure: (message) => emit(SettingsState.settingsFailure(message)));
  }

  Future<void> createComplaint() async {
    emit(SettingsState.settingsLoading());
    final body =
        CreateComplaintBody.fromJson(oprations.complaintsDataWrapper.toJson());
    final result = await _repo.createComplaint(body);
    result.when(
        success: (result) => {
              emit(SettingsState.settingsSuccess()),
            },
        failure: (message) => emit(SettingsState.settingsFailure(message)));
  }
}
