// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SettingsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsStateCopyWith<$Res> {
  factory $SettingsStateCopyWith(
          SettingsState value, $Res Function(SettingsState) then) =
      _$SettingsStateCopyWithImpl<$Res, SettingsState>;
}

/// @nodoc
class _$SettingsStateCopyWithImpl<$Res, $Val extends SettingsState>
    implements $SettingsStateCopyWith<$Res> {
  _$SettingsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  _$InitialImpl();

  @override
  String toString() {
    return 'SettingsState.initial()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements SettingsState {
  factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$SettingsLoadingImplCopyWith<$Res> {
  factory _$$SettingsLoadingImplCopyWith(_$SettingsLoadingImpl value,
          $Res Function(_$SettingsLoadingImpl) then) =
      __$$SettingsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SettingsLoadingImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$SettingsLoadingImpl>
    implements _$$SettingsLoadingImplCopyWith<$Res> {
  __$$SettingsLoadingImplCopyWithImpl(
      _$SettingsLoadingImpl _value, $Res Function(_$SettingsLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SettingsLoadingImpl implements _SettingsLoading {
  _$SettingsLoadingImpl();

  @override
  String toString() {
    return 'SettingsState.settingsLoading()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return settingsLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return settingsLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsLoading != null) {
      return settingsLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return settingsLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return settingsLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsLoading != null) {
      return settingsLoading(this);
    }
    return orElse();
  }
}

abstract class _SettingsLoading implements SettingsState {
  factory _SettingsLoading() = _$SettingsLoadingImpl;
}

/// @nodoc
abstract class _$$SettingsSuccessImplCopyWith<$Res> {
  factory _$$SettingsSuccessImplCopyWith(_$SettingsSuccessImpl value,
          $Res Function(_$SettingsSuccessImpl) then) =
      __$$SettingsSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SettingsSuccessImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$SettingsSuccessImpl>
    implements _$$SettingsSuccessImplCopyWith<$Res> {
  __$$SettingsSuccessImplCopyWithImpl(
      _$SettingsSuccessImpl _value, $Res Function(_$SettingsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SettingsSuccessImpl implements _SettingsSuccess {
  _$SettingsSuccessImpl();

  @override
  String toString() {
    return 'SettingsState.settingsSuccess()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return settingsSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return settingsSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsSuccess != null) {
      return settingsSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return settingsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return settingsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsSuccess != null) {
      return settingsSuccess(this);
    }
    return orElse();
  }
}

abstract class _SettingsSuccess implements SettingsState {
  factory _SettingsSuccess() = _$SettingsSuccessImpl;
}

/// @nodoc
abstract class _$$SettingsFailureImplCopyWith<$Res> {
  factory _$$SettingsFailureImplCopyWith(_$SettingsFailureImpl value,
          $Res Function(_$SettingsFailureImpl) then) =
      __$$SettingsFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$SettingsFailureImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$SettingsFailureImpl>
    implements _$$SettingsFailureImplCopyWith<$Res> {
  __$$SettingsFailureImplCopyWithImpl(
      _$SettingsFailureImpl _value, $Res Function(_$SettingsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$SettingsFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SettingsFailureImpl implements _SettingsFailure {
  _$SettingsFailureImpl(this.message);

  @override
  String message;

  @override
  String toString() {
    return 'SettingsState.settingsFailure(message: $message)';
  }

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsFailureImplCopyWith<_$SettingsFailureImpl> get copyWith =>
      __$$SettingsFailureImplCopyWithImpl<_$SettingsFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return settingsFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return settingsFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsFailure != null) {
      return settingsFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return settingsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return settingsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (settingsFailure != null) {
      return settingsFailure(this);
    }
    return orElse();
  }
}

abstract class _SettingsFailure implements SettingsState {
  factory _SettingsFailure(String message) = _$SettingsFailureImpl;

  String get message;
  set message(String value);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SettingsFailureImplCopyWith<_$SettingsFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateComplaintLoadingImplCopyWith<$Res> {
  factory _$$CreateComplaintLoadingImplCopyWith(
          _$CreateComplaintLoadingImpl value,
          $Res Function(_$CreateComplaintLoadingImpl) then) =
      __$$CreateComplaintLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateComplaintLoadingImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$CreateComplaintLoadingImpl>
    implements _$$CreateComplaintLoadingImplCopyWith<$Res> {
  __$$CreateComplaintLoadingImplCopyWithImpl(
      _$CreateComplaintLoadingImpl _value,
      $Res Function(_$CreateComplaintLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreateComplaintLoadingImpl implements _CreateComplaintLoading {
  _$CreateComplaintLoadingImpl();

  @override
  String toString() {
    return 'SettingsState.createComplaintLoading()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return createComplaintLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return createComplaintLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintLoading != null) {
      return createComplaintLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return createComplaintLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return createComplaintLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintLoading != null) {
      return createComplaintLoading(this);
    }
    return orElse();
  }
}

abstract class _CreateComplaintLoading implements SettingsState {
  factory _CreateComplaintLoading() = _$CreateComplaintLoadingImpl;
}

/// @nodoc
abstract class _$$CreateComplaintSuccessImplCopyWith<$Res> {
  factory _$$CreateComplaintSuccessImplCopyWith(
          _$CreateComplaintSuccessImpl value,
          $Res Function(_$CreateComplaintSuccessImpl) then) =
      __$$CreateComplaintSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateComplaintSuccessImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$CreateComplaintSuccessImpl>
    implements _$$CreateComplaintSuccessImplCopyWith<$Res> {
  __$$CreateComplaintSuccessImplCopyWithImpl(
      _$CreateComplaintSuccessImpl _value,
      $Res Function(_$CreateComplaintSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreateComplaintSuccessImpl implements _CreateComplaintSuccess {
  _$CreateComplaintSuccessImpl();

  @override
  String toString() {
    return 'SettingsState.createComplaintSuccess()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return createComplaintSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return createComplaintSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintSuccess != null) {
      return createComplaintSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return createComplaintSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return createComplaintSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintSuccess != null) {
      return createComplaintSuccess(this);
    }
    return orElse();
  }
}

abstract class _CreateComplaintSuccess implements SettingsState {
  factory _CreateComplaintSuccess() = _$CreateComplaintSuccessImpl;
}

/// @nodoc
abstract class _$$CreateComplaintFailureImplCopyWith<$Res> {
  factory _$$CreateComplaintFailureImplCopyWith(
          _$CreateComplaintFailureImpl value,
          $Res Function(_$CreateComplaintFailureImpl) then) =
      __$$CreateComplaintFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$CreateComplaintFailureImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$CreateComplaintFailureImpl>
    implements _$$CreateComplaintFailureImplCopyWith<$Res> {
  __$$CreateComplaintFailureImplCopyWithImpl(
      _$CreateComplaintFailureImpl _value,
      $Res Function(_$CreateComplaintFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$CreateComplaintFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CreateComplaintFailureImpl implements _CreateComplaintFailure {
  _$CreateComplaintFailureImpl(this.message);

  @override
  String message;

  @override
  String toString() {
    return 'SettingsState.createComplaintFailure(message: $message)';
  }

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateComplaintFailureImplCopyWith<_$CreateComplaintFailureImpl>
      get copyWith => __$$CreateComplaintFailureImplCopyWithImpl<
          _$CreateComplaintFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() settingsLoading,
    required TResult Function() settingsSuccess,
    required TResult Function(String message) settingsFailure,
    required TResult Function() createComplaintLoading,
    required TResult Function() createComplaintSuccess,
    required TResult Function(String message) createComplaintFailure,
  }) {
    return createComplaintFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? settingsLoading,
    TResult? Function()? settingsSuccess,
    TResult? Function(String message)? settingsFailure,
    TResult? Function()? createComplaintLoading,
    TResult? Function()? createComplaintSuccess,
    TResult? Function(String message)? createComplaintFailure,
  }) {
    return createComplaintFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? settingsLoading,
    TResult Function()? settingsSuccess,
    TResult Function(String message)? settingsFailure,
    TResult Function()? createComplaintLoading,
    TResult Function()? createComplaintSuccess,
    TResult Function(String message)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintFailure != null) {
      return createComplaintFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SettingsLoading value) settingsLoading,
    required TResult Function(_SettingsSuccess value) settingsSuccess,
    required TResult Function(_SettingsFailure value) settingsFailure,
    required TResult Function(_CreateComplaintLoading value)
        createComplaintLoading,
    required TResult Function(_CreateComplaintSuccess value)
        createComplaintSuccess,
    required TResult Function(_CreateComplaintFailure value)
        createComplaintFailure,
  }) {
    return createComplaintFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SettingsLoading value)? settingsLoading,
    TResult? Function(_SettingsSuccess value)? settingsSuccess,
    TResult? Function(_SettingsFailure value)? settingsFailure,
    TResult? Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult? Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult? Function(_CreateComplaintFailure value)? createComplaintFailure,
  }) {
    return createComplaintFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SettingsLoading value)? settingsLoading,
    TResult Function(_SettingsSuccess value)? settingsSuccess,
    TResult Function(_SettingsFailure value)? settingsFailure,
    TResult Function(_CreateComplaintLoading value)? createComplaintLoading,
    TResult Function(_CreateComplaintSuccess value)? createComplaintSuccess,
    TResult Function(_CreateComplaintFailure value)? createComplaintFailure,
    required TResult orElse(),
  }) {
    if (createComplaintFailure != null) {
      return createComplaintFailure(this);
    }
    return orElse();
  }
}

abstract class _CreateComplaintFailure implements SettingsState {
  factory _CreateComplaintFailure(String message) =
      _$CreateComplaintFailureImpl;

  String get message;
  set message(String value);

  /// Create a copy of SettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateComplaintFailureImplCopyWith<_$CreateComplaintFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
