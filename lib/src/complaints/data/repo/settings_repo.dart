import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/complaints/data/api_service/settings_api_service.dart';
import 'package:alsarea_store/src/complaints/data/models/create_complaint_body.dart';
import 'package:alsarea_store/src/complaints/data/models/settings_response.dart';

class SettingsRepo {
  final SettingsApiService _apiService;
  SettingsRepo(this._apiService);

  Future<Result<SettingsResponse>> settings() =>
      errorHandlerAsync(() => _apiService.settings());

  Future<Result<BaseResponse>> createComplaint(CreateComplaintBody body) =>
      errorHandlerAsync(() => _apiService.createComplaint(body));
}
