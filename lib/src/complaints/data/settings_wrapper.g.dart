// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_wrapper.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ComplaintsDataWrapper _$ComplaintsDataWrapperFromJson(
        Map<String, dynamic> json) =>
    ComplaintsDataWrapper(
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$ComplaintsDataWrapperToJson(
        ComplaintsDataWrapper instance) =>
    <String, dynamic>{
      if (instance.comment case final value?) 'comment': value,
    };
