import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/complaints/data/models/create_complaint_body.dart';
import 'package:alsarea_store/src/complaints/data/models/settings_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'settings_api_service.g.dart';

@RestApi()
abstract class SettingsApiService {
  factory SettingsApiService(Dio dio, {String baseUrl}) = _SettingsApiService;

  @GET(EndPoints.contactSettings)
  Future<SettingsResponse> settings();

  @POST(EndPoints.createComplaint)
  Future<BaseResponse> createComplaint(@Queries() CreateComplaintBody body);
}
