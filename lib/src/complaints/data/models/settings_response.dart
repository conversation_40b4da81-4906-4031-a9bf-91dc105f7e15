import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/complaints/data/models/settings_modal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_response.g.dart';

@JsonSerializable()
class SettingsResponse extends BaseResponse {
  final SettingsModel? data;

  SettingsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory SettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$SettingsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$SettingsResponseToJson(this);
}
