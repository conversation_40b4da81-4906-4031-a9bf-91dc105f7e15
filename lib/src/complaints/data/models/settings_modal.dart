import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_modal.freezed.dart';
part 'settings_modal.g.dart';

SettingsModel settingsModelFromJson(String str) =>
    SettingsModel.fromJson(json.decode(str));

String settingsModelToJson(SettingsModel data) => json.encode(data.toJson());

@freezed
class SettingsModel with _$SettingsModel {
  const factory SettingsModel({
    @Json<PERSON>ey(name: "facebook") String? facebook,
    @<PERSON><PERSON><PERSON>ey(name: "phone") String? phone,
    @J<PERSON><PERSON><PERSON>(name: "whatsapp") String? whatsapp,
    @J<PERSON><PERSON><PERSON>(name: "instagram") String? instagram,
    @<PERSON><PERSON><PERSON><PERSON>(name: "twitter") String? twitter,
    @J<PERSON><PERSON><PERSON>(name: "snapchat") String? snapchat,
    @JsonKey(name: "tikTok") String? tikTok,
    @<PERSON><PERSON><PERSON><PERSON>(name: "supportsMultipleRestaurants")
    bool? supportsMultipleRestaurants,
    @<PERSON><PERSON><PERSON><PERSON>(name: "cod") String? cod,
    @J<PERSON><PERSON><PERSON>(name: "deliveryValue") String? deliveryValue,
  }) = _SettingsModel;

  factory SettingsModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsModelFromJson(json);
}
