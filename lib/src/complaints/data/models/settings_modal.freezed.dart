// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_modal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingsModel _$SettingsModelFromJson(Map<String, dynamic> json) {
  return _SettingsModel.fromJson(json);
}

/// @nodoc
mixin _$SettingsModel {
  @JsonKey(name: "facebook")
  String? get facebook => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "whatsapp")
  String? get whatsapp => throw _privateConstructorUsedError;
  @JsonKey(name: "instagram")
  String? get instagram => throw _privateConstructorUsedError;
  @JsonKey(name: "twitter")
  String? get twitter => throw _privateConstructorUsedError;
  @JsonKey(name: "snapchat")
  String? get snapchat => throw _privateConstructorUsedError;
  @JsonKey(name: "tikTok")
  String? get tikTok => throw _privateConstructorUsedError;
  @JsonKey(name: "supportsMultipleRestaurants")
  bool? get supportsMultipleRestaurants => throw _privateConstructorUsedError;
  @JsonKey(name: "cod")
  String? get cod => throw _privateConstructorUsedError;
  @JsonKey(name: "deliveryValue")
  String? get deliveryValue => throw _privateConstructorUsedError;

  /// Serializes this SettingsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SettingsModelCopyWith<SettingsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsModelCopyWith<$Res> {
  factory $SettingsModelCopyWith(
          SettingsModel value, $Res Function(SettingsModel) then) =
      _$SettingsModelCopyWithImpl<$Res, SettingsModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "facebook") String? facebook,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "whatsapp") String? whatsapp,
      @JsonKey(name: "instagram") String? instagram,
      @JsonKey(name: "twitter") String? twitter,
      @JsonKey(name: "snapchat") String? snapchat,
      @JsonKey(name: "tikTok") String? tikTok,
      @JsonKey(name: "supportsMultipleRestaurants")
      bool? supportsMultipleRestaurants,
      @JsonKey(name: "cod") String? cod,
      @JsonKey(name: "deliveryValue") String? deliveryValue});
}

/// @nodoc
class _$SettingsModelCopyWithImpl<$Res, $Val extends SettingsModel>
    implements $SettingsModelCopyWith<$Res> {
  _$SettingsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? facebook = freezed,
    Object? phone = freezed,
    Object? whatsapp = freezed,
    Object? instagram = freezed,
    Object? twitter = freezed,
    Object? snapchat = freezed,
    Object? tikTok = freezed,
    Object? supportsMultipleRestaurants = freezed,
    Object? cod = freezed,
    Object? deliveryValue = freezed,
  }) {
    return _then(_value.copyWith(
      facebook: freezed == facebook
          ? _value.facebook
          : facebook // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsapp: freezed == whatsapp
          ? _value.whatsapp
          : whatsapp // ignore: cast_nullable_to_non_nullable
              as String?,
      instagram: freezed == instagram
          ? _value.instagram
          : instagram // ignore: cast_nullable_to_non_nullable
              as String?,
      twitter: freezed == twitter
          ? _value.twitter
          : twitter // ignore: cast_nullable_to_non_nullable
              as String?,
      snapchat: freezed == snapchat
          ? _value.snapchat
          : snapchat // ignore: cast_nullable_to_non_nullable
              as String?,
      tikTok: freezed == tikTok
          ? _value.tikTok
          : tikTok // ignore: cast_nullable_to_non_nullable
              as String?,
      supportsMultipleRestaurants: freezed == supportsMultipleRestaurants
          ? _value.supportsMultipleRestaurants
          : supportsMultipleRestaurants // ignore: cast_nullable_to_non_nullable
              as bool?,
      cod: freezed == cod
          ? _value.cod
          : cod // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingsModelImplCopyWith<$Res>
    implements $SettingsModelCopyWith<$Res> {
  factory _$$SettingsModelImplCopyWith(
          _$SettingsModelImpl value, $Res Function(_$SettingsModelImpl) then) =
      __$$SettingsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "facebook") String? facebook,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "whatsapp") String? whatsapp,
      @JsonKey(name: "instagram") String? instagram,
      @JsonKey(name: "twitter") String? twitter,
      @JsonKey(name: "snapchat") String? snapchat,
      @JsonKey(name: "tikTok") String? tikTok,
      @JsonKey(name: "supportsMultipleRestaurants")
      bool? supportsMultipleRestaurants,
      @JsonKey(name: "cod") String? cod,
      @JsonKey(name: "deliveryValue") String? deliveryValue});
}

/// @nodoc
class __$$SettingsModelImplCopyWithImpl<$Res>
    extends _$SettingsModelCopyWithImpl<$Res, _$SettingsModelImpl>
    implements _$$SettingsModelImplCopyWith<$Res> {
  __$$SettingsModelImplCopyWithImpl(
      _$SettingsModelImpl _value, $Res Function(_$SettingsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? facebook = freezed,
    Object? phone = freezed,
    Object? whatsapp = freezed,
    Object? instagram = freezed,
    Object? twitter = freezed,
    Object? snapchat = freezed,
    Object? tikTok = freezed,
    Object? supportsMultipleRestaurants = freezed,
    Object? cod = freezed,
    Object? deliveryValue = freezed,
  }) {
    return _then(_$SettingsModelImpl(
      facebook: freezed == facebook
          ? _value.facebook
          : facebook // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsapp: freezed == whatsapp
          ? _value.whatsapp
          : whatsapp // ignore: cast_nullable_to_non_nullable
              as String?,
      instagram: freezed == instagram
          ? _value.instagram
          : instagram // ignore: cast_nullable_to_non_nullable
              as String?,
      twitter: freezed == twitter
          ? _value.twitter
          : twitter // ignore: cast_nullable_to_non_nullable
              as String?,
      snapchat: freezed == snapchat
          ? _value.snapchat
          : snapchat // ignore: cast_nullable_to_non_nullable
              as String?,
      tikTok: freezed == tikTok
          ? _value.tikTok
          : tikTok // ignore: cast_nullable_to_non_nullable
              as String?,
      supportsMultipleRestaurants: freezed == supportsMultipleRestaurants
          ? _value.supportsMultipleRestaurants
          : supportsMultipleRestaurants // ignore: cast_nullable_to_non_nullable
              as bool?,
      cod: freezed == cod
          ? _value.cod
          : cod // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryValue: freezed == deliveryValue
          ? _value.deliveryValue
          : deliveryValue // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingsModelImpl implements _SettingsModel {
  const _$SettingsModelImpl(
      {@JsonKey(name: "facebook") this.facebook,
      @JsonKey(name: "phone") this.phone,
      @JsonKey(name: "whatsapp") this.whatsapp,
      @JsonKey(name: "instagram") this.instagram,
      @JsonKey(name: "twitter") this.twitter,
      @JsonKey(name: "snapchat") this.snapchat,
      @JsonKey(name: "tikTok") this.tikTok,
      @JsonKey(name: "supportsMultipleRestaurants")
      this.supportsMultipleRestaurants,
      @JsonKey(name: "cod") this.cod,
      @JsonKey(name: "deliveryValue") this.deliveryValue});

  factory _$SettingsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingsModelImplFromJson(json);

  @override
  @JsonKey(name: "facebook")
  final String? facebook;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "whatsapp")
  final String? whatsapp;
  @override
  @JsonKey(name: "instagram")
  final String? instagram;
  @override
  @JsonKey(name: "twitter")
  final String? twitter;
  @override
  @JsonKey(name: "snapchat")
  final String? snapchat;
  @override
  @JsonKey(name: "tikTok")
  final String? tikTok;
  @override
  @JsonKey(name: "supportsMultipleRestaurants")
  final bool? supportsMultipleRestaurants;
  @override
  @JsonKey(name: "cod")
  final String? cod;
  @override
  @JsonKey(name: "deliveryValue")
  final String? deliveryValue;

  @override
  String toString() {
    return 'SettingsModel(facebook: $facebook, phone: $phone, whatsapp: $whatsapp, instagram: $instagram, twitter: $twitter, snapchat: $snapchat, tikTok: $tikTok, supportsMultipleRestaurants: $supportsMultipleRestaurants, cod: $cod, deliveryValue: $deliveryValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingsModelImpl &&
            (identical(other.facebook, facebook) ||
                other.facebook == facebook) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.whatsapp, whatsapp) ||
                other.whatsapp == whatsapp) &&
            (identical(other.instagram, instagram) ||
                other.instagram == instagram) &&
            (identical(other.twitter, twitter) || other.twitter == twitter) &&
            (identical(other.snapchat, snapchat) ||
                other.snapchat == snapchat) &&
            (identical(other.tikTok, tikTok) || other.tikTok == tikTok) &&
            (identical(other.supportsMultipleRestaurants,
                    supportsMultipleRestaurants) ||
                other.supportsMultipleRestaurants ==
                    supportsMultipleRestaurants) &&
            (identical(other.cod, cod) || other.cod == cod) &&
            (identical(other.deliveryValue, deliveryValue) ||
                other.deliveryValue == deliveryValue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      facebook,
      phone,
      whatsapp,
      instagram,
      twitter,
      snapchat,
      tikTok,
      supportsMultipleRestaurants,
      cod,
      deliveryValue);

  /// Create a copy of SettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsModelImplCopyWith<_$SettingsModelImpl> get copyWith =>
      __$$SettingsModelImplCopyWithImpl<_$SettingsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingsModelImplToJson(
      this,
    );
  }
}

abstract class _SettingsModel implements SettingsModel {
  const factory _SettingsModel(
          {@JsonKey(name: "facebook") final String? facebook,
          @JsonKey(name: "phone") final String? phone,
          @JsonKey(name: "whatsapp") final String? whatsapp,
          @JsonKey(name: "instagram") final String? instagram,
          @JsonKey(name: "twitter") final String? twitter,
          @JsonKey(name: "snapchat") final String? snapchat,
          @JsonKey(name: "tikTok") final String? tikTok,
          @JsonKey(name: "supportsMultipleRestaurants")
          final bool? supportsMultipleRestaurants,
          @JsonKey(name: "cod") final String? cod,
          @JsonKey(name: "deliveryValue") final String? deliveryValue}) =
      _$SettingsModelImpl;

  factory _SettingsModel.fromJson(Map<String, dynamic> json) =
      _$SettingsModelImpl.fromJson;

  @override
  @JsonKey(name: "facebook")
  String? get facebook;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "whatsapp")
  String? get whatsapp;
  @override
  @JsonKey(name: "instagram")
  String? get instagram;
  @override
  @JsonKey(name: "twitter")
  String? get twitter;
  @override
  @JsonKey(name: "snapchat")
  String? get snapchat;
  @override
  @JsonKey(name: "tikTok")
  String? get tikTok;
  @override
  @JsonKey(name: "supportsMultipleRestaurants")
  bool? get supportsMultipleRestaurants;
  @override
  @JsonKey(name: "cod")
  String? get cod;
  @override
  @JsonKey(name: "deliveryValue")
  String? get deliveryValue;

  /// Create a copy of SettingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SettingsModelImplCopyWith<_$SettingsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
