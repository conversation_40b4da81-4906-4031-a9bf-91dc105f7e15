import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_complaint_body.freezed.dart';
part 'create_complaint_body.g.dart';

@freezed
class CreateComplaintBody with _$CreateComplaintBody {
  const factory CreateComplaintBody({
    String? comment,
  }) = _CreateComplaintBody;

  factory CreateComplaintBody.fromJson(Map<String, dynamic> json) =>
      _$CreateComplaintBodyFromJson(json);
}
