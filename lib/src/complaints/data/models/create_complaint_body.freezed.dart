// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_complaint_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateComplaintBody _$CreateComplaintBodyFromJson(Map<String, dynamic> json) {
  return _CreateComplaintBody.fromJson(json);
}

/// @nodoc
mixin _$CreateComplaintBody {
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this CreateComplaintBody to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateComplaintBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateComplaintBodyCopyWith<CreateComplaintBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateComplaintBodyCopyWith<$Res> {
  factory $CreateComplaintBodyCopyWith(
          CreateComplaintBody value, $Res Function(CreateComplaintBody) then) =
      _$CreateComplaintBodyCopyWithImpl<$Res, CreateComplaintBody>;
  @useResult
  $Res call({String? comment});
}

/// @nodoc
class _$CreateComplaintBodyCopyWithImpl<$Res, $Val extends CreateComplaintBody>
    implements $CreateComplaintBodyCopyWith<$Res> {
  _$CreateComplaintBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateComplaintBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateComplaintBodyImplCopyWith<$Res>
    implements $CreateComplaintBodyCopyWith<$Res> {
  factory _$$CreateComplaintBodyImplCopyWith(_$CreateComplaintBodyImpl value,
          $Res Function(_$CreateComplaintBodyImpl) then) =
      __$$CreateComplaintBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? comment});
}

/// @nodoc
class __$$CreateComplaintBodyImplCopyWithImpl<$Res>
    extends _$CreateComplaintBodyCopyWithImpl<$Res, _$CreateComplaintBodyImpl>
    implements _$$CreateComplaintBodyImplCopyWith<$Res> {
  __$$CreateComplaintBodyImplCopyWithImpl(_$CreateComplaintBodyImpl _value,
      $Res Function(_$CreateComplaintBodyImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateComplaintBody
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? comment = freezed,
  }) {
    return _then(_$CreateComplaintBodyImpl(
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateComplaintBodyImpl implements _CreateComplaintBody {
  const _$CreateComplaintBodyImpl({this.comment});

  factory _$CreateComplaintBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateComplaintBodyImplFromJson(json);

  @override
  final String? comment;

  @override
  String toString() {
    return 'CreateComplaintBody(comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateComplaintBodyImpl &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, comment);

  /// Create a copy of CreateComplaintBody
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateComplaintBodyImplCopyWith<_$CreateComplaintBodyImpl> get copyWith =>
      __$$CreateComplaintBodyImplCopyWithImpl<_$CreateComplaintBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateComplaintBodyImplToJson(
      this,
    );
  }
}

abstract class _CreateComplaintBody implements CreateComplaintBody {
  const factory _CreateComplaintBody({final String? comment}) =
      _$CreateComplaintBodyImpl;

  factory _CreateComplaintBody.fromJson(Map<String, dynamic> json) =
      _$CreateComplaintBodyImpl.fromJson;

  @override
  String? get comment;

  /// Create a copy of CreateComplaintBody
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateComplaintBodyImplCopyWith<_$CreateComplaintBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
