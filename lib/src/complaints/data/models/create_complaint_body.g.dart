// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_complaint_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateComplaintBodyImpl _$$CreateComplaintBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateComplaintBodyImpl(
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$CreateComplaintBodyImplToJson(
        _$CreateComplaintBodyImpl instance) =>
    <String, dynamic>{
      if (instance.comment case final value?) 'comment': value,
    };
