import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_wrapper.g.dart';

ComplaintsDataWrapper complaintsDataWrapperFromJson(String str) =>
    ComplaintsDataWrapper.fromJson(json.decode(str));

String complaintsDataWrapperToJson(ComplaintsDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable(explicitToJson: true)
class ComplaintsDataWrapper {
  @JsonKey(name: "comment")
  String? comment;

  ComplaintsDataWrapper({
    this.comment,
  });

  factory ComplaintsDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ComplaintsDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ComplaintsDataWrapperToJson(this);
}
