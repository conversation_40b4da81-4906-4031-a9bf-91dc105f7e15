import 'dart:developer';

import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/services/injection_container.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/core/widgets/custom/custom_refresh_widget.dart';
import 'package:alsarea_store/core/widgets/shared/default_app_bar.dart';
import 'package:alsarea_store/src/sales_report/presentation/cubit/reports_cubit.dart';
import 'package:alsarea_store/src/sales_report/widgets/sale_report_card.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

@RoutePage()
class SalesReportScreen extends StatelessWidget implements AutoRouteWrapper {
  const SalesReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(
        context,
        title: Text(context.l10n.salesReport),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: BlocBuilder<ReportsCubit, ReportsState>(
          builder: (context, state) {
            return state.maybeWhen(
                orElse: () => const SizedBox(),
                getReportsLoading: () =>
                    const Center(child: CircularProgressIndicator()),
                getReportsError: (msg) => Center(
                      child: Text(
                        msg,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                getReportsSuccess: () {
                  final cubit = injector<ReportsCubit>();
                  log(cubit.operations.reportsList.toString());
                  return Column(
                    spacing: 16,
                    children: [
                      ExpansionTile(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide.none,
                        ),
                        collapsedShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide.none,
                        ),
                        backgroundColor:
                            AppColors.primary.withValues(alpha: 0.1),
                        collapsedBackgroundColor:
                            AppColors.primary.withValues(alpha: 0.1),
                        title: Text(
                          context.l10n.totals,
                          style: TextStyles.title16.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 18.sp,
                          ),
                        ),
                        childrenPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        children: [
                          TotalsItem(
                            label: context.l10n.totalOrdersNumber,
                            value: cubit.operations.report?.totalOrdersCount
                                    .toString() ??
                                '0',
                            unit: context.l10n.order,
                          ),
                          TotalsItem(
                            label: context.l10n.totalSales,
                            value: cubit.operations.report?.totalSales
                                    .toString() ??
                                '0',
                            unit: context.l10n.egp,
                          ),
                          TotalsItem(
                            label: context.l10n.commissionAlsarea,
                            value: cubit
                                    .operations.report?.totalCompanyCommission
                                    .toString() ??
                                '0',
                            unit: context.l10n.egp,
                          ),
                        ],
                      ),
                      Expanded(
                        child: CustomRefreshWidget(
                          onRefresh: () async {
                            await cubit.getReports();
                          },
                          onLoading: () async {
                            await cubit.getReports(isLoadMore: true);
                          },
                          child: ListView.separated(
                            itemCount: cubit.operations.reportsList.length,
                            separatorBuilder: (_, __) =>
                                const SizedBox(height: 16),
                            itemBuilder: (_, index) => SaleReportCard(
                              report: cubit.operations.reportsList[index],
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                });
          },
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<ReportsCubit>()..getReports(),
        child: this,
      );
}

class TotalsItem extends StatelessWidget {
  const TotalsItem({
    super.key,
    required this.label,
    required this.value,
    this.unit,
  });

  final String label;
  final String value;
  final String? unit;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyles.body16,
        ),
        Text(
          '$value ${unit ?? context.l10n.egp}',
          style: TextStyles.body16,
        ),
      ],
    );
  }
}
