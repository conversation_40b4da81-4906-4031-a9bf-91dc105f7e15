import 'dart:developer';

import 'package:alsarea_store/src/sales_report/data/models/get_reports_params.dart';
import 'package:alsarea_store/src/sales_report/data/repo/reports_repo.dart';
import 'package:alsarea_store/src/sales_report/presentation/cubit/reports_operations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'reports_cubit.freezed.dart';
part 'reports_state.dart';

class ReportsCubit extends Cubit<ReportsState> {
  final ReportsRepo _reportsRepo;
  ReportsCubit(this._reportsRepo) : super(ReportsState.initial());

  final operations = ReportsOperations();

  Future<void> getReports({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(ReportsState.getReportsLoading());
      operations.reportsList.clear();
      operations.currentPage = 1;
    }
    if (operations.currentPage > operations.totalPages) return;
    operations.skip = (operations.currentPage - 1) * 2;
    operations.dataWrapper.take = 2;
    operations.dataWrapper.skip = operations.skip;
    final body = GetReportsParams.fromJson(operations.dataWrapper.toJson());
    final result = await _reportsRepo.getReports(body);
    result.when(
        success: (result) => {
              operations.reportsList.addAll(result.data?.report ?? []),
              log('Reports: ${operations.reportsList.length}'),
              operations.totalPages = result.pages?.totalPages ?? 1,
              operations.currentPage++,
              operations.report = result.data,
              emit(ReportsState.getReportsSuccess()),
            },
        failure: (message) => emit(ReportsState.getReportsError(message)));
  }
}
