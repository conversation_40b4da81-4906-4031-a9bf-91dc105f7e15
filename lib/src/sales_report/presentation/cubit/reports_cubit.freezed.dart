// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reports_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReportsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getReportsLoading,
    required TResult Function() getReportsSuccess,
    required TResult Function(String msg) getReportsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getReportsLoading,
    TResult? Function()? getReportsSuccess,
    TResult? Function(String msg)? getReportsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getReportsLoading,
    TResult Function()? getReportsSuccess,
    TResult Function(String msg)? getReportsError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetReportsLoading value) getReportsLoading,
    required TResult Function(_GetReportsSuccess value) getReportsSuccess,
    required TResult Function(_GetReportsError value) getReportsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetReportsLoading value)? getReportsLoading,
    TResult? Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult? Function(_GetReportsError value)? getReportsError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetReportsLoading value)? getReportsLoading,
    TResult Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult Function(_GetReportsError value)? getReportsError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportsStateCopyWith<$Res> {
  factory $ReportsStateCopyWith(
          ReportsState value, $Res Function(ReportsState) then) =
      _$ReportsStateCopyWithImpl<$Res, ReportsState>;
}

/// @nodoc
class _$ReportsStateCopyWithImpl<$Res, $Val extends ReportsState>
    implements $ReportsStateCopyWith<$Res> {
  _$ReportsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ReportsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  _$InitialImpl();

  @override
  String toString() {
    return 'ReportsState.initial()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getReportsLoading,
    required TResult Function() getReportsSuccess,
    required TResult Function(String msg) getReportsError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getReportsLoading,
    TResult? Function()? getReportsSuccess,
    TResult? Function(String msg)? getReportsError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getReportsLoading,
    TResult Function()? getReportsSuccess,
    TResult Function(String msg)? getReportsError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetReportsLoading value) getReportsLoading,
    required TResult Function(_GetReportsSuccess value) getReportsSuccess,
    required TResult Function(_GetReportsError value) getReportsError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetReportsLoading value)? getReportsLoading,
    TResult? Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult? Function(_GetReportsError value)? getReportsError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetReportsLoading value)? getReportsLoading,
    TResult Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult Function(_GetReportsError value)? getReportsError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ReportsState {
  factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$GetReportsLoadingImplCopyWith<$Res> {
  factory _$$GetReportsLoadingImplCopyWith(_$GetReportsLoadingImpl value,
          $Res Function(_$GetReportsLoadingImpl) then) =
      __$$GetReportsLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetReportsLoadingImplCopyWithImpl<$Res>
    extends _$ReportsStateCopyWithImpl<$Res, _$GetReportsLoadingImpl>
    implements _$$GetReportsLoadingImplCopyWith<$Res> {
  __$$GetReportsLoadingImplCopyWithImpl(_$GetReportsLoadingImpl _value,
      $Res Function(_$GetReportsLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetReportsLoadingImpl implements _GetReportsLoading {
  _$GetReportsLoadingImpl();

  @override
  String toString() {
    return 'ReportsState.getReportsLoading()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getReportsLoading,
    required TResult Function() getReportsSuccess,
    required TResult Function(String msg) getReportsError,
  }) {
    return getReportsLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getReportsLoading,
    TResult? Function()? getReportsSuccess,
    TResult? Function(String msg)? getReportsError,
  }) {
    return getReportsLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getReportsLoading,
    TResult Function()? getReportsSuccess,
    TResult Function(String msg)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsLoading != null) {
      return getReportsLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetReportsLoading value) getReportsLoading,
    required TResult Function(_GetReportsSuccess value) getReportsSuccess,
    required TResult Function(_GetReportsError value) getReportsError,
  }) {
    return getReportsLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetReportsLoading value)? getReportsLoading,
    TResult? Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult? Function(_GetReportsError value)? getReportsError,
  }) {
    return getReportsLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetReportsLoading value)? getReportsLoading,
    TResult Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult Function(_GetReportsError value)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsLoading != null) {
      return getReportsLoading(this);
    }
    return orElse();
  }
}

abstract class _GetReportsLoading implements ReportsState {
  factory _GetReportsLoading() = _$GetReportsLoadingImpl;
}

/// @nodoc
abstract class _$$GetReportsSuccessImplCopyWith<$Res> {
  factory _$$GetReportsSuccessImplCopyWith(_$GetReportsSuccessImpl value,
          $Res Function(_$GetReportsSuccessImpl) then) =
      __$$GetReportsSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetReportsSuccessImplCopyWithImpl<$Res>
    extends _$ReportsStateCopyWithImpl<$Res, _$GetReportsSuccessImpl>
    implements _$$GetReportsSuccessImplCopyWith<$Res> {
  __$$GetReportsSuccessImplCopyWithImpl(_$GetReportsSuccessImpl _value,
      $Res Function(_$GetReportsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetReportsSuccessImpl implements _GetReportsSuccess {
  _$GetReportsSuccessImpl();

  @override
  String toString() {
    return 'ReportsState.getReportsSuccess()';
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getReportsLoading,
    required TResult Function() getReportsSuccess,
    required TResult Function(String msg) getReportsError,
  }) {
    return getReportsSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getReportsLoading,
    TResult? Function()? getReportsSuccess,
    TResult? Function(String msg)? getReportsError,
  }) {
    return getReportsSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getReportsLoading,
    TResult Function()? getReportsSuccess,
    TResult Function(String msg)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsSuccess != null) {
      return getReportsSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetReportsLoading value) getReportsLoading,
    required TResult Function(_GetReportsSuccess value) getReportsSuccess,
    required TResult Function(_GetReportsError value) getReportsError,
  }) {
    return getReportsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetReportsLoading value)? getReportsLoading,
    TResult? Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult? Function(_GetReportsError value)? getReportsError,
  }) {
    return getReportsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetReportsLoading value)? getReportsLoading,
    TResult Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult Function(_GetReportsError value)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsSuccess != null) {
      return getReportsSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetReportsSuccess implements ReportsState {
  factory _GetReportsSuccess() = _$GetReportsSuccessImpl;
}

/// @nodoc
abstract class _$$GetReportsErrorImplCopyWith<$Res> {
  factory _$$GetReportsErrorImplCopyWith(_$GetReportsErrorImpl value,
          $Res Function(_$GetReportsErrorImpl) then) =
      __$$GetReportsErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String msg});
}

/// @nodoc
class __$$GetReportsErrorImplCopyWithImpl<$Res>
    extends _$ReportsStateCopyWithImpl<$Res, _$GetReportsErrorImpl>
    implements _$$GetReportsErrorImplCopyWith<$Res> {
  __$$GetReportsErrorImplCopyWithImpl(
      _$GetReportsErrorImpl _value, $Res Function(_$GetReportsErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? msg = null,
  }) {
    return _then(_$GetReportsErrorImpl(
      null == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetReportsErrorImpl implements _GetReportsError {
  _$GetReportsErrorImpl(this.msg);

  @override
  String msg;

  @override
  String toString() {
    return 'ReportsState.getReportsError(msg: $msg)';
  }

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetReportsErrorImplCopyWith<_$GetReportsErrorImpl> get copyWith =>
      __$$GetReportsErrorImplCopyWithImpl<_$GetReportsErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() getReportsLoading,
    required TResult Function() getReportsSuccess,
    required TResult Function(String msg) getReportsError,
  }) {
    return getReportsError(msg);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? getReportsLoading,
    TResult? Function()? getReportsSuccess,
    TResult? Function(String msg)? getReportsError,
  }) {
    return getReportsError?.call(msg);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? getReportsLoading,
    TResult Function()? getReportsSuccess,
    TResult Function(String msg)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsError != null) {
      return getReportsError(msg);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_GetReportsLoading value) getReportsLoading,
    required TResult Function(_GetReportsSuccess value) getReportsSuccess,
    required TResult Function(_GetReportsError value) getReportsError,
  }) {
    return getReportsError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_GetReportsLoading value)? getReportsLoading,
    TResult? Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult? Function(_GetReportsError value)? getReportsError,
  }) {
    return getReportsError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_GetReportsLoading value)? getReportsLoading,
    TResult Function(_GetReportsSuccess value)? getReportsSuccess,
    TResult Function(_GetReportsError value)? getReportsError,
    required TResult orElse(),
  }) {
    if (getReportsError != null) {
      return getReportsError(this);
    }
    return orElse();
  }
}

abstract class _GetReportsError implements ReportsState {
  factory _GetReportsError(String msg) = _$GetReportsErrorImpl;

  String get msg;
  set msg(String value);

  /// Create a copy of ReportsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetReportsErrorImplCopyWith<_$GetReportsErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
