import 'package:alsarea_store/core/helpers/extensions.dart';
import 'package:alsarea_store/core/theme/app_colors.dart';
import 'package:alsarea_store/core/theme/text_styles.dart';
import 'package:alsarea_store/gen/assets.gen.dart';
import 'package:alsarea_store/src/sales_report/data/models/report_model.dart';
import 'package:flutter/material.dart';

class SaleReportCard extends StatelessWidget {
  const SaleReportCard({super.key, required this.report});
  final ReportItemModel report;
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  report.createdAt ?? '',
                  style: TextStyles.body16,
                ),
                // Container(
                //   decoration: BoxDecoration(
                //     color: AppColors.primary,
                //     borderRadius: BorderRadius.circular(4),
                //   ),
                //   padding: const EdgeInsets.symmetric(horizontal: 8),
                //   child: Text(
                //     context.l10n.specialOrder,
                //     style: TextStyles.label11.copyWith(
                //       fontWeight: FontWeight.w700,
                //       color: Colors.white,
                //     ),
                //   ),
                // ),
                Text(
                  '# ${report.id}',
                  style: TextStyles.body16,
                ),
              ],
            ),
          ),
          const Divider(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    spacing: 8,
                    children: [
                      Assets.icons.person.svg(
                        width: 16,
                        height: 16,
                        colorFilter: const ColorFilter.mode(
                          AppColors.primary,
                          BlendMode.srcIn,
                        ),
                      ),
                      Expanded(
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          alignment: AlignmentDirectional.centerStart,
                          child: Text(
                            report.customerName ?? '',
                            style: TextStyles.body16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Row(
                    spacing: 8,
                    children: [
                      Assets.icons.location.svg(
                        width: 16,
                        height: 16,
                        colorFilter: const ColorFilter.mode(
                          AppColors.primary,
                          BlendMode.srcIn,
                        ),
                      ),
                      Expanded(
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          alignment: AlignmentDirectional.centerStart,
                          child: Text(
                            report.city ?? '',
                            style: TextStyles.body16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.l10n.totalOrder,
                  style: TextStyles.body16,
                ),
                Text(
                  '${report.total} ${context.l10n.egp}',
                  style: TextStyles.body16.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.green,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
