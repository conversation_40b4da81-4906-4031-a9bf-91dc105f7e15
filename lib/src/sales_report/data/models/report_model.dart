// To parse this JSON data, do
//
//     final reportModel = reportModelFromJson(jsonString);

import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'report_model.freezed.dart';
part 'report_model.g.dart';

ReportModel reportModelFromJson(String str) =>
    ReportModel.fromJson(json.decode(str));

String reportModelToJson(ReportModel data) => json.encode(data.toJson());

@freezed
class ReportModel with _$ReportModel {
  const factory ReportModel({
    @JsonKey(name: "totalOrdersCount") int? totalOrdersCount,
    @JsonKey(name: "totalSales") int? totalSales,
    @Json<PERSON>ey(name: "totalCompanyCommission") int? totalCompanyCommission,
    @JsonKey(name: "orders") List<ReportItemModel>? report,
  }) = _ReportModel;

  factory ReportModel.fromJson(Map<String, dynamic> json) =>
      _$ReportModelFromJson(json);
}

@freezed
class ReportItemModel with _$ReportItemModel {
  const factory ReportItemModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "createdAt") String? createdAt,
    @JsonKey(name: "customerName") String? customerName,
    @JsonKey(name: "city") String? city,
    @JsonKey(name: "total") int? total,
    @JsonKey(name: "subTotal") int? subTotal,
  }) = _ReportItemModel;

  factory ReportItemModel.fromJson(Map<String, dynamic> json) =>
      _$ReportItemModelFromJson(json);
}
