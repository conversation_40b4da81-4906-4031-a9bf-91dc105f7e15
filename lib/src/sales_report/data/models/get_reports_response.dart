import 'package:alsarea_store/core/api/base_response.dart';
import 'package:alsarea_store/src/sales_report/data/models/report_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_reports_response.g.dart';

@JsonSerializable()
class GetReportsResponse extends BaseResponse {
  final ReportModel? data;

  GetReportsResponse(
      {this.data,
      required super.errors,
      required super.message,
      required super.status,
      required super.statusName,
      required super.debug,
      required super.pages});
  factory GetReportsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetReportsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetReportsResponseToJson(this);
}
