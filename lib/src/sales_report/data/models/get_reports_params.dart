import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_reports_params.freezed.dart';
part 'get_reports_params.g.dart';

GetReportsParams getReportsParamsFromJson(String str) =>
    GetReportsParams.fromJson(json.decode(str));

String getReportsParamsToJson(GetReportsParams data) =>
    json.encode(data.toJson());

@freezed
class GetReportsParams with _$GetReportsParams {
  const factory GetReportsParams({
    @JsonKey(name: "take") int? take,
    @J<PERSON><PERSON><PERSON>(name: "skip") int? skip,
  }) = _GetReportsParams;

  factory GetReportsParams.fromJson(Map<String, dynamic> json) =>
      _$GetReportsParamsFromJson(json);
}
