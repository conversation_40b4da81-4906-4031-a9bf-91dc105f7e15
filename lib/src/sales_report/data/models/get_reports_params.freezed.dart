// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_reports_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetReportsParams _$GetReportsParamsFromJson(Map<String, dynamic> json) {
  return _GetReportsParams.fromJson(json);
}

/// @nodoc
mixin _$GetReportsParams {
  @JsonKey(name: "take")
  int? get take => throw _privateConstructorUsedError;
  @JsonKey(name: "skip")
  int? get skip => throw _privateConstructorUsedError;

  /// Serializes this GetReportsParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetReportsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetReportsParamsCopyWith<GetReportsParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetReportsParamsCopyWith<$Res> {
  factory $GetReportsParamsCopyWith(
          GetReportsParams value, $Res Function(GetReportsParams) then) =
      _$GetReportsParamsCopyWithImpl<$Res, GetReportsParams>;
  @useResult
  $Res call(
      {@JsonKey(name: "take") int? take, @JsonKey(name: "skip") int? skip});
}

/// @nodoc
class _$GetReportsParamsCopyWithImpl<$Res, $Val extends GetReportsParams>
    implements $GetReportsParamsCopyWith<$Res> {
  _$GetReportsParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetReportsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? take = freezed,
    Object? skip = freezed,
  }) {
    return _then(_value.copyWith(
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetReportsParamsImplCopyWith<$Res>
    implements $GetReportsParamsCopyWith<$Res> {
  factory _$$GetReportsParamsImplCopyWith(_$GetReportsParamsImpl value,
          $Res Function(_$GetReportsParamsImpl) then) =
      __$$GetReportsParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "take") int? take, @JsonKey(name: "skip") int? skip});
}

/// @nodoc
class __$$GetReportsParamsImplCopyWithImpl<$Res>
    extends _$GetReportsParamsCopyWithImpl<$Res, _$GetReportsParamsImpl>
    implements _$$GetReportsParamsImplCopyWith<$Res> {
  __$$GetReportsParamsImplCopyWithImpl(_$GetReportsParamsImpl _value,
      $Res Function(_$GetReportsParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetReportsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? take = freezed,
    Object? skip = freezed,
  }) {
    return _then(_$GetReportsParamsImpl(
      take: freezed == take
          ? _value.take
          : take // ignore: cast_nullable_to_non_nullable
              as int?,
      skip: freezed == skip
          ? _value.skip
          : skip // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetReportsParamsImpl implements _GetReportsParams {
  const _$GetReportsParamsImpl(
      {@JsonKey(name: "take") this.take, @JsonKey(name: "skip") this.skip});

  factory _$GetReportsParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetReportsParamsImplFromJson(json);

  @override
  @JsonKey(name: "take")
  final int? take;
  @override
  @JsonKey(name: "skip")
  final int? skip;

  @override
  String toString() {
    return 'GetReportsParams(take: $take, skip: $skip)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetReportsParamsImpl &&
            (identical(other.take, take) || other.take == take) &&
            (identical(other.skip, skip) || other.skip == skip));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, take, skip);

  /// Create a copy of GetReportsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetReportsParamsImplCopyWith<_$GetReportsParamsImpl> get copyWith =>
      __$$GetReportsParamsImplCopyWithImpl<_$GetReportsParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetReportsParamsImplToJson(
      this,
    );
  }
}

abstract class _GetReportsParams implements GetReportsParams {
  const factory _GetReportsParams(
      {@JsonKey(name: "take") final int? take,
      @JsonKey(name: "skip") final int? skip}) = _$GetReportsParamsImpl;

  factory _GetReportsParams.fromJson(Map<String, dynamic> json) =
      _$GetReportsParamsImpl.fromJson;

  @override
  @JsonKey(name: "take")
  int? get take;
  @override
  @JsonKey(name: "skip")
  int? get skip;

  /// Create a copy of GetReportsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetReportsParamsImplCopyWith<_$GetReportsParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
