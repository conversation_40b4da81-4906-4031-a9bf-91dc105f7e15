// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReportModelImpl _$$ReportModelImplFromJson(Map<String, dynamic> json) =>
    _$ReportModelImpl(
      totalOrdersCount: (json['totalOrdersCount'] as num?)?.toInt(),
      totalSales: (json['totalSales'] as num?)?.toInt(),
      totalCompanyCommission: (json['totalCompanyCommission'] as num?)?.toInt(),
      report: (json['orders'] as List<dynamic>?)
          ?.map((e) => ReportItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ReportModelImplToJson(_$ReportModelImpl instance) =>
    <String, dynamic>{
      if (instance.totalOrdersCount case final value?)
        'totalOrdersCount': value,
      if (instance.totalSales case final value?) 'totalSales': value,
      if (instance.totalCompanyCommission case final value?)
        'totalCompanyCommission': value,
      if (instance.report case final value?) 'orders': value,
    };

_$ReportItemModelImpl _$$ReportItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ReportItemModelImpl(
      id: (json['id'] as num?)?.toInt(),
      createdAt: json['createdAt'] as String?,
      customerName: json['customerName'] as String?,
      city: json['city'] as String?,
      total: (json['total'] as num?)?.toInt(),
      subTotal: (json['subTotal'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ReportItemModelImplToJson(
        _$ReportItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.customerName case final value?) 'customerName': value,
      if (instance.city case final value?) 'city': value,
      if (instance.total case final value?) 'total': value,
      if (instance.subTotal case final value?) 'subTotal': value,
    };
