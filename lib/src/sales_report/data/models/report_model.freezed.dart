// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'report_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReportModel _$ReportModelFromJson(Map<String, dynamic> json) {
  return _ReportModel.fromJson(json);
}

/// @nodoc
mixin _$ReportModel {
  @JsonKey(name: "totalOrdersCount")
  int? get totalOrdersCount => throw _privateConstructorUsedError;
  @JsonKey(name: "totalSales")
  int? get totalSales => throw _privateConstructorUsedError;
  @JsonKey(name: "totalCompanyCommission")
  int? get totalCompanyCommission => throw _privateConstructorUsedError;
  @JsonKey(name: "orders")
  List<ReportItemModel>? get report => throw _privateConstructorUsedError;

  /// Serializes this ReportModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportModelCopyWith<ReportModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportModelCopyWith<$Res> {
  factory $ReportModelCopyWith(
          ReportModel value, $Res Function(ReportModel) then) =
      _$ReportModelCopyWithImpl<$Res, ReportModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "totalOrdersCount") int? totalOrdersCount,
      @JsonKey(name: "totalSales") int? totalSales,
      @JsonKey(name: "totalCompanyCommission") int? totalCompanyCommission,
      @JsonKey(name: "orders") List<ReportItemModel>? report});
}

/// @nodoc
class _$ReportModelCopyWithImpl<$Res, $Val extends ReportModel>
    implements $ReportModelCopyWith<$Res> {
  _$ReportModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalOrdersCount = freezed,
    Object? totalSales = freezed,
    Object? totalCompanyCommission = freezed,
    Object? report = freezed,
  }) {
    return _then(_value.copyWith(
      totalOrdersCount: freezed == totalOrdersCount
          ? _value.totalOrdersCount
          : totalOrdersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSales: freezed == totalSales
          ? _value.totalSales
          : totalSales // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCompanyCommission: freezed == totalCompanyCommission
          ? _value.totalCompanyCommission
          : totalCompanyCommission // ignore: cast_nullable_to_non_nullable
              as int?,
      report: freezed == report
          ? _value.report
          : report // ignore: cast_nullable_to_non_nullable
              as List<ReportItemModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReportModelImplCopyWith<$Res>
    implements $ReportModelCopyWith<$Res> {
  factory _$$ReportModelImplCopyWith(
          _$ReportModelImpl value, $Res Function(_$ReportModelImpl) then) =
      __$$ReportModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "totalOrdersCount") int? totalOrdersCount,
      @JsonKey(name: "totalSales") int? totalSales,
      @JsonKey(name: "totalCompanyCommission") int? totalCompanyCommission,
      @JsonKey(name: "orders") List<ReportItemModel>? report});
}

/// @nodoc
class __$$ReportModelImplCopyWithImpl<$Res>
    extends _$ReportModelCopyWithImpl<$Res, _$ReportModelImpl>
    implements _$$ReportModelImplCopyWith<$Res> {
  __$$ReportModelImplCopyWithImpl(
      _$ReportModelImpl _value, $Res Function(_$ReportModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalOrdersCount = freezed,
    Object? totalSales = freezed,
    Object? totalCompanyCommission = freezed,
    Object? report = freezed,
  }) {
    return _then(_$ReportModelImpl(
      totalOrdersCount: freezed == totalOrdersCount
          ? _value.totalOrdersCount
          : totalOrdersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalSales: freezed == totalSales
          ? _value.totalSales
          : totalSales // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCompanyCommission: freezed == totalCompanyCommission
          ? _value.totalCompanyCommission
          : totalCompanyCommission // ignore: cast_nullable_to_non_nullable
              as int?,
      report: freezed == report
          ? _value._report
          : report // ignore: cast_nullable_to_non_nullable
              as List<ReportItemModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReportModelImpl implements _ReportModel {
  const _$ReportModelImpl(
      {@JsonKey(name: "totalOrdersCount") this.totalOrdersCount,
      @JsonKey(name: "totalSales") this.totalSales,
      @JsonKey(name: "totalCompanyCommission") this.totalCompanyCommission,
      @JsonKey(name: "orders") final List<ReportItemModel>? report})
      : _report = report;

  factory _$ReportModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReportModelImplFromJson(json);

  @override
  @JsonKey(name: "totalOrdersCount")
  final int? totalOrdersCount;
  @override
  @JsonKey(name: "totalSales")
  final int? totalSales;
  @override
  @JsonKey(name: "totalCompanyCommission")
  final int? totalCompanyCommission;
  final List<ReportItemModel>? _report;
  @override
  @JsonKey(name: "orders")
  List<ReportItemModel>? get report {
    final value = _report;
    if (value == null) return null;
    if (_report is EqualUnmodifiableListView) return _report;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ReportModel(totalOrdersCount: $totalOrdersCount, totalSales: $totalSales, totalCompanyCommission: $totalCompanyCommission, report: $report)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReportModelImpl &&
            (identical(other.totalOrdersCount, totalOrdersCount) ||
                other.totalOrdersCount == totalOrdersCount) &&
            (identical(other.totalSales, totalSales) ||
                other.totalSales == totalSales) &&
            (identical(other.totalCompanyCommission, totalCompanyCommission) ||
                other.totalCompanyCommission == totalCompanyCommission) &&
            const DeepCollectionEquality().equals(other._report, _report));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalOrdersCount, totalSales,
      totalCompanyCommission, const DeepCollectionEquality().hash(_report));

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReportModelImplCopyWith<_$ReportModelImpl> get copyWith =>
      __$$ReportModelImplCopyWithImpl<_$ReportModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReportModelImplToJson(
      this,
    );
  }
}

abstract class _ReportModel implements ReportModel {
  const factory _ReportModel(
          {@JsonKey(name: "totalOrdersCount") final int? totalOrdersCount,
          @JsonKey(name: "totalSales") final int? totalSales,
          @JsonKey(name: "totalCompanyCommission")
          final int? totalCompanyCommission,
          @JsonKey(name: "orders") final List<ReportItemModel>? report}) =
      _$ReportModelImpl;

  factory _ReportModel.fromJson(Map<String, dynamic> json) =
      _$ReportModelImpl.fromJson;

  @override
  @JsonKey(name: "totalOrdersCount")
  int? get totalOrdersCount;
  @override
  @JsonKey(name: "totalSales")
  int? get totalSales;
  @override
  @JsonKey(name: "totalCompanyCommission")
  int? get totalCompanyCommission;
  @override
  @JsonKey(name: "orders")
  List<ReportItemModel>? get report;

  /// Create a copy of ReportModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReportModelImplCopyWith<_$ReportModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReportItemModel _$ReportItemModelFromJson(Map<String, dynamic> json) {
  return _ReportItemModel.fromJson(json);
}

/// @nodoc
mixin _$ReportItemModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "createdAt")
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "customerName")
  String? get customerName => throw _privateConstructorUsedError;
  @JsonKey(name: "city")
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  int? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "subTotal")
  int? get subTotal => throw _privateConstructorUsedError;

  /// Serializes this ReportItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReportItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReportItemModelCopyWith<ReportItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReportItemModelCopyWith<$Res> {
  factory $ReportItemModelCopyWith(
          ReportItemModel value, $Res Function(ReportItemModel) then) =
      _$ReportItemModelCopyWithImpl<$Res, ReportItemModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "createdAt") String? createdAt,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "city") String? city,
      @JsonKey(name: "total") int? total,
      @JsonKey(name: "subTotal") int? subTotal});
}

/// @nodoc
class _$ReportItemModelCopyWithImpl<$Res, $Val extends ReportItemModel>
    implements $ReportItemModelCopyWith<$Res> {
  _$ReportItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReportItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? customerName = freezed,
    Object? city = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReportItemModelImplCopyWith<$Res>
    implements $ReportItemModelCopyWith<$Res> {
  factory _$$ReportItemModelImplCopyWith(_$ReportItemModelImpl value,
          $Res Function(_$ReportItemModelImpl) then) =
      __$$ReportItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "createdAt") String? createdAt,
      @JsonKey(name: "customerName") String? customerName,
      @JsonKey(name: "city") String? city,
      @JsonKey(name: "total") int? total,
      @JsonKey(name: "subTotal") int? subTotal});
}

/// @nodoc
class __$$ReportItemModelImplCopyWithImpl<$Res>
    extends _$ReportItemModelCopyWithImpl<$Res, _$ReportItemModelImpl>
    implements _$$ReportItemModelImplCopyWith<$Res> {
  __$$ReportItemModelImplCopyWithImpl(
      _$ReportItemModelImpl _value, $Res Function(_$ReportItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReportItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? customerName = freezed,
    Object? city = freezed,
    Object? total = freezed,
    Object? subTotal = freezed,
  }) {
    return _then(_$ReportItemModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      subTotal: freezed == subTotal
          ? _value.subTotal
          : subTotal // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReportItemModelImpl implements _ReportItemModel {
  const _$ReportItemModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "createdAt") this.createdAt,
      @JsonKey(name: "customerName") this.customerName,
      @JsonKey(name: "city") this.city,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "subTotal") this.subTotal});

  factory _$ReportItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReportItemModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "createdAt")
  final String? createdAt;
  @override
  @JsonKey(name: "customerName")
  final String? customerName;
  @override
  @JsonKey(name: "city")
  final String? city;
  @override
  @JsonKey(name: "total")
  final int? total;
  @override
  @JsonKey(name: "subTotal")
  final int? subTotal;

  @override
  String toString() {
    return 'ReportItemModel(id: $id, createdAt: $createdAt, customerName: $customerName, city: $city, total: $total, subTotal: $subTotal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReportItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.subTotal, subTotal) ||
                other.subTotal == subTotal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, createdAt, customerName, city, total, subTotal);

  /// Create a copy of ReportItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReportItemModelImplCopyWith<_$ReportItemModelImpl> get copyWith =>
      __$$ReportItemModelImplCopyWithImpl<_$ReportItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReportItemModelImplToJson(
      this,
    );
  }
}

abstract class _ReportItemModel implements ReportItemModel {
  const factory _ReportItemModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "createdAt") final String? createdAt,
      @JsonKey(name: "customerName") final String? customerName,
      @JsonKey(name: "city") final String? city,
      @JsonKey(name: "total") final int? total,
      @JsonKey(name: "subTotal") final int? subTotal}) = _$ReportItemModelImpl;

  factory _ReportItemModel.fromJson(Map<String, dynamic> json) =
      _$ReportItemModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "createdAt")
  String? get createdAt;
  @override
  @JsonKey(name: "customerName")
  String? get customerName;
  @override
  @JsonKey(name: "city")
  String? get city;
  @override
  @JsonKey(name: "total")
  int? get total;
  @override
  @JsonKey(name: "subTotal")
  int? get subTotal;

  /// Create a copy of ReportItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReportItemModelImplCopyWith<_$ReportItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
