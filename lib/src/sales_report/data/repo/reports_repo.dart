import 'package:alsarea_store/core/error/error_handler.dart';
import 'package:alsarea_store/core/helpers/result.dart';
import 'package:alsarea_store/src/sales_report/data/api_service/reports_api_service.dart';
import 'package:alsarea_store/src/sales_report/data/models/get_reports_params.dart';
import 'package:alsarea_store/src/sales_report/data/models/get_reports_response.dart';

class ReportsRepo {
  final ReportsApiService _reportsApiService;
  ReportsRepo(this._reportsApiService);
  Future<Result<GetReportsResponse>> getReports(GetReportsParams params) =>
      errorHandlerAsync(() => _reportsApiService.getReports(params));
}
