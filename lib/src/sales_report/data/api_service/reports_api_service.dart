import 'package:alsarea_store/core/api/end_points.dart';
import 'package:alsarea_store/src/sales_report/data/models/get_reports_params.dart';
import 'package:alsarea_store/src/sales_report/data/models/get_reports_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'reports_api_service.g.dart';

@RestApi()
abstract class ReportsApiService {
  factory ReportsApiService(
    Dio dio, {
    String baseUrl,
  }) = _ReportsApiService;

  @GET(EndPoints.getReports)
  Future<GetReportsResponse> getReports(@Queries() GetReportsParams params);
}
