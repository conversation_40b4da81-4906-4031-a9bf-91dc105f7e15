// To parse this JSON data, do
//
//     final reportsDataWrapper = reportsDataWrapperFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'reports_data_wrapper.g.dart';

ReportsDataWrapper reportsDataWrapperFromJson(String str) =>
    ReportsDataWrapper.fromJson(json.decode(str));

String reportsDataWrapperToJson(ReportsDataWrapper data) =>
    json.encode(data.toJson());

@JsonSerializable()
class ReportsDataWrapper {
  @JsonKey(name: "take")
  int? take;
  @J<PERSON><PERSON><PERSON>(name: "skip")
  int? skip;

  ReportsDataWrapper({
    this.take = 2,
    this.skip,
  });

  factory ReportsDataWrapper.fromJson(Map<String, dynamic> json) =>
      _$ReportsDataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$ReportsDataWrapperToJson(this);
}
