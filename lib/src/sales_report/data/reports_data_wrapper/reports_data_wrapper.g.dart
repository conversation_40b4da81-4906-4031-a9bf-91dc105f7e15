// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reports_data_wrapper.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportsDataWrapper _$ReportsDataWrapperFromJson(Map<String, dynamic> json) =>
    ReportsDataWrapper(
      take: (json['take'] as num?)?.toInt() ?? 2,
      skip: (json['skip'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ReportsDataWrapperToJson(ReportsDataWrapper instance) =>
    <String, dynamic>{
      if (instance.take case final value?) 'take': value,
      if (instance.skip case final value?) 'skip': value,
    };
